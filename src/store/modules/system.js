import { getSystem } from '@/service/system'
import store2 from 'store2'

const state = {
  systemInfo: {
    brand: '', // product
    model: '',
    vn: '',
    system: '',
    resolution: '',
    orientation: '',
    mac: '',
    src: '',
    extra: {},
    hasThunderMic: false
  },
  audioEffects: {
    play_type: '',
    speaker_num: '',
    mic_type: '',
  },
  car_name: '',
}

const getters = {}

const actions = {
  getSystemInfo({ commit }) {
    try {
      console.info('getSystemInfo 00000')
      const res = getSystem()
      console.info('getSystemInfo', res)
      store2('mac_address', res.mac)
      commit('SAVE_SYSTEM_INFO', res)
    } catch (error) {
      console.log('getSystemInfo', error)
    }
  },
  getCarName({ commit }, data) {
    commit('SAVE_CAR_NAME', data)
  }
}

const mutations = {
  SAVE_SYSTEM_INFO(state, systemInfo) {
    state.systemInfo = systemInfo;
  },
  SAVE_AUDIO_EFFECTS(state, data) {
    state.audioEffects = data
    console.log(data)
  },
  SAVE_CAR_NAME(state, data) {
    state.car_name = data
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
}
