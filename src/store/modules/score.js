import store2 from 'store2'

const state = {
  enabled: !!store2.get('isEnabledScore'),
  scoreSongInfo: {
    songId: '',
    musicName: '',
    scoreSourceUrl: '',
    isScore: 0,
  },
  result: {
    name: '',
    score: 0,
  },
  scoreData: {
    gradeFragmentList: [],
    fistFrontStartTime: null,
    duration: 0,
    currentSong: {}
  },
  scoreSongInfo1: {},
}

const getters = {}

const actions = {
  updateCurSongIsSupport({ commit }, payload) {
    commit('SET_SCORE_SONG_INFO', payload)
  },
  updateScoreInfo({ commit }, payload) {
    commit('SET_SCORE_SONG_INFO_1', payload)
  },
  open({ commit }) {
    commit('SET_SCORE_ENABLED', true);
  },
  close({ commit }) {
    commit('SET_SCORE_ENABLED', false)
  },
}

const mutations = {
  SET_SCORE_ENABLED(state, data) {
    state.enabled = data 
  },
  SET_SCORE_SONG_INFO(state, data) {
    state.scoreSongInfo = data
  },
  SET_SCORE_SONG_INFO_1(state, data) {
    state.scoreSongInfo1 = data
  },
  SET_SCORE_RESULT(state, data) {
    state.result = data
  },
  RESET_SCORE_RESULT(state) {
    state.result = {
      name: '',
      score: 0
    }
  },
  SET_SCORE_DATA(state, payload) {
    state.scoreData.gradeFragmentList = payload.gradeFragmentList;
    state.scoreData.fistFrontStartTime = payload.fistFrontStartTime;
    state.scoreData.duration = payload.duration;
    state.scoreData.currentSong = payload.currentSong;
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
}
