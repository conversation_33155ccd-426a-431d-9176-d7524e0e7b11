import { DownloadStatus } from '@/utils/download'
import { TSDownloaderInstance } from '@/packages/TSJsbridge/TSDownloader'
import { TSFileInstance } from '@/packages/TSJsbridge/TSFile'
import { TSConfigInstance } from '@/packages/TSJsbridge/TSConfig'

const state = {
  currentTask: {
    progress: 0,
    songid: 0,
    state: 1,
    music_name: '',
    tmpFilePath: '',
  },
  downloadDir: ''
}

const getters = {}


const downloadDirPromise = () => {
  return new Promise((resolve) => {
    const downloadDir = TSConfigInstance.getDownloadDir();
    resolve(downloadDir);
  });
}

async function getIsLocalSong({ commit }, downloadDir, song) {
  try {
    if (!song) return
    
    let src = song?.src
    if (!downloadDir) {
      const downloadDir = await downloadDirPromise()
      await commit('UPDATE_DOWNLOAD_DIR', downloadDir)
      src = `${downloadDir}/${song?.songid}.ts`
    }
    if (!src) {
      src = `${downloadDir}/${song?.songid}.ts`
    }
    const res = TSFileInstance.getIsExist(src)
    if (res) {
      // 更新一下
      commit('UPDATE_SONG_DOWNLOAD_STATE', {
        songid: song?.songid, downloadState: DownloadStatus.SUCCESS, downloadProgress: 100, src
      }, { root: true })
    } else {
      // 更新一下
      commit('UPDATE_SONG_DOWNLOAD_STATE', {
        songid: song?.songid,
        downloadState: song.downloadState || DownloadStatus.WAIT,
        downloadProgress: song.downloadProgress || 0,
        src: ''
      }, { root: true })
    }
    return res
    
  } catch (error) {
    console.log('getIsLocalSong', error)
  }
}

const actions = {
  getIsLocalSong({ state, commit }, payload) {
    return getIsLocalSong({ commit }, state.downloadDir, payload)
  },
  startDownload({ state, commit }, song) {
    console.log('startDownload', song)
    if (state.currentTask?.songid) {
      console.log('startDownload', '当前有任务正在进行', state.currentTask.music_name)
      return
    }
    if (!song?.songid) {
      console.log('startDownload', '无songid', '暂停下载')
      return
    }
    commit('UPDATE_CURRENT_TASK', {
      songid: song?.songid,
      progress: 0,
      state: DownloadStatus.WAIT,
      music_name: song.music_name,
      is_vip: song.is_vip,
    })
    TSDownloaderInstance.start(song)
  },
  stopDownload({ state, commit }) {
    TSDownloaderInstance.stop(state.currentTask?.songid)
    commit('RESET_CURRENT_TASK')
  },
  async checkAutoDownload({ state, rootState, dispatch }, isVip = !!rootState.vipInfo?.end_time) {
    if (state.currentTask?.songid) {
      console.log('startDownload', '当前有任务正在进行', state.currentTask.music_name)
      return
    }
  
    const startIndex = 0
    const orderedList = rootState.orderedList.slice(startIndex, startIndex + 10)
  
    for (const item of orderedList) {
      // 剔除 isAIMV 为 true 的歌曲
      if (item.isAIMV === true) {
        continue
      }

      const isLocalSong = item.downloadState === DownloadStatus.SUCCESS

      if (!isLocalSong && item.downloadState !== DownloadStatus.FAILED) {
        if (isVip || (!isVip && !item.is_vip)) {
          console.log('准备下载后续歌曲', item.music_name)
          dispatch('startDownload', item)
          break
        }
      }
    }
  }
}

const mutations = {
  UPDATE_CURRENT_TASK(state, payload) {
    state.currentTask = payload
  },
  UPDATE_CURRENT_TASK_PROGRESS(state, payload) {
    state.currentTask.progress = payload
  },
  UPDATE_CURRENT_TASK_STATE(state, payload) {
    state.currentTask.state = payload
  },
  UPDATE_CURRENT_TASK_TMPFILEPATH(state, payload) {
    state.currentTask.tmpFilePath = payload
  },
  RESET_CURRENT_TASK(state) {
    console.log('重置下载任务')
    state.currentTask = {
      progress: state.currentTask.progress || 0,
      songid: 0,
      state: 1,
      music_name: ''
    }
  },
  UPDATE_DOWNLOAD_DIR(state, payload) {
    state.downloadDir = payload
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
}
