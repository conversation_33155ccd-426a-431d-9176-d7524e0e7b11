import { TSFileInstance } from '@/packages/TSJsbridge/TSFile'
import { TSConfigInstance } from '@/packages/TSJsbridge/TSConfig';

const state = {
  downloadDir: '',
  usableSpace: 0,
  totalSpace: 0,
  limitMin: 30 * 1024 * 1024,
  limitMax: 4 * 1024 * 1024 * 1024,
  // limitMax: 80 * 1024 * 1024,
  fileList: [],
  isSpaceEnough: true,
}

const getters = {
  getSpaceIsEnough(state) {
    return state.isSpaceEnough
  }
}

const actions = {
  deleteLocalSong({ dispatch }, payload) {
    const path = payload.src
    let res = false
    if (path) {
      console.log('删除本地歌曲', payload.music_name)
      res = TSFileInstance.delete(path)
      console.log('清理歌曲', payload.music_name, res ? '成功' : '失败')
      if (res) {
        dispatch('getUsableSpace')
      }
    }
    return res
  },
  getSpaceInfo({ commit }) {
    const usableSpace = TSFileInstance.getUsableSpace() || 0
    const totalSpace = TSFileInstance.getTotalSpace() || 0
    commit('UPDATE_TOTAL_SPACE', totalSpace)
    commit('UPDATE_USABLE_SPACE', usableSpace)
  },
  getUsableSpace({ commit, state }) {
    // 获取downloadDir
    if (!state.downloadDir) {
      commit('UPDATE_DOWNLOAD_DIR', TSConfigInstance.getDownloadDir())
    }

    const res = TSFileInstance.getFiles(state.downloadDir) || [];
    commit('UPDATE_DOWNLOAD_FILE_LIST', res)
    const usedSpace = res.reduce((acc, cur) => {
      acc += Number(cur.fileSize)
      return acc
    }, 0)
    const availableSpace = state.limitMax - usedSpace >= 0 ? state.limitMax - usedSpace : 0
    commit('UPDATE_USABLE_SPACE', availableSpace)
    commit('UPDATE_IS_SPACE_ENOUGH', availableSpace >= state.limitMin)
    // 有多少首歌
    // 占多大
    // max计算
    // 返回
  }
}

const mutations = {
  UPDATE_IS_SPACE_ENOUGH(state, payload) {
    state.isSpaceEnough = payload
  },
  UPDATE_DOWNLOAD_FILE_LIST(state, payload) {
    state.fileList = payload
  },
  UPDATE_DOWNLOAD_DIR(state, payload) {
    state.downloadDir = payload
  },
  UPDATE_TOTAL_SPACE(state, payload) {
    state.totalSpace = payload
  },
  UPDATE_USABLE_SPACE(state, payload) {
    state.usableSpace = payload
  },
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
}
