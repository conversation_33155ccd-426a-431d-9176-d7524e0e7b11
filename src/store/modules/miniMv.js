import { getMiniDefaultList } from '@/service/playlist';
import store2 from 'store2';

const state = {
  defaultList: [],
  defaultSong: {},
  defaultSongIndex: 0,
  muted: true,
};

const getters = {};

const actions = {
  async fetchDefaultList({ commit }) {
    try {
      const { data_list = [] } = await getMiniDefaultList();
      console.log('fetchDefaultList', data_list);
      store2.set('miniMvDefaultList', data_list); // 存储到本地
      commit('SET_DEFAULT_LIST', data_list);
      if (data_list.length > 0) {
        commit('SET_DEFAULT_SONG', {
          song: data_list.value[0],
          index: 0,
        })
      }
    } catch (error) {
      console.error('Failed to fetch default list:', error);
      const localData = store2.get('miniMvDefaultList') || [];
      commit('SET_DEFAULT_LIST', localData);
      if (localData.length > 0) {
        commit('SET_DEFAULT_SONG', {
          song: localData[0],
          index: 0,
        })
      }
    }
  },
  setMuted({ commit }, muted) {
    commit('SET_MUTED', muted);
  },
};

const mutations = {
  SET_DEFAULT_LIST(state, defaultList) {
    state.defaultList = defaultList;
  },
  SET_DEFAULT_SONG(state, { song, index = 0}) {
    state.defaultSong = song;
    state.defaultSongIndex = index;
  },
  SET_MUTED(state, muted) {
    state.muted = muted;
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
