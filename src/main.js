import './styles/index.styl'
import VueLoading from 'vue-loading-overlay'
import 'vue-loading-overlay/dist/vue-loading.css'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import SearchBar from '@/components/search-bar/index.vue'
import LoginConfrimGlobalModal from '@/components/modal/global/login-confirm'
import LoginQrcodeGlobalModal from '@/components/modal/global/login-qrcode'
import VipQrcodeGlobalModal from '@/components/modal/global/vip-qrcode'
import ActivityGlobalModal from '@/components/modal/global/activity-modal'
import MobileOrderQrcodeModal from '@/components/modal/global/mobile-order-qrcode'
import UserCollectModal from '@/components/modal/global/user-collect'
import MicOnlineModal from '@/components/modal/global/mic-online'
import GradeResultModal from '@/components/modal/global/grade-result'
import SplashScreenModal from '@/components/modal/global/splash-screen'
import singNotify from '@/components/notify'
import { Toast } from 'vant'
import HeadBar from '@/components/head-bar/index.vue'
import VantStyle from '@/components/style/vant.vue'
import LoadMore from '@/components/load-more/index.vue'
import SuspenseStatus from '@/components/SuspenseStatus/index.vue'
import ServiceLicence from '@/components/service-licence/index.vue'
import DownloadStatus from '@/components/download-status/index.vue'
import DynamicIcon from '@/components/DynamicIcon.vue';
import Offline from '@/components/empty/offline.vue'
import VueImgFallback from '@/directives/img-fallback'
import VueLog from '@/directives/v-log'
import InputFocus from '@/directives/input-focus';
import ScrollLimiter from '@/directives/scroll-limiter'
import SourcePreloader from '@/packages/source-preloader'
import { initBootId, initSessionID } from '@/utils/boot'
import { setTag } from '@/utils/bl';
import '@/packages/TSJsbridge'
import { withTimeoutHandling } from './utils/promiseUtils';
import Vue3TouchEvents from 'vue3-touch-events';
import ErrorHandler from './plugins/errorHandler'

const isOnlineDebug = window.location.href.includes('debug');
const isStageDebug = window.location.href.includes('tsl.stage');
const isWatermark = window.location.href.includes('watermark');
const _boot_id = initBootId()
initSessionID()
setTag(_boot_id)

// if (isOnlineDebug || isStageDebug) {
//   import(
//     '@/utils/debug'
//   );
// }

if (process.env.NODE_ENV !== 'production') {
  import('@/utils/debug')
}

if (isWatermark) {
  import(
    '@/utils/watermark'
  )
}

new SourcePreloader({
  sources: [
    // 歌手占位图
    'https://qncweb.ktvsky.com/20231226/vadd/a3056a76c9626aa4d68318625ae87a35.png',
    // 歌单占位图
    'https://qncweb.ktvsky.com/20211216/vadd/c6d01bc5aa27699dc276fbeab70ecfe5.png',
    // mv封面
    'https://qncweb.ktvsky.com/20211216/vadd/b1e6cb49a76d753245ec0a5dd71356dc.png',
  ]
})

const app = createApp(App)
app.component('SearchBar', SearchBar)
app.component('HeadBar', HeadBar)
app.component('VantStyle', VantStyle)
app.component('LoadMore', LoadMore)
app.component('SuspenseStatus', SuspenseStatus)
app.component('ServiceLicence', ServiceLicence)
app.component('DownloadStatus', DownloadStatus)
app.component('Offline', Offline)
app.component('DynamicIcon', DynamicIcon)

app.config.errorHandler = function (err, instance, info) {
  console.log('Vue error:', err, instance, info)
  console.error('Vue error:', err, instance, info);
};
app
  .directive('Log', VueLog)
  .directive('ImgFallback', VueImgFallback)
  .directive('Focus', InputFocus)
  .directive('ScrollLimiter', ScrollLimiter)
  .provide('withTimeoutHandling', withTimeoutHandling)
  .use(LoginConfrimGlobalModal)
  .use(LoginQrcodeGlobalModal)
  .use(VipQrcodeGlobalModal)
  .use(ActivityGlobalModal)
  .use(MobileOrderQrcodeModal)
  .use(UserCollectModal)
  .use(MicOnlineModal)
  .use(SplashScreenModal)
  .use(singNotify)
  .use(VueLoading)         
  .use(GradeResultModal)                                                                                                                            
  .use(Toast)
  .use(store)
  .use(router)
  .use(Vue3TouchEvents)
  .use(ErrorHandler, { endpoint: '/api/logs/report' })
  .mount('#app');

const pollingWorker = new Worker('./pollingWorker.js', { type: 'module' });

pollingWorker.postMessage(_boot_id);