<template>
    <div
        v-if="!isVip && routeName !== 'home' && routeName !== 'songList' && routeName !== 'agreementPrivacy' && routeName !== 'agreementUser'"
        class="search-bar-operation"
        @click.stop="handleShowVip"
    >
      <img
        class="search-bar-operation-img"
        src="https://qncweb.ktvsky.com/20250613/vadd/84e89b8f8f352ad29b136422f296e2c7.png"
      >
    </div>
</template>
  
<script setup>
  import { computed, defineProps, onMounted } from 'vue'
  import { useStore } from 'vuex'
  import useVip from '@/composables/useVip'
  
  // Define props
  const props = defineProps({
    routeName: {
      type: String,
      default: ''
    }
  })
  
  const store = useStore()
  const { showVipQrcode } = useVip()
  
  const userInfo = computed(() => store.state.userInfo)
  const isLogin = computed(() => !!userInfo.value.unionid)
  const isVip = computed(() => store.state.vipInfo?.end_time)
  // const isVip = computed(() => {
  //   if (store.state.vipInfo?.end_time) {
  //     const vipEndDate = new Date(store.state.vipInfo.end_time).getTime()
  //     const now = Date.now()
  //     return now <= vipEndDate
  //   }
  //   return false
  // })
  // const currentSize = computed(() => store.state.currentSize);
  
  const handleShowVip = () => {
  //   sendLog({
  //     event_type: 'click',
  //     event_name: 6001,
  //     event_data: {
  //       str1: '首页',
  //       str2: '底部运营位',
  //       str3: '点击',
  //       str4: 'click',
  //       str5: isLogin.value ? '2' : '1',
  //     }
  //   })
    showVipQrcode({
      log: '通用-顶部运营位',
      logData: {
        str1: '通用',
        str2: '顶部运营位',
        str3: '点击',
        str4: 'click',
      },
      isLogin: isLogin.value,
      fr:  isLogin.value ? 1865 : 1864,
    })
  
  }
  
  
  onMounted(() => {
      // sendLog({
      //   event_type: 'click',
      //   event_name: 6001,
      //   event_data: {
      //     str1: '歌星详情页',
      //     str2: '歌星详情页-运营位',
      //     str3: '展示',
      //     str4: 'show',
      //     // str5: isLogin.value ? '2' : '1',
      //   }
      // })
  })
  
</script>
  
<style lang="stylus" scoped>
  .search-bar-operation
    display: flex
    align-items: center
    width 426px
    height auto // 75px
    margin-right 36px

    &-img
      width 100%
      height 100%
  
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)
        display none
  
    // 三分之一屏
    @media (max-width: 700px)
        display none
  </style>