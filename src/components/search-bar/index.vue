<template>
  <div class="search-bar flex-between" :class="enableSearch && 'enable-search'">
    <div
      class="search-bar-left"
      :class="{
        'flex-between': isShowSearch && currentSize === 'small'
      }"
    >
      <Logo v-show="isShowLogo" :themeClass="themeClass" />
      <div 
        v-show="isShowBack" 
        class="back align-items-center" 
        @click="handleBack"
      >
        <DynamicIcon name="back" />
      </div>
      <span v-show="title" class="title ellipsis">{{ title }}</span>

      <div 
        v-show="isShowSearch"
        class="search-input align-items-center" 
      >
        <DynamicIcon name="search" />
        <input
          type="text"
          :placeholder="placeholder"
          v-model="keyword"
          class="search-bar-tool-input"
          v-focus
          ref="searchInputDom"
          @mousedown="disableDoubleClickSelection"
          @keydown="handleSearchKeydown($event)"
          maxlength="20"
          autocapitalize="off"
          style="-webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;"
        />
        <div
          v-show="keyword"
          class="clear"
          @click.stop="keyword = ''"
        >
          <DynamicIcon name="close" />
        </div>
      </div>
      <div 
        v-show="isShowSearch" 
        class="search-btn flex-center" 
        @click="handleSearch"
      >
        搜索
      </div>
    </div>
    <!-- <div @click="handleClearCache" style="margin-right: 30px;">清除缓存</div> -->
    <!-- <div @click="handleClearCache" style="margin-right: 30px;">清除缓存</div>
    <div @click="handleToggleMV">歌词/MV</div>
    <div @click="handleToggleTheme">切换皮肤</div> -->
    <div v-if="centerTitle" class="search-bar-center">{{ centerTitle }}</div>
    
    <!-- <div style="position:fixed;top:20px;left:0" @click="handleToggleTheme">切换皮肤</div> -->
    <!-- <div @click="handleToggleMV" style="position:fixed;top:50px;left:100px">切换歌词/mv isMvMode{{ isMvMode }}</div>
    <div @click="handleClearCache" style="position:fixed;top:50px;left:600px">清除缓存</div> -->

    <!-- <div @click="handleToggleSpeed(0)" style="position:fixed;top:50px;left:0px">低速挡位</div> 
    <div @click="handleToggleSpeed(120)" style="position:fixed;top:50px;left:100px">高速挡位</div>  -->
    <!-- <div style="position:fixed;top:0;left:0" @click="handleToggleTheme">切换皮肤</div>
    <div @click="handleClearCache" style="position:fixed;top:0;left:100px">清除缓存</div> -->
    
    <div class="search-bar-right">
      <!-- <div
        v-if="isShowMic && !!isShowMic"
        class="mic"
        v-log="{
          event_type: 'show',
          event_name: '6008',
          event_data: {
            str1: '我的页',
            str2: '麦克风',
            str3: '展示入口',
            str4: 'show',
          }
        }"
      >
        <div class="mic-main flex-center" @click="handleClickMic">
          <DynamicIcon name="mic" />
          <p>麦克风</p>
        </div>
        <div v-if="microphones.length" class="mic-spots">
          <p
            v-for="item in microphones"
            :key="item"
            :class="item.status && 'active'"></p>
        </div>
        <div v-if="showMicInfo" class="mic-info">
          <ul>
            <li
              v-for="(item, index) in microphones"
              :key="item"
              :class="item.status && 'active'"
            >
              <span></span>麦克风 {{index + 1}}
            </li>
          </ul>
          <span class="triangle"></span>
        </div>
      </div> -->

      <Operation
        :routeName="routeName"
      />

      <div
        v-show="isShowToSearch"
        class="search-input align-items-center"
        @click="handleToSearch"
      >
        <DynamicIcon name="search" />
        <p style="opacity: 0.5;">搜索歌曲、歌手</p>
      </div>
      <div
        v-if="currentSize !== 'small'"
        v-show="isShowOrder"
        class="already-order flex-center" 
        @click="handleOpenOrderControl"
        v-log="{
          event_type: 'click',
          event_name: '6012',
          event_data: {
            str1: '通用',
            str2: '顶部已点',
            str3: '点击已点icon',
            str4: 'click',
            str5: routeName,
          }
        }"
      >
        <DynamicIcon name="already-order" />
        <span v-if="orderedSongNum" class="flex-center">{{ orderedSongNum }}</span>
        <p>已点</p>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ref,
  computed,
  onBeforeMount,
  toRefs,
  watch,
  onMounted,
  nextTick,
  inject
} from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import Operation from './operation.vue'
import useSongItem from '@/composables/useSongItem'
import useLoginValid from '@/composables/useLoginValid'
import eventBus from '@/utils/event-bus'
import useVip from '@/composables/useVip'
import { sendLog } from '@/directives/v-log/log'
import useMicOnline from '@/components/modal/global/mic-online/create.js'
import { TSNativeInstance } from '@/packages/TSJsbridge';
import useDownload from '@/composables/useDownload';
import Logo from './logo.vue'
// import '@/assets/icons/search.svg';

export default {
  name: 'SearchBar',
  props: {
    placeholder: String,
    title: String,
    centerTitle: String,
    isShowLogo: {
      type: Boolean,
      default: false
    },
    isShowBack: {
      type: Boolean,
      default: true,
    },
    isShowSearch: {
      type: Boolean,
      default: false
    },
    isShowToSearch: {
      type: Boolean,
      default: false
    },
    isShowOrder: {
      type: Boolean,
      default: true,
    },
    isShowMic: {
      type: Boolean,
      default: false
    },
    enableSearch: {
      type: Boolean,
      default: false
    },
    enableBack: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Logo,
    Operation,
  },
  methods: {
    handleFocusInput() {
      if (this.$route.query.keyword) return
      const inputDom = this.$refs.searchInputDom
      console.log('handleFocusInput')
      inputDom.focus()
    },
    handleBlurInput() {
      const inputDom = this.$refs.searchInputDom
      inputDom.blur()
    },
  },
  setup(props, { emit }) {
    const withTimeoutHandling = inject('withTimeoutHandling');

    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const { isShowMic, enableBack } = toRefs(props)
    const { orderSong } = useSongItem()
    const { showLoginQrcode, isLogin } = useLoginValid()
    const { showVipQrcode, isVipUser } = useVip()
    const $useMicOnline = useMicOnline()
    const { getIsLocalSong } = useDownload();
    const micCount = ref(4)
    const micMallInfo = computed(() => store.state.base.mallInfo)
    const showMicInfo = ref(false)
    const routeName = computed(() => route.name)

    const themeClass = computed(() => store.state.themeClass)
    const currentSize = computed(() => store.state.currentSize);

    // 搜索页和歌星页的来源埋点
    const frObj = {
      search: 1764,
      singer: 1766,
    }
    const route_page = computed(() => route.name)

    let videoPlayerHistory = computed(() => store.state.videoPlayerHistory)
    const orderedSongIdMap = computed(() => store.state.orderedSongIdMap)
    const initControlPlay = computed(() => store.state.videoInitAutoPlay)
    const orderedList = computed(() => store.state.orderedList)
    const orderedSongNum = computed(() => store.state.orderedList.length)
    const microphones = computed(() => store.state.microphones)
    const isMvMode = computed(() => store.state.mvMode.mode === 'mv')

    const videoPaused = computed(() => store.state.videoPaused)
    const isSinging = computed(
      () =>
        orderedSongNum.value && !videoPaused.value
    )
    let keyword = ref('')

    let isShowOrderToast = ref(false)
    let showOrderToastTimer = null
    let micOnlineInstance = ref(null)

    const handleBack = () => {
      if (enableBack.value) {
        emit('back')
        keyword.value = ''
        return
      }
      store.commit('base/SET_IS_ROUTER_BACK', true)

      if (window.history.state && window.history.state.back === null) {
        // 如果没有上一页，跳转到首页
        console.log('没有上一页，跳转到首页')
        router.push({ name: 'home' });
      } else {
        console.log('有上一页，返回上一页')
        router.back()
      }
    }

    const showOrderToast = () => {
      isShowOrderToast.value = true
      showOrderToastTimer = setTimeout(() => {
        isShowOrderToast.value = false
      }, 3 * 1000)
    }

    // 只有这里可以进mv页
    const handleOpenMv = async () => {
      console.log('开始处理打开MV');

      try {
        if (!videoPlayerHistory.value.songItem.songid && !orderedSongNum.value) {
          console.log('未点播歌曲且未登录');
          if (isLogin.value) {
            // Toast('请点播歌曲')
            if (isShowOrderToast.value) clearTimeout(showOrderToastTimer);
            showOrderToast();
            console.log('显示点播提示');
          } else {
            showLoginQrcode();
            console.log('显示登录二维码');
          }
          return;
        }

        if ((!isLogin.value || !isVipUser.value)) {
          console.log('未登录或非VIP用户');
          const isLocal = await withTimeoutHandling(getIsLocalSong(orderedList.value[0]));
          if (orderedList.value[0].is_vip && !isLocal) {
            showVipQrcode();
            console.log('显示VIP二维码');
            return;
          }
        }

        sendLog({
          event_type: '10000~50000',
          event_name: 10066,
          event_data: {
            str1: '首页',
            str2: '进入欢唱',
            str3: '点击进入欢唱',
            str4: 'click',
          },
        });
        console.log('发送日志');

        if (
          Object.keys(orderedSongIdMap.value).length &&
          !initControlPlay.value
        ) {
          console.log('恢复播放已点歌曲');
          orderSong(orderedList.value[0], {
            position: 'recovery',
            isPushOrdered: false,
            enabledMvShow: true,
            useFirstSongAutoPlay: true,
          });
          store.commit('UPDATE_IS_SING_STATUS', true);
          store.dispatch('searchTips/updateIsShowSingTips', false); // 关闭tips弹窗
          console.log('更新唱歌状态并关闭提示弹窗');
          return;
        }

        console.log('恢复播放历史歌曲');
        orderSong(videoPlayerHistory.value.songItem, {
          position: 'recovery',
          isPushOrdered: false,
          enabledMvShow: true,
          from: 'searchBar'
        });
        store.commit('UPDATE_IS_SING_STATUS', true);
        store.dispatch('searchTips/updateIsShowSingTips', false); // 关闭tips弹窗
        console.log('更新唱歌状态并关闭提示弹窗');
      } catch (error) {
        console.error('handleOpenMv 错误:', error);
      }

      console.log('处理打开MV结束');
    }
    
    const handleToSearch = () => {
      try {
        console.log('handleToSearch')
        router.push({
          name: 'search',
        })
        emit('on-click-search')
      } catch (error) {
        console.log('handleToSearch error', error)
      }
    }

    const handleClearKeyword = () => {
      keyword.value = ''
    }

    const handleChangeKeyword = (e) => {
      keyword.value = e
    }

    const handleFormSubmit = () => {
      return false
    }

    const handleSearch = () => {
      emit('search', keyword.value)
    }

    const handleSearchKeydown = (e) => {
      if (e.keyCode == 13) {
        handleSearch()
      }
    }

    const handleOpenOrderControl = () => {
      eventBus.emit('show-order-song-control-popup')
      sendLog({
        event_type: '10000~50000',
        event_name: 10005,
        event_data: {
          str1: '首页',
          str2: '已点',
          str3: '打开已点',
          str4: 'click',
        },
      })
    }

    const handleShowVip = () => {
      sendLog({
        event_type: 'custom',
        event_name: 1733,
      })
      showVipQrcode({
        log: frObj[route.name] ? `${route.name}-顶部运营位` : '首页-顶部运营位',
        isLogin: isLogin.value,
        fr: frObj[route.name] ? frObj[route.name] : 1758
      })
      sendLog({
        event_type: '10000~50000',
        event_name: 10004,
        event_data: {
          str1: '首页',
          str2: '顶部运营位',
          str3: '点击运营位',
          str4: 'click',
        },
      })
    }

    const handleClickMic = () => {
      if (microphones.value.length) {
        showMicInfo.value = !showMicInfo.value
      } else {
        sendLog({
          event_type: 'click',
          event_name: '6008',
          event_data: {
            str1: '我的页',
            str2: '麦克风',
            str3: '购买弹窗',
            str4: 'click',
          }
        })
        // sendLog({
        //   event_type: '10000~50000',
        //   event_name: 30053,
        //   event_data: {
        //     str1: '麦克风',
        //     str2: '购买弹窗',
        //     str3: 'show',
        //   },
        // })
        micOnlineInstance.value = $useMicOnline.show({
          info: micMallInfo.value
        })
      }
    }

    const handleExit = () => {
      console.log('handleExit')
      TSNativeInstance.exit()
    }

    watch(microphones.value, () => {
      if (micOnlineInstance.value) {
        micOnlineInstance.value.hide()
      }
    })

    const handleClearInput = () => {
      keyword.value = ''
    }

    onBeforeMount(async () => {
      if (!isShowMic.value) return
      if (micMallInfo.value.model) {
        sendLog({
          event_type: '10000~50000',
          event_name: 30052,
          event_data: {
            str1: '麦克风',
            str2: '展示入口',
            str3: 'show',
          },
        })
      }
    })

    const disableDoubleClickSelection = (event) => {
      if (event.detail > 1) {
        event.preventDefault();
      }
    }

    const handleToggleTheme = () => {
      store.commit('SET_THEME', themeClass.value === 'themeDark' ? 'themeLight' : 'themeDark')
      console.log(themeClass.value === 'themeDark' ? 'themeLight' : 'themeDark')
    }

    const handleToggleMVMode = () => {
      console.log(isMvMode.value ? '歌词' : 'mv')
      store.commit('mvMode/SAVE_MV_MODE', isMvMode.value ? '歌词' : 'mv')
    }

    const handleClearCache = () => {
      window.TSFile.clearCache()
    }

    const handleToggleMV = () => {
      console.log(isMvMode.value ? '歌词' : 'mv')
      store.commit('mvMode/SAVE_MV_MODE', {
        mode: isMvMode.value ? '歌词' : 'mv'
      })
    }

    onMounted(nextTick(() => {
      setTimeout(() => {
        const refSearchInput = document.getElementById('searchInputId')
        if (!refSearchInput) return
        refSearchInput.addEventListener('copy', (event) => event.preventDefault())
        refSearchInput.addEventListener('mousedown', (e) => {
          if (e.detail > 1) {
            e.preventDefault()
          }
        })
      }, 1000)
    }))

    const handleToggleSpeed = (payload) => {
      console.log('handleToggleSpeed', payload)
      store.dispatch('carMonitor/setCarSpeed', payload)
    }

    return {
      themeClass,
      keyword,
      route_page,
      orderedSongNum,
      orderedList,
      isShowOrderToast,
      isSinging,
      micCount,
      micMallInfo,
      showMicInfo,
      microphones,
      handleBack,
      handleOpenMv,
      handleToSearch,
      handleSearch,
      handleChangeKeyword,
      handleClearKeyword,
      handleSearchKeydown,
      handleFormSubmit,
      handleOpenOrderControl,
      handleShowVip,
      handleClickMic,
      handleExit,
      handleClearInput,
      disableDoubleClickSelection,
      handleToggleTheme,
      handleClearCache,
      handleToggleMV,
      routeName,
      currentSize,
      handleToggleMVMode,
      handleToggleSpeed,
      isMvMode,
    }
  },
}
</script>

<style lang="stylus" scoped>
.dark-theme
  .search-bar
    --mic-background rgba(255, 255, 255, 0.1)
    --mic-info-background rgba(255, 255, 255, 0.08)

.search-bar
  --mic-background rgba(255, 255, 255, 1)
  --mic-info-background rgba(255, 255, 255, 1)
  --dropdown-height 80px
  --item-height 75px

  height calc(var(--search-bar-height) + var(--dropdown-height))
  padding var(--dropdown-height) 80px 0
  // background var(--background-primary)

  // 三分之一屏
  @media (max-width: 700px)
    padding-right 60px
    padding-left 60px

  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    padding-right calc(32px * var(--scale)) !important
    padding-left calc(32px * var(--scale)) !important
    padding-bottom calc(20px * var(--scale)) !important
    height calc(65px * 1.48 + var(--dropdown-height)) !important

  .svg-icon
    width 42px
    height 42px

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(28px * var(--scale)) !important
      height calc(28px * var(--scale)) !important
  .search-input
    border 1px solid var(--border-color)
    border-radius 12px
    width 528px
    height 75px
    position relative
    .svg-icon
      color var(--text-primary-color)
      margin 0 8px 0 24px
    
    // 三分之一屏
    @media (max-width: 700px)
      width 522px
      height 138px
      border-radius 30px
      font-size var(--font-size-extra-small)
      *
        font-size var(--font-size-extra-small)
      .svg-icon
        width 72px
        height 72px
    // 三分之一屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(332px * var(--scale)) !important 
      height calc(50px * var(--scale)) !important
      border-radius calc(10px * var(--scale)) !important

  .search-bar-left
    display flex
    align-items center
    max-width 65%

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      max-width 75% !important

    &.flex-between
      width 100%
      max-width 100%
    .back
      width 36px
      height 36px
      color var(--text-primary-color)
      margin 0px

      .svg-icon
        width 100%
        height 100%

      // 三分之一屏
      @media (max-width: 700px)
        width calc(20px * 3)
        height calc(20px * 3)

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(26px * var(--scale)) !important
        height calc(26px * var(--scale)) !important
    
    .title
      margin-left 60px
      font-size var(--font-size-extra-large)
      flex 1

      // 三分之一屏
      @media (max-width: 700px)
        font-size var(--font-size-large)
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        font-size calc(22px * var(--scale)) !important
    .search-input
      display flex
      align-items center
      width 520px
      margin-left 60px
      position relative
      margin-right 0px

      // 三分之一屏
      @media (max-width: 700px)
        width calc(200px * 3)
        margin-left calc(28px * 3)
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(260px * var(--scale)) !important
        // margin-left calc(28px * 3) !important

      .svg-icon
        position absolute
        left 24px
        top 50%
        transform translateY(-50%)
        margin 0px

        // 三分之一屏
        @media (max-width: 700px)
          left calc(12px * 3)
          width calc(28px * 3)
          height calc(28px * 3)

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(28px * var(--scale)) !important
          height calc(28px * var(--scale)) !important
      input
        width 100%
        height 100%
        padding-left 79px
        padding-right 60px

        // 三分之一屏
        @media (max-width: 700px)
          padding-left calc(46px * 3)
          padding-right calc(30px * 3)

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          padding-left calc(46px * var(--scale)) !important
          padding-right calc(30px * var(--scale)) !important
      
      .clear
        width 30px
        height 30px
        position absolute
        right 20px
        top 50%
        transform: translateY(-50%);
    
        // 三分之一屏
        @media (max-width: 700px)
          width calc(18px * 3)
          height calc(18px * 3)

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(18px * var(--scale)) !important
          height calc(18px * var(--scale)) !important

        .svg-icon
          margin 0px
          width 100%
          height 100%
          left unset

    .search-btn
      width: 180px
      height: var(--item-height)
      border-radius: 16px
      background #DBAE6A
      margin-left 28px
      color rgba(0, 0, 0, 0.8)

      // 三分之一屏
      @media (max-width: 700px)
        width calc(96px * 3)
        height calc(50px * 3)
        border-radius calc(12px * 3)

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(120px * var(--scale)) !important
        height calc(50px * var(--scale)) !important
        border-radius calc(12px * var(--scale)) !important
 

  .search-bar-right
    display flex
    align-items center
    .mic
      width: 212px;
      height: var(--item-height)
      border-radius: 30px
      background var(--mic-background)
      margin-right 28px
      position relative
      
      // 三分之一屏
      @media (max-width: 700px)
        width calc(128px * 3)
        height calc(50px * 3)
        .svg-icon
          width calc(30px * 3)
          height calc(30px * 3)
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(128px * 1.48) !important
        height calc(50px * 1.48) !important
      &-info
        position absolute
        background: var(--mic-info-background)
        padding 0px 24px
        left -222px
        top 0
        border-radius 10px
        backdrop-filter: blur(1000px)
        width 200px
        height 150px
        display flex
        flex-direction column
        justify-content center
        z-index 100

        // 三分之一屏
        @media (max-width: 700px)
          zoom 2
          box-shadow: 0px 8px 50px 0px rgba(0, 0, 0, 0.2);
        .triangle
          content ""
          position absolute
          top 35px
          right -20px
          width: 20px;
          height: 10px;
          border-top: 10px solid transparent; /* 三角形的上边 */
          border-bottom: 10px solid transparent; /* 三角形的下边 */
          border-left: 10px solid var(--mic-info-background); /* 三角形的右边 */
        li
          width 150px
          padding 18px 0 18px 15px
          // color: rgba(255, 255, 255, 0.20);
          border-bottom 1px solid var(--border-color);
          display flex
          align-items center
          color var(--border-color2)

          // 三分之一屏
          @media (max-width: 700px)
            font-size 24px
          &:last-child
            border none
          span
            width: 12px;
            height: 12px;
            border-radius 50%
            background var(--text-primary-color)
            opacity 0.2
            margin-right 16px
          &.active
            color #1AD773
            *
              opacity 1
            span
              background #1AD773
      &-spots
        display flex
        justify-content center
        width 100%
        height 4px
        position absolute
        bottom 10px
        left 0

        // 三分之一屏
        @media (max-width: 700px)
          zoom 2
        p
          width 20px
          height 4px
          background: var(--border-color2)
          margin 0 4px
          &.active
            background: #65D47D
      .svg-icon
        width 44px
        height 44px
        margin-right 8px
        
        // 三分之一屏
        @media (max-width: 700px)
          width calc(30px * 3)
          height calc(30px * 3)
      &-main
        width 100%
        height 100%
        // p
        //   font-size var(--font-size-large)

    .already-order
      width 184px
      height var(--item-height)
      margin-left 20px
      background var(--top-right-corner-background)
      color #fff
      border-radius 16px
      position relative
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        border-radius calc(12px * var(--scale)) !important
      p
        color #fff
        // font-size var(--font-size-large)
      .svg-icon
        margin-left 0px
      span
        position absolute
        width 36px
        height 36px
        right -8px
        top -8px
        border-radius 100%
        background #DBAE6A
        color #342611
        font-size 24px
        font-weight 700
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width  calc(120px * var(--scale)) !important
        height calc(50px * var(--scale)) !important
        border-radius calc(12px * var(--scale)) !important
        span
          width  calc(33px * var(--scale)) !important
          height calc(24px * var(--scale)) !important
          border-radius calc(22px * var(--scale)) !important
          top calc(-8px * var(--scale)) !important
          font-size var(--font-size-extra-small) !important
.enable-search
  .search-bar-left
    .search-input
      margin-left 30px
</style>