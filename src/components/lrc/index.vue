<template>
  <div
    class="lrc"
    @click="handleClickLrc"
    :class="{
      'pos-left': pos == 2 && currentSize !== 'small',
      'pos-right': pos == 1 && currentSize !== 'small',
      'lrc-small': currentSize === 'small'
    }"
  >
    <!-- <div
      class="test"
      style="position: fixed; top: 0; right: 0;width:200px;height:auto;background: skyblue;z-index:1000"
    >
      <p @click="handleClickStart">开始 </p>
      <p @click="handleClickPause">暂停</p>
      <p>倒计时:{{ countdownToFirstLyric }}</p>
      <p>歌词:{{ currentIyricIndex }}</p>
      <p>歌词长度：{{lrcData.length}}</p>
    </div> -->
    <div v-if="currentSize === 'small'" class="small-back flex">
      <div class="left flex" @click.stop="handleClickBack">
      <SongInfo v-if="!mvIsHide" />
      <div
          class="back align-items-center" 
          @click="handleBack"
        >
          <!-- <DynamicIcon name="back" /> -->
           <img :src="backBg"/>
        </div>
      </div>
    </div>
    <div v-if="currentSize === 'small'" class="small-control flex-between">
      <div class="replay" @click="handleClickControl('replay')">
        <svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.8">
          <path d="M21.4812 14.9924C27.7035 13.3197 34.0991 17.0243 35.7664 23.2668C37.4336 29.5092 33.7411 35.9257 27.5188 37.5984C21.2966 39.271 14.9009 35.5665 13.2337 29.324C12.6498 27.1379 12.7233 24.9304 13.3326 22.9128M26.3147 18.606L20.046 15.441L24.4998 10" stroke="currentColor" stroke-width="2.33333" stroke-linecap="round" stroke-linejoin="round"/>
          </g>
        </svg>
      </div>
      <div
        v-show="!videoPaused"
        class="playpause"
        @click="handleClickControl('pause')"
      >
        <svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.8">
          <rect x="13.4166" y="10" width="2.33333" height="27.7667" rx="1.16667" fill="currentColor"/>
          <rect x="33.25" y="10" width="2.33333" height="28" rx="1.16667" fill="currentColor"/>
          </g>
        </svg>
      </div>
      <div
        v-show="videoPaused"
        class="playpause"
        @click="handleClickControl('play')"
      >
        <svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.8">
          <path d="M14.5 10.1691L33.7095 24L14.5 37.8309L14.5 10.1691ZM14.1479 9.91566L14.1483 9.91598C14.1482 9.91587 14.1481 9.91577 14.1479 9.91566L14.2777 9.73532L14.1479 9.91566Z" stroke="currentColor" stroke-width="2.22222"/>
          </g>
        </svg>
      </div>
      <div class="next" @click="handleClickControl('next')">
        <svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.8">
          <path d="M14.0557 12.1691L30.4874 24L14.0557 35.8309L14.0557 12.1691Z" stroke="currentColor" stroke-width="2.22222"/>
          <path d="M33.8334 13.9998C33.8334 13.3861 34.3308 12.8887 34.9445 12.8887C35.5581 12.8887 36.0556 13.3861 36.0556 13.9998V35.1109C36.0556 35.7245 35.5581 36.222 34.9445 36.222C34.3308 36.222 33.8334 35.7245 33.8334 35.1109V13.9998Z" fill="currentColor"/>
          </g>
        </svg>
      </div>
      <div class="track" @click="handleClickControl('track')">
        <!-- 原唱icon -->
        <svg v-show="enabledAudioTrackId != 1 || !supportsOrg" width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.8">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M25.1593 12.4004C18.6908 12.4004 13.45 17.6348 13.45 24.0881C13.45 29.877 17.6679 34.6866 23.2062 35.6141C23.7509 35.7054 24.1185 36.2209 24.0273 36.7656C23.9361 37.3103 23.4206 37.6779 22.8759 37.5867C16.3923 36.5008 11.45 30.8727 11.45 24.0881C11.45 16.527 17.5894 10.4004 25.1593 10.4004C30.5426 10.4004 35.2002 13.4987 37.4427 18.0035C37.6888 18.4979 37.4875 19.0982 36.9931 19.3443C36.4987 19.5905 35.8984 19.3892 35.6523 18.8948C33.7354 15.0442 29.7562 12.4004 25.1593 12.4004Z" fill="currentColor"/>
          <circle cx="25.0499" cy="24.0012" r="2.8" fill="currentColor"/>
          <circle cx="28.58" cy="34.9014" r="2" stroke="currentColor" stroke-width="2"/>
          <circle cx="35.5499" cy="33.501" r="2" stroke="currentColor" stroke-width="2"/>
          <path d="M30.5499 33.601V27.101L37.5499 25.001V32.351" stroke="currentColor" stroke-width="2" stroke-linecap="square"/>
          </g>
        </svg>
        <!-- 伴唱icon -->
        <svg v-show="enabledAudioTrackId == 1 && supportsOrg" width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.8" clip-path="url(#clip0_1864_41483)">
          <path d="M37.5 24C37.5 31.1797 31.6797 37 24.5 37C17.3203 37 11.5 31.1797 11.5 24C11.5 16.8203 17.3203 11 24.5 11C31.6797 11 37.5 16.8203 37.5 24Z" stroke="currentColor" stroke-width="2"/>
          <circle cx="24.5" cy="24.0002" r="2.8" fill="currentColor"/>
          <rect x="30.2346" y="17.2754" width="2.1" height="4.9" rx="1.05" transform="rotate(45 30.2346 17.2754)" fill="currentColor"/>
          <rect x="21.2402" y="26.2695" width="2.1" height="4.9" rx="1.05" transform="rotate(45 21.2402 26.2695)" fill="currentColor"/>
          </g>
          <defs>
          <clipPath id="clip0_1864_41483">
          <rect width="28" height="28" fill="white" transform="translate(10.5 10)"/>
          </clipPath>
          </defs>
        </svg>
      </div>

    </div>
    <!-- <div class="lrc-background"></div> -->
    <div
      id="refLrc"
      class="lrc-out flex-column"
      :class="currentIyricIndex < 1 && 'init'"
    >
      <div
        v-show="currentPlayNoLrc || countdownToFirstLyric > 3 && currentIyricIndex < 0"
      >
        <p class="lrc-list-songname">{{ songName }}</p>
        <p class="lrc-list-singername">{{ singerName }}</p>
      </div>
      <div v-if="currentPlayNoLrc" class="lrc-list-nodata">
        <!-- <div class="lrc-list-songname">{{ songName }}</div>
        <div class="lrc-list-singername">{{ singerName }}</div> -->
        <p>暂无匹配歌词</p>
      </div>

      <div
        v-if="lrcData.length"
        v-show="countdownToFirstLyric <= 3 || currentIyricIndex >= 0"
        class="lrc-list flex-column"
      >
        <!-- <div
          v-show="countdownToFirstLyric > 3 && currentIyricIndex < 0"
          class="flex-column"
          style="height: 100%;justify-content:center"
        >
          <p class="lrc-list-songname">{{ songName }}</p>
          <p class="lrc-list-singername">{{ singerName }}</p>
        </div> -->
        <div
          style="overflow-y: hidden;"
          id="lrc-scroll"
          class="lrc-scroll"
        >
          <div class="start-node">
            <span :class="(countdownToFirstLyric <= 2 || currentIyricIndex >= 0) && 'start-node-active'"></span>
            <span :class="(countdownToFirstLyric <= 1 || currentIyricIndex >= 0) && 'start-node-active'"></span>
            <span :class="(countdownToFirstLyric <= 0 || currentIyricIndex >= 0) && 'start-node-active'"></span>
          </div>
          <div
            v-for="(lyric, index) in lrcData"
            :key="index"
            class="lrc-list-item"
            :class="{ 'active': index === currentIyricIndex }"
          >
            {{ lyric.irc.join('').replace(/,/g, '') }}
          </div>
        </div>
      </div>

      <!-- <div v-if="currentPlayNoLrc" class="lrc-list-nodata">
        <p>暂无匹配歌词</p>
      </div> -->
    </div>
  </div>
</template>

<script setup>
import {
  defineProps,
  toRefs,
  watch,
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  defineEmits,
  ref,
} from 'vue'
import eventBus from '@/utils/event-bus'
import { useStore } from 'vuex'
import debounce from 'lodash/debounce'
import SongInfo from '../mv/components/song-info/index.vue'
import useTimeUpdate from '@/composables/useTimeUpdate'
import VueScrollTo from 'vue-scrollto'; // 引入 vue-scrollto

const props = defineProps({
  lrcData: Array,
  lrcEmpty: {
    type: Boolean,
    default: true,
  },
  songName: String,
  singerName: String,
  currentIyricIndex: {
    type: Number,
    default: -1,
  },
  paused: {
    type: Boolean,
    default: false,
  },
  currentPlayTime: {
    type: Number,
    default: 0,
  },
  browserType: {
    type: String,
    default: 'landscape'
  },
  pos: {
    type: Number,
    default: 0, // 0 不显示 1 右侧显示 2 左侧显示
  },
  from: {
    type: String,
    default: ''
  },
  currentSize: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['on-lrc-click'])

const store = useStore()
const { handleTimeupdate, currentPlayTime }  = useTimeUpdate()

const { lrcData, currentIyricIndex, currentSize, from } = toRefs(props)
const mvIsHide = computed(() => store.state.mvIsHide)
const videoPaused = computed(() => store.state.videoPaused);
const videoPlayer = computed(() => store.state.videoPlayerHistory);
const isLrcMode = computed(() => store.state.mvMode.mode === '歌词')
const enabledAudioTrackId = computed(() => videoPlayer.value.enabledAudioTrack.id);
const supportsOrg = computed(() => videoPlayer.value.supportsOrg);
const countdownToFirstLyric = computed(() => store.state.countdownToFirstLyric);
const currentPlayNoLrc = computed(() => store.state.songItemLrcEmpty)
const themeClass = computed(() => store.state.themeClass)
const hasOrderedSongs = computed(() => store.state.orderedList.length > 0);
const backBg = computed(() => themeClass.value === 'themeLight' ? 'https://qncweb.ktvsky.com/20250213/vadd/f8e87fb85facc5a987b5879de8b4d580.png' : 'https://qncweb.ktvsky.com/20250214/vadd/69d75b03d8e48a34493b28aad3b4192e.png')

const startNodeVisible = ref(false);

let goBackTimer = null
let reducePointTimer = null

let timer = null
const handleClickStart = () => {
  let intervalTime = 100;
  timer = setInterval(() => {
    handleTimeupdate(intervalTime);
    intervalTime += 500;
  }, 500);
}

const handleClickPause = () => {
  clearInterval(timer)
}

const handleClickControl = async (payload) => {
  switch (payload) {
    case 'replay':
      // 处理重播逻辑
      console.log('重播');
      eventBus.emit('video-control-replay');
      break;
    case 'play':
      // 处理播放逻辑
      console.log('播放');
      eventBus.emit('video-control-resume')
      break;
    case 'pause':
      // 处理暂停逻辑
      console.log('暂停');
      eventBus.emit('handle-video-pause')
      break;
    case 'next':
      // 处理下一首逻辑
      console.log('下一首');
      eventBus.emit('handle-video-next', 'small-lrc')
      break;
    case 'track':
      // 处理轨道逻辑
      console.log('轨道');
      eventBus.emit('handle-video-toggle-track')
      
      break;
    default:
      console.log('未知操作');
  }
}

const handleClickLrc = () => {
  if (!hasOrderedSongs.value) return

  if (currentSize.value === 'small') return

  emit('on-lrc-click')

  if (mvIsHide.value) {
    store.commit('UPDATE_MV_ISHIDE', false)
  }
}

const handleClickBack = () => {
  console.log('handleClickBack')
  store.commit('UPDATE_MV_ISHIDE', true)
}

const handleInit = () => {
  const scrollArea = from.value === 'mini'
    ? document.querySelector('#mini-lrc #lrc-scroll')
    : document.querySelector('#mv-fullscreen #lrc-scroll')

  if (scrollArea && scrollArea.scrollTop) {
    scrollArea.scrollTop(0)
  }
  console.log('lrc handleInit')
  if (goBackTimer) {
    clearTimeout(goBackTimer)
    goBackTimer = null
  }
  if (reducePointTimer) {
    clearInterval(reducePointTimer)
    reducePointTimer = null
  }
}

let isMvIsHideUpdated = false; // 标志位，表示 mvIsHide 是否更新

// 立即执行的防抖函数
const debouncedScroll = debounce(async () => {
  await nextTick();

  const scrollArea = from.value === 'mini'
    ? document.querySelector('#mini-lrc #lrc-scroll')
    : document.querySelector('#mv-fullscreen #lrc-scroll');

  const activeItem = scrollArea?.querySelector('.lrc-list-item.active');

  if (activeItem) {
    console.log('debouncedScroll防抖执行了');
    if (scrollArea.offsetHeight === 0) return;

    const offset = from.value === 'mini'
      ? -scrollArea.offsetHeight / 2
      : -scrollArea.offsetHeight * 0.3;

    // 使用 vue-scrollto 实现滚动
    VueScrollTo.scrollTo(activeItem, 0, {
      easing: 'ease',
      offset: from.value === 'mini' ? offset + 30 : offset, // 滚动到容器中心
      container: scrollArea, // 指定滚动容器
      duration: 0,
    });
  }
}, 500, { leading: true, trailing: false }); // leading: true 表示立即执行

// 监听 mvIsHide 更新
watch(mvIsHide, async () => {
  isMvIsHideUpdated = true; // 设置标志位

  debouncedScroll()

  setTimeout(() => {
    isMvIsHideUpdated = false; // 500ms 后清除标志位
  }, 500);
});

// 监听 mvIsHide 更新
watch(isLrcMode, async (val) => {
  if (val) {
    debouncedScroll()
  }
});

// 监听 currentIyricIndex 和 currentSize 更新
watch([currentIyricIndex, currentSize], async ([lyricIndex, size]) => {
  if (isMvIsHideUpdated) {
    // 如果 mvIsHide 在 500ms 内更新过，则跳过逻辑
    return;
  }

  if (lyricIndex === -1) {
    handleInit();
    return;
  }

  debouncedScroll();
});

watch(
  () => props.currentPlayTime,
  (newTime) => {
    if (newTime < 3 && !startNodeVisible.value) {
      startNodeVisible.value = true;
    } else if (newTime >= 3 && startNodeVisible.value) {
      startNodeVisible.value = false;
    }
  },
  { immediate: true }
);

const handleControlIrcReplay = () => {
  console.log('handleControlIrcReplay')
  handleInit()
}
const handleControlIrcNext = () => {
  console.log('handleControlIrcNext')
  handleInit()
}
const handleControlIrcEnd = () => {
  console.log('handleControlIrcEnd')
  handleInit()
}

const attachIrcPlayerEvents = () => {
  eventBus.on('irc-control-replay', handleControlIrcReplay)
  eventBus.on('irc-control-next', handleControlIrcNext)
  eventBus.on('irc-control-end', handleControlIrcEnd)
}
const detachIrcPlayerEvents = () => {
  eventBus.off('irc-control-replay', handleControlIrcReplay)
  eventBus.off('irc-control-next', handleControlIrcNext)
  eventBus.off('irc-control-end', handleControlIrcEnd)
}

onMounted(() => {
  attachIrcPlayerEvents()
})
onUnmounted(() => {
  detachIrcPlayerEvents()
})
onBeforeUnmount(() => {
  // const refLrc = document.getElementById('refLrc')
  // refLrc.removeEventListener('scroll', handleScroll)
})


</script>

<style lang="stylus" scoped>
.lrc
  background-image url('https://qncweb.ktvsky.com/20250123/other/4b62b4f2156f392b2978bd8bb149071a.png')
  background-color #cde8ef
.dark-theme .lrc
  background-image url('https://qncweb.ktvsky.com/20241228/other/76d45f0166aaefdd591e2a99d1a1d488.png')
  background-color #120d26
.dark-theme .back
  background rgba(255, 255, 255, .1)
  border-radius 20px
.lrc
  width 100vw
  height 100vh
  // color rgba(238, 238, 238, 0.4)
  font-size 70px
  overflow-y auto
  position relative
  display flex
  justify-content center
  align-items center
  // background url('https://qncweb.ktvsky.com/20240105/vadd/6f3236a5a683a7502f421250e9c94a8a.png')
  background-size cover !important
  background-position center
  .start-node
    display grid
    grid-template-columns repeat(3, 1fr)
    gap calc(16px * var(--scale))
    margin 0 auto calc(20px * var(--scale))
    width fit-content
    span
      width calc(16px * var(--scale))
      height calc(16px * var(--scale))
      border-radius 100%
      background var(--text-primary-color)
      opacity 0.2
    &-active
      opacity 0.6 !important
  &-out
    width 100vw
    height 100vh
    overflow-y auto
    position relative
    margin-top 0px
    display flex
    justify-content center
    align-items center

  .lrc-list-singername
    text-align center
    // color rgba(238, 238, 238, 0.8)
    font-size 40px
    padding-bottom 20px
    opacity 0.6
  .lrc-list-songname
    text-align center
    font-size 100px
    padding 0px 100px 30px

  &-list
    width 100%
    height 100% !important
    z-index 0
    position relative
    transition-property all
    transition-duration 0.5s
    transition-timing-function ease-in-out
    touch-action none
    display flex
    flex-direction column
    justify-content center
    .lrc-scroll
      height calc(100vh - 126px)
      padding-top 20vh
      padding-bottom 40vh
    *
      touch-action none
    &-item
      text-align center
      margin 0px auto
      font-size calc(40px * var(--scale))
      opacity 0.2
      white-space: normal; /* 允许换行 */
      max-width calc(300 / 399 * 100%)
      margin-top calc(40px * var(--scale))
      margin-bottom calc(40px * var(--scale))
      margin-left auto !important
      margin-right auto !important
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)   
        max-width calc(500 / 399 * 100%) !important
      
    .start-point
      height 100px
      margin-bottom 120px
      text-align center
      display flex
      justify-content center
      align-items center
      span
        padding 0px
        width 30px
        height 30px
        border-radius 50%
        opacity 0.2
        background var(--text-primary-color)
        &:nth-child(2)
          margin 0 18px
    .active
      height auto !important
      // font-size calc(56px * var(--scale))
      transform scale(calc(56 / 40))
      font-weight 700
      transition transform 0.4s linear
      opacity 1
      & + *
        opacity 0.5
        transform scale(calc(46 / 40))
        // font-size calc(46px * var(--scale))
    .active-scan
      background rgba(238, 238, 238, 0.86) -webkit-linear-gradient(left, rgba(124, 193, 255, 1), rgba(124, 193, 255, 1)) no-repeat 0 0
      -webkit-text-fill-color transparent
      -webkit-background-clip text
      background-size 0 100%
      animation scan 2s linear
      animation-fill-mode forwards
    .active-load
      background-size 100% 100%
    .active-pause
      animation-play-state paused !important
    .animate-fade-in
      animation fade-in 1.2s cubic-bezier(0.39, 0.575, 0.565, 1) both
  &-bottom
    position absolute
    bottom 50px
    display flex !important
    flex-direction column
    justify-content center
    align-items center
    z-index 9
    opacity 0.7
    img
      width 300px
    p
      // color #999999
      font-size 30px
      margin-top 40px
  @keyframes scan
    0%
      background-size 0 100%
    100%
      background-size 100% 100%
  @keyframes fade-in
    0%
      opacity 0.2
    100%
      opacity 1
  &-background
    position absolute
    top 0
    left 0
    right 0
    bottom 0
    z-index 0
    video
      width 100vw
      height 100vh

.pos-left
  padding-left 43vw
  // .lrc-background
  //   padding-left 43vw
  
  .lrc-list
    position relative
    p
      font-size 50px

    .lrc-list-item-p
      font-size 80px

  .lrc-out
    height 100vh !important
    display flex
    align-items center
      
.pos-right
  padding-right 43vw
  // .lrc-background
  //   padding-right 43vw
    
  .lrc-list
    position relative
    p
      font-size 50px

    .lrc-list-item-p
      font-size 80px

  .lrc-out
    height 100vh !important
    display flex
    align-items center

.lrc-list-nodata
  opacity 1
  .lrc-list-songname
    padding-top 0px
  .svg-icon
    width 90px
    height 90px
    margin 0 auto 45px
  p
    font-size 33px
    text-align center
    opacity 0.4

.mini-lrc
  width 100%
  height 100%
  padding calc(16px * var(--scale)) 0
  margin 0 !important
  position absolute
  left 0
  top 0
  z-index 10
  .lrc-out
    height 100%
  .lrc-list-songname
    font-size 48px
    padding 5vh 100px 12px
    overflow hidden
    text-overflow ellipsis
    display -webkit-box
    -webkit-box-orient vertical
    -webkit-line-clamp 2
  // .lrc-scroll
  //   padding-top 12vh
  .lrc-list-singername
    font-size 20px
  .lrc-list
    .lrc-list-item
      font-size calc(20px * var(--scale))
      margin 0px 0
      line-height 80px
      height 80px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        line-height 80px!important
        height 80px!important
        font-size calc(20px * var(--scale)) !important
    .active
      // font-size calc(24px * var(--scale))
      transform scale(calc(24 / 20))
      & + *
        // font-size calc(22px * var(--scale))
        transform scale(calc(22 / 20))
  .lrc-list-nodata
    p
      padding-top 0px

.lrc-small
  position fixed
  z-index 200
  left 0
  top 0
  background-image none
  background: var(--tab-button-background-color);
  .lrc-list-songname
    padding-bottom calc(16px * var(--scale))
  .lrc-scroll
    height calc(432px * 3)
  // .lrc-background
  //   display none
  .lrc-list-item
    margin-bottom calc(38px * var(--scale))
    font-size calc(22px * var(--scale))
  .active
    transform scale(calc(30 / 22))
    // font-size calc(30px * var(--scale))
    & + *
      transform scale(calc(26 / 22))
      // font-size calc(26px * var(--scale))
  .small-back
    width 100%
    height calc(82px * 3)
    position fixed
    top 0
    left 0
    padding-left calc(20px * 3)
    align-items center
    z-index 10
    .title
      font-size var(--font-size-large)
    .back
      position absolute
      right 70px
      width calc(34px * var(--scale))
      height calc(34px * var(--scale))
    .svg-icon
      width calc(20px * 3)
      height calc(20px * 3)
      margin-right calc(16px * 3)
    .song-info
      width calc(242px * 3)
      height fit-content
      white-space nowrap
      background none
      backdrop-filter none
      position unset
      margin-left 0px
      *
        color var(--text-primary-color)

  .small-control
    width calc(330px * 3)
    position fixed
    bottom calc(28px * 3)
    left 50%
    margin-left calc(-165px * 3)
    z-index 10
    svg
      color var(--text-primary-color)
    div
      width calc(48px * 3)
      height calc(48px * 3)
</style>