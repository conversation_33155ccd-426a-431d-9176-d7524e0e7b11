<template>
  <div class="order-control-btn">
    <span v-if="orderedSongNum">{{ orderedSongNum }}</span>
    <img src="https://qncweb.ktvsky.com/20211217/vadd/29d2ace4f4d4fb15458aece5c73d0220.png"/>
    <p>已点</p>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'OrderControlBtn',
  setup() {
    const store = useStore()
    const orderedSongNum = computed(() => store.state.orderedList.length)

    return {
      orderedSongNum
    }
  }
}
</script>

<style lang="stylus" scoped>
.order-control-btn
  z-index 11
  position fixed
  bottom 70px
  right 80px
  width: 120px
  height: 120px
  background: rgba(56, 58, 62, 1)
  border-radius: 14px
  display flex
  flex-direction: column
  align-items: center
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.2), 0px 0px 30px 0px rgba(0, 0, 0, 0.8)
  @media screen and (max-width: 1200px)
    bottom 80px
  span
    display flex
    justify-content: center
    align-items: center
    width 36px
    height 36px
    background #5558FF
    border-radius 50%
    position absolute
    top 10px
    right 24px
    color #fff
    font-size 20px
  img
    width 54px
    height 58px
    margin 13px 0px 0px 0px
  p
    font-size 24px
    color: rgba(255, 255, 255, 0.8)
</style>
