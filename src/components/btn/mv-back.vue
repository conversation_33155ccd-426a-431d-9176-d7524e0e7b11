<template>
  <div
    class="backbtn"
    :class="{ full: isFullMode }"
    @click="handleBack"
    v-log="{
      event_type: 'click',
      event_name: '6007',
      event_data: {
        str1: '欢唱页',
        str2: '播控浮层',
        str3: '返回首页',
        str4: 'click',
      }
    }"
  >
    <svg class="backbtn-icon" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M19.6739 3.70714L20.381 3.00003L18.9668 1.58582L18.2597 2.29292L1.28914 19.2635L0.582031 19.9706L1.28914 20.6777L18.2597 37.6483L18.9668 38.3554L20.381 36.9412L19.6739 36.234L4.4399 21H36.9981V19H4.38102L19.6739 3.70714Z" fill="white" style="fill:white;fill-opacity:1;"/>
    </svg>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { computed } from 'vue';
import { useStore } from 'vuex';
import { TSNativeInstance } from '@/packages/TSJsbridge';

// 使用 Vue Router
const route = useRoute();
// 使用 Vuex Store
const store = useStore();

// 计算属性，获取 mvIsHide 的状态
const mvIsHide = computed(() => store.state.mvIsHide);
const isFullMode = computed(() => store.state.mvMode.mode !== 'mv' && store.state.currentSize === 'full');
// 处理返回按钮的逻辑
const handleBack = async (payload) => {
  try {
    console.log('handleBack: Start handling back button'); // 新增的console信息

    const isMvHidden = mvIsHide.value;
    console.log('handleBack: Current mvIsHide status', isMvHidden); // 新增的console信息

    // 如果当前路由是 home 且 mvIsHide 为 true，调用退出方法
    if (route.name === 'home' && isMvHidden) {
      console.log('handleBack: Current route is home and mvIsHide is true, calling exit method'); // 新增的console信息
      TSNativeInstance.exit();
      return;
    }

    // 提交 mutation 更新 mvIsHide 状态
    console.log('handleBack: Updating mvIsHide status to true'); // 新增的console信息
    store.commit('UPDATE_MV_ISHIDE', true);

    console.log('handleBack: Back button handling completed'); // 新增的console信息
  } catch (error) {
    console.log('handleBack error', error, payload);
  }
};
</script>

<style lang="stylus" scoped>
.backbtn
  position absolute
  top 40px
  left 40px
  z-index 12
  width 130px
  height 130px
  display flex
  justify-content center
  align-items center
  background: rgba(30, 31, 33, 0.7);
  border-radius 10px
  backdrop-filter: blur(30px)
  &.full
    top 70px
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width calc(72px * 1.48) !important
    height calc(72px * 1.48) !important
  &-icon
    width 40px
    height auto
</style>
