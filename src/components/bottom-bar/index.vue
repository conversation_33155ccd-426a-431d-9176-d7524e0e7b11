<template>
  <div>
    <div
      v-if="isShowBottomBar"
      class="bottom-bar"
      @click="handleClickBottombar"
    >

      <div class="left">
        <div class="bottom-bar-btn flex-center">
          <DynamicIcon
            v-show="paused"
            @click.stop="handleVideoPlay"
            name="bottom-play"
            v-log="{
              event_type: 'click',
              event_name: '6012',
              event_data: {
                str1: '通用',
                str2: '歌词条',
                str3: '暂停/播放',
                str4: 'click',
                str5: playingSong.songid
              }
            }"
          />
          <DynamicIcon
            v-show="!paused"
            @click.stop="handleVideoPause"
            name="bottom-pause"
            v-log="{
              event_type: 'click',
              event_name: '6012',
              event_data: {
                str1: '通用',
                str2: '歌词条',
                str3: '切歌',
                str4: 'click',
                str5: playingSong.songid
              }
            }"
          />
        </div>
        <div class="bottom-bar-btn flex-center">
          <DynamicIcon @click.stop="handleVideoNext" name="bottom-next" />
        </div>
      </div>
      <div class="bottom-bar-lyc theme-color">
        <div class="bottom-bar-lyc-item">
          <p
            class="bottom-bar-lyc-item-p"
            v-if="orderedListNum == 0 || !lycListNum || currIrcIndex === -1"
            :class="currentSize === 'small' && 'ellipsis'"
          >
            <i v-if="orderedListNum!=0">{{ formattedMusicName }}</i>
            <i v-if="orderedListNum!=0">{{ formattedSinger }}</i>
            <i v-if="currentPlayNoLrc">暂无匹配歌词</i>
            <i v-if="orderedListNum==0">还没有要演唱的歌曲哦！快去点歌吧！</i>
          </p>
          <p v-else class="bottom-bar-lyc-item-p" ref="lyricP">
            <span
              v-for="(text, ind) in lycTxt.irc" 
              :key="ind + lycTxt.t"
              :ref="el => { if (el) lyricSpans[ind] = el }"
              :class="{ 'scroll-animation': isAnimate(text) }"
              :style="isAnimate(text) ? 
                `animation-duration: ${lycTxt.t}s;` : ''"
            >
              {{ text.trim() }}
            </span>
          </p>
        </div>
      </div>
      <div
        v-if="currentSize == 'small'"
        class="small-ordered flex-center"
        @click.stop="handleToOrdered"
      >
        <DynamicIcon name="small-ordered" />
        <span>{{ orderedListNum }}</span>
      </div>
      <div
        v-else
        class="flag font-bold flex-center"
        @click.stop="handleClickBottombar('mv')"
      >MV</div>
    </div>
    <OperationSus 
      v-if="isShowOperationSus"
      :class="botPos"
      @close="handleCloseOperationSus"
    />
  </div>
</template>

<script setup>
import { computed, ref, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import eventBus from '@/utils/event-bus'
import useVip from '@/composables/useVip'
import useSongItem from '@/composables/useSongItem'
import useDownload from '@/composables/useDownload';
import OperationSus from '@/components/operation'
import Toast from '@/utils/toast'
import getComponentLrcData from '@/components/mv/utils'
import { sendLog } from '@/directives/v-log/log'
import { debounce } from 'lodash'
import get from 'lodash/get'

const store = useStore()
const route = useRoute()
const router = useRouter()
const { showVipQrcode, isVipUser } = useVip()
const { orderSong, validSong } = useSongItem()
const { getIsLocalSong } = useDownload();

const mvIsHide = computed(() => store.state.mvIsHide)
const paused = computed(() => store.state.videoPaused || orderedListNum.value === 0)
const videoPlayerHistory = computed(() => store.state.videoPlayerHistory)
const playingSong = computed(() => get(store.state.orderedList, '[0]', {}))
const orderedList = computed(() => store.state.orderedList)
const orderedListNum = computed(() => orderedList.value.length)
const orderedSongIdMap = computed(() => store.state.orderedSongIdMap)
const isMiniVisible = computed(() => store.state.isMiniVisible)
const currentPlayNoLrc = computed(() => store.state.songItemLrcEmpty)
const currentSize = computed(() => store.state.currentSize);

const isShowBottomBar = computed(() => 
  mvIsHide.value && (!isMiniVisible.value || currentSize.value === 'small')
)
const isShowOperationSus = ref(true)

const lyricP = ref(null);
const lyricSpans = ref([]);

const botPos = computed(() => {
  if (isShowBottomBar.value) {
    if (route.name === 'search') return 'bot-pos-search'
    if (route.name === 'singer') return 'bot-pos-singer'
    return 'bot-pos'
  }
  return ''
})

const lycList = computed(() => {
  return getComponentLrcData(store.state.songItemLrc)
})
const lycListNum = computed(() => lycList.value.length)
const currIrcIndex = computed(() => store.state.currIrcIndex)
const lycTxt = computed(() => {
  if (lycListNum.value) {
    if (currIrcIndex.value) {
      return lycList.value[currIrcIndex.value]
    }
    return lycList.value[0]
  }
  return []
})

const handleToOrdered = () => {
  router.push({
    name: 'smallOrdered'
  })
}

const truncateString = (str, maxLength) => str.length > maxLength ? str.substring(0, maxLength) + '...' : str;

const formattedMusicName = computed(() => {
  if (currentSize.value === 'small') {
    return orderedList.value[0].music_name;
  }
  return truncateString(orderedList.value[0].music_name, 15);
});
const isAnimate = (text) => {
  return currentSize.value === 'small' &&  text.trim().length > 13
}
const formattedSinger = computed(() => {
  if (currentSize.value === 'small') {
    return orderedList.value[0].singer;
  }
  return truncateString(orderedList.value[0].singer, 10);
});

const checkMvIsPlaying = async () => {
  if (!isVipUser.value) {
    const isLocal = await getIsLocalSong(orderedList.value[0])
    if (orderedList.value[0].is_vip && !isLocal) {
      showVipQrcode()
      return false
    }
  }
  if (!videoPlayerHistory.value.songItem.songid) {
    if (
      Object.keys(orderedSongIdMap.value).length
    ) {
      orderSong(orderedList.value[0], {
        position: 'recovery',
        isPushOrdered: false,
      })
      return true
    }
    Toast('请点播歌曲')
    return false
  }
  return true
}

const handleVideoPlay = async () => {
  if (orderedListNum.value == 0) {
    Toast('请点播歌曲')
    return
  }
  const canPlay = await checkMvIsPlaying()
  if (canPlay) {
    eventBus.emit('video-control-resume')
  }
  sendLog({
    event_type: '10000~50000',
    event_name: 10096,
    event_data: {
      str1: '任意页',
      str2: '歌词条',
      str3: '播放',
      str4: 'click',
    },
  })
}

const handleVideoPause = () => {
  if (orderedListNum.value == 0) {
    Toast('请点播歌曲')
    return
  }
  checkMvIsPlaying()
  eventBus.emit('handle-video-pause')
  sendLog({
    event_type: '10000~50000',
    event_name: 10096,
    event_data: {
      str1: '任意页',
      str2: '歌词条',
      str3: '暂停',
      str4: 'click',
    },
  })
}

const handleVideoNext = debounce(() => {
  if (orderedListNum.value == 0) {
    Toast('请点播歌曲')
    return
  }
  eventBus.emit('handle-video-next', 'bottom-bar')
  sendLog({
    event_type: '10000~50000',
    event_name: 10097,
    event_data: {
      str1: '任意页',
      str2: '歌词条',
      str3: '切歌',
      str4: 'click',
    },
  })
}, 1000, { leading: true, trailing: false })

const handleCloseOperationSus = () => {
  isShowOperationSus.value = false
}

const handleClickBottombar = async (payload) => {
  if (orderedListNum.value == 0) {
    return
  }
  const canPlay = await checkMvIsPlaying()
  console.log('handleClickBottombar', canPlay)
  if (!canPlay) return

  store.commit('UPDATE_MV_ISHIDE', false)
  if (payload === 'mv') {
    sendLog({
      event_type: 'click',
      event_name: '6012',
      event_data: {
        str1: '通用',
        str2: '歌词条',
        str3: '点击MV图标进入 MV 页面',
        str4: 'click',
        str5: playingSong.value.songid
      }
    })
    return
  }
  sendLog({
    event_type: 'click',
    event_name: '6012',
    event_data: {
      str1: '通用',
      str2: '歌词条',
      str3: '点击歌词条进入 MV页面',
      str4: 'click',
      str5: playingSong.value.songid
    }
  })
}

watch([isShowBottomBar, route], ([val]) => {
  if (val) {
    sendLog({
      event_type: 'show',
      event_name: '6012',
      event_data: {
        str1: '通用',
        str2: '歌词条',
        str3: '展示歌词条',
        str4: 'show',
      }
    })
    // sendLog({
    //   event_type: '10000~50000',
    //   event_name: 10095,
    //   event_data: {
    //     str1: '任意页',
    //     str2: '歌词条',
    //     str3: '展示',
    //     str4: 'show',
    //   },
    // })
  }
}, { deep: true })

watch(currIrcIndex, async (newIndex) => {
  const currentLyric = lycList.value[newIndex];

  if (currentLyric?.irc?.length > 50) {
    const delayTime = currentLyric.delayTime[currentLyric.delayTime.length - 2];

    setTimeout(() => {
      const lastSpan = lyricSpans.value[lyricSpans.value.length - 1];
      lastSpan.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'start'
      });
    }, delayTime);
  }
}, {
  deep: true,
  immediate: true,
});

</script>

<style lang="stylus" scoped>
--bottom-bar-height = 125px;

.bottom-bar
  width 100%
  height --bottom-bar-height
  display flex
  align-items center
  padding 0 286px 0
  position fixed
  bottom 0
  left 0
  z-index 9
  background: var(--bottom-lyric-bar-background)
  backdrop-filter: blur(200px)

  // 三分之一屏
  @media (max-width: 700px)
    height calc(83px * 3)
    padding-right calc(69px * 3)
    padding-left calc(118px * 3)

  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    height calc(83px * 1.48) !important

  *
    color var(--song-name-color)
  .svg-icon
    width 48px
    height 48px

    // 三分之一屏 & 三分之二屏
    @media (max-width: 900px)
      width calc(24px * var(--scale)) !important
      height calc(24px * var(--scale)) !important


  .left
    position absolute
    left 42px
    display flex
    margin-right calc(10px * 3)
    // 三分之一屏
    @media (max-width: 700px)
      left 0px
      margin-right 0px
  .flag
    position absolute
    top 50%
    transform translateY(-50%)
    right 71px
    height 40px
    padding 0 10px
    border-radius 8px
    background: var(--bottom-lyric-bar-flag-background)
    font-size 22px
    color var(--bottom-lyric-bar-flag-color)

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      height calc(20px * 1.48) !important
  &-btn
    width --bottom-bar-height
    height --bottom-bar-height

    // 三分之一屏 & 三分之二屏
    @media (max-width: 900px)
      width calc(54px * var(--scale)) !important
      height calc(54px * var(--scale)) !important

      .svg-icon
        width calc(24px * var(--scale)) !important
        height calc(24px * var(--scale)) !important

    &:last-child
      margin-left 24px

      // 三分之一屏
      @media (max-width: 700px)
        margin-left 0px
  &-img
    width 44px
    height 44px
  &-lyc
    flex 1
    height 100%
    display flex
    align-items center
    justify-content center
    overflow hidden
    // 三分之一屏
    @media (max-width: 700px)
      margin 0 -15px
    &-item
      width 100%
      height 100%
      display flex
      align-items center
      justify-content center
      
      &-p
        font-size calc(24px * var(--scale))
        overflow-x scroll
        white-space nowrap
        span
          font-size calc(24px * var(--scale))
        // 三分之一屏
        @media (max-width: 700px)
          font-size var(--font-size-large)
          max-width 100%
          span
            font-size var(--font-size-large)
          i
            font-size var(--font-size-large)
        i
          margin-left 20px
          
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          font-size calc(20px * var(--scale)) !important
          span
            font-size calc(20px * var(--scale)) !important
.bot-pos
  bottom 138px
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    bottom 90px !important
.bot-pos-search
  bottom 140px !important
.bot-pos-singer
  bottom 140px !important

.small-ordered
  width calc(54px * 3)
  height calc(54px * 3)
  position absolute
  right calc(5px * 3)
  top calc(15px * 3)

  .svg-icon
    width calc(24px * 3)
    height calc(24px * 3)

  span
    position absolute
    right calc(2px * 3)
    top calc(6px * 3)
    min-width calc(24px * 3)
    height calc(16px * 3)
    line-height calc(16px * 3)
    background: #DBAE6A;
    font-size calc(11px * 3)
    padding 0 calc(5px * 3)
    color  #342611;
    border-radius calc(22px * 3)
    text-align center
    font-weight 700

.scroll-animation
  animation: scroll-right 5s linear 1

@keyframes scroll-right
  0%
    transform: translateX(100%) // 从右侧开始
  100%
    transform: translateX(-100%) // 向左滚动到完全消失
</style>