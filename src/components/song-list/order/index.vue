<template>
  <div
    v-if="orderedList.length"
    class="song-list"
    :class="[from]"
  >
    <RecycleScroller
      :items="orderedList"
      :item-size="tableItemSize"
      key-field="_i"
      v-slot="{ item }"
    >
      <SongItem
        from="ordered"
        :index="item._index"
        :key="item._i"
        :songItem="item"
        :isLandscape="isLandscape"
        @order="handleOrder"
        @stickTop="handleStickTop"
        @delete="handleDelete"
        @singer-click="handleSingerClick"
      ></SongItem>
    </RecycleScroller>
  </div>
  <div class="empty" v-else>
    <DynamicIcon name="empty" useTheme="true" />
    <p>暂无歌曲，快去点歌吧～</p>
  </div>
</template>3

<script setup>
import { getCurrentInstance, onMounted, ref, defineEmits, defineProps, computed, toRefs } from 'vue'
import SongItem from '../item.vue'
import { useStore } from 'vuex'
import useOrder from '@/composables/useOrder'
import { sendLog } from '@/directives/v-log/log'
import get from 'lodash/get'
import { checkLandscapeOrPortrait } from '@/utils/device'
import eventBus from '@/utils/event-bus';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import useDownload from '@/composables/useDownload';
import useVip from '@/composables/useVip';
import { RecycleScroller } from 'vue-virtual-scroller'

const { getIsLocalSong } = useDownload();
const { showVipQrcode, isVipUser } = useVip()

const props = defineProps({
  from: {
    type: String,
    default: '',
  }
})
const { from } = toRefs(props);
const emit = defineEmits(['singer-click'])
const store = useStore()
let isLandscape = ref(false)

const { orderSong, stickSongToTop, deleteSong, orderedList } = useOrder()

const currentSize = computed(() => store.state.currentSize);
const isLogin = computed(() => !!store.state.userInfo.unionid)

const handleOrder = async (songItem, index) => {
  try {
    sendLog({
      event_type: 'click',
      event_name: from.value === 'mvOrder' ? 6007 : 6012,
      event_data: {
        str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
        str2: from.value === 'mvOrder' ? '已点' : '已点/已唱弹窗',
        str3: '已点-点击歌曲',
        str4: 'click',
      },
    });

    // 提前拦截，不做后续处理
    if (!isVipUser.value && songItem.is_vip) {
      const isLocal = await getIsLocalSong(songItem)
      if (!isLocal) {
        const frValue = from.value === 'mvOrder' ? (isLogin.value ? 1831 : 1830) : (isLogin.value ? 1812 : 1811);
        const logDataStr1 = from.value === 'mvOrder' ? 'MV 页面' : '全局';
        const logDataStr2 = from.value === 'mvOrder' ? '快速点歌-已点' : '已点';

        showVipQrcode({
          fr: frValue,
          logData: {
            str1: logDataStr1,
            str2: logDataStr2,
            str3: '点击 VIP 歌曲',
            str4: 'click',
            str7: songItem.songid,
          },
          isLogin: isLogin.value,
        })
        return
      }
    }

    if (from.value === 'mvOrder' && index === 0) {
      return;
    }

    if (from.value !== 'mvOrder') {
      if (currentSize.value !== 'small') {
        eventBus.emit('close-order-song-control-popup');
      }
      store.commit('UPDATE_IS_SING_STATUS', true);
      store.dispatch('searchTips/updateIsShowSingTips', false); // 关闭tips弹窗
      
      if (index === 0) {
        store.commit('UPDATE_MV_ISHIDE', false)
        return
      }
    }
    await store.commit('UPDATE_CURR_IYRIC_INDEX', -1)

    orderSong(songItem, index);
  } catch (error) {
    console.error('处理歌曲排序时发生错误:', error);
  }
};

const handleStickTop = (songitem, index) => {
  stickSongToTop(index)
  sendLog({
    event_type: 'click',
    event_name: from.value === 'mvOrder' ? 6007 : 6012,
    event_data: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: from.value === 'mvOrder' ? '已点' : '已点/已唱弹窗',
      str3: '已点-置顶歌曲',
      str4: 'click',
    },
  });
  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: 10059,
  //   event_data: {
  //     str1: '已点',
  //     str2: '已点',
  //     str3: '置顶歌曲',
  //     str4: 'click',
  //   },
  // })
}

const handleDelete = (index) => {
  sendLog({
    event_type: 'click',
    event_name: from.value === 'mvOrder' ? 6007 : 6012,
    event_data: {
      str1: '欢唱页',
      str2: '已点',
      str3: '已点-删除歌曲',
      str4: 'click',
      str5: orderedList.value[index].songid
    },
  });
  deleteSong(index)

  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: 10060,
  //   event_data: {
  //     str1: '已点',
  //     str2: '已点',
  //     str3: '删除歌曲',
  //     str4: 'click',
  //   },
  // })
}

const handleSingerClick = (v) => {
  emit('singer-click', v)

  sendLog({
    event_type: 'click',
    event_name: from.value === 'mvOrder' ? 6007 : 6012,
    event_data: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: from.value === 'mvOrder' ? '已点' : '已点/已唱弹窗',
      str3: '已点-点击歌手名称',
      str4: 'click',
    },
  });
}

onMounted(async () => {
  isLandscape.value = checkLandscapeOrPortrait() === 'landscape'
})

//数据行实际高度
const tableItemSize = ref(
  currentSize.value === 'small' 
    ? 85 
    : currentSize.value === 'medium' 
      ? 78 
      : (document.documentElement.clientWidth || document.body.clientWidth) * 140 / 1920
);
</script>

<style lang="stylus" scoped>
.song-list
  flex 1
  padding 0 48px
  overflow hidden
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    :deep(.song-item)
      height calc(72px * 1.48) !important
      .left
        .name
          margin-bottom calc(6px * 1.48) !important
.empty
  flex 0.9
  p
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      font-size calc(16px * 1.48) !important
</style>
