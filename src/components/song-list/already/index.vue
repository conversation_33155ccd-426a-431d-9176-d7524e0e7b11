<template>
  <div
    class="song-list"
    v-if="alreadyList.length"
  >
    <RecycleScroller
      :items="alreadyList"
      :item-size="tableItemSize"
      key-field="_i"
      v-slot="{ item }"
    >
      <SongItem
        :index="item._index"
        :songItem="item"
        :isLandscape="isLandscape"
        @order="handleOrder"
        @stickTop="handleStickTop"
        @delete="handleDelete"
        @singer-click="handleSingerClick"
        @addOrdered="handleAddOrdered"
      ></SongItem>
    </RecycleScroller>
    <p v-if="alreadyList.length > 80" class="tip">已唱歌曲仅保留近期唱过的 99 首歌曲</p>
  </div>
  <div class="empty" v-else>
    <DynamicIcon name="empty" useTheme="true" />
    <p>暂无歌曲，快去点歌吧～</p>
  </div>
</template>

<script setup>
import { onMounted, ref, defineEmits, defineProps, toRefs, computed } from 'vue'
import SongItem from '../item.vue'
import useAlready from '@/composables/useAlready'
import { sendLog } from '@/directives/v-log/log'
import { checkLandscapeOrPortrait } from '@/utils/device'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { RecycleScroller } from 'vue-virtual-scroller'
import eventBus from '@/utils/event-bus'
import { useStore } from 'vuex'
import useDownload from '@/composables/useDownload';
import useVip from '@/composables/useVip';

const emit = defineEmits(['singer-click'])
const props = defineProps({
  from: {
    type: String,
    default: '',
  },
})

const store = useStore()
const { getIsLocalSong } = useDownload();
const { showVipQrcode, isVipUser } = useVip()

let isLandscape = ref(false)

const { from } = toRefs(props);
const { orderSong, alreadyList, stickSongToTop, deleteSong } = useAlready()

const isLogin = computed(() => !!store.state.userInfo.unionid)
const hasOrderedSongs = computed(() => {
  return store.state.orderedList.length > 0;
});

function showVipQrcodeWithLog(songItem) {
  showVipQrcode({
    fr: from.value === 'mvOrder' ? (isLogin.value ? 1833 : 1832) : (isLogin.value ? 1814 : 1813),
    logData: {
      str1: from.value === 'mvOrder' ? 'MV 页面' : '全局',
      str2: from.value === 'mvOrder' ? '快速点歌-已唱' : '已唱',
      str3: '点击 VIP 歌曲',
      str4: 'click',
      str7: songItem.songid,
    },
    isLogin: isLogin.value,
  });
}

const currentSize = computed(() => store.state.currentSize);
const handleOrder = async (songItem) => {
  // 提前拦截，不做后续处理
  console.log(isVipUser.value, songItem.is_vip)
  if (!isVipUser.value && songItem.is_vip) {
    const isLocal = await getIsLocalSong(songItem)
    if (!isLocal) {
      showVipQrcodeWithLog(songItem);
      return
    }
  }

  orderSong(songItem, {
    immediate: true,
    isOpenFullScreen: true,
    orderLog: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: '已点/已唱弹窗',
      str3: '已唱-立即开唱',
    }
  })

  eventBus.emit('close-order-song-control-popup');

  sendLog({
    event_type: 'click',
    event_name: from.value === 'mvOrder' ? 6007 : 6012,
    event_data: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: from.value === 'mvOrder' ? '已唱' : '已点/已唱弹窗',
      str3: '已唱-立即开唱',
      str4: 'click',
    },
  });
}

const handleAddOrdered = async (songItem) => {
  if (!isVipUser.value && songItem.is_vip) {
    const isLocal = await getIsLocalSong(songItem)
    if (!isLocal) {
      showVipQrcodeWithLog(songItem);
      return
    }
  }
  orderSong(songItem, {
    isPushOrdered: true,
    immediate: !hasOrderedSongs.value,
    orderLog: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: '已点/已唱弹窗',
      str3: '已唱-加入已点',
    },
  })

  sendLog({
    event_type: 'click',
    event_name: from.value === 'mvOrder' ? 6007 : 6012,
    event_data: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: from.value === 'mvOrder' ? '已唱' : '已点/已唱弹窗',
      str3: '已唱-点击歌曲',
      str4: 'click',
    },
  });
}

const handleStickTop = async(songItem) => {
  if (!isVipUser.value && songItem.is_vip) {
    const isLocal = await getIsLocalSong(songItem)
    if (!isLocal) {
      showVipQrcodeWithLog(songItem);
      return
    }
  }
  
  stickSongToTop(songItem, {
    orderLog: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: '已点/已唱弹窗',
      str3: '已唱-置顶歌曲',
    }
  })

  sendLog({
    event_type: 'click',
    event_name: from.value === 'mvOrder' ? 6007 : 6012,
    event_data: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: from.value === 'mvOrder' ? '已唱' : '已点/已唱弹窗',
      str3: '已唱-置顶歌曲',
      str4: 'click',
    },
  });
}

const handleDelete = (index) => {
  console.log('handleDelete', index)

  sendLog({
    event_type: 'click',
    event_name: from.value === 'mvOrder' ? 6007 : 6012,
    event_data: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: from.value === 'mvOrder' ? '已唱' : '已点/已唱弹窗',
      str3: '已唱-删除歌曲',
      str4: 'click',
      // str5: alreadyList.value[index].songid,
    },
  });
  
  deleteSong(index)
}

const handleSingerClick = (v) => {
  emit('singer-click', v)

  sendLog({
    event_type: 'click',
    event_name: from.value === 'mvOrder' ? 6007 : 6012,
    event_data: {
      str1: from.value === 'mvOrder' ? '欢唱页' : '通用',
      str2: from.value === 'mvOrder' ? '已唱' : '已点/已唱弹窗',
      str3: '已唱-点击歌手名称',
      str4: 'click',
    },
  });
}

onMounted(async () => {
  isLandscape.value = checkLandscapeOrPortrait() === 'landscape'
})

//数据行实际高度
const tableItemSize = ref(
  currentSize.value === 'small' 
    ? 85 
    : currentSize.value === 'medium' 
      ? 78 
      : (document.documentElement.clientWidth || document.body.clientWidth) * 140 / 1920
);
</script>

<style lang="stylus" scoped>
.song-list
  flex 1
  padding 0 48px 50px
  position relative
  overflow hidden
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    :deep(.song-item)
      height calc(72px * 1.48) !important
      .left
        .name
          margin-bottom calc(6px * 1.48) !important
  .tip
    line-height 54px
    color var(--text-secondary-color)
    font-size 24px
    text-align center
    position absolute
    width 100%
    bottom 0px
    left 0px

    // 三分之一屏
    @media (max-width: 700px)
      font-size var(--font-size-small)
      bottom -20px
.empty
  flex 0.9
  p
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      font-size calc(16px * 1.48) !important
</style>
