<template>
  <div
    class="song-item flex-between"
    :class="{
      ordered: isOrdered && from !== 'ordered',
      playing: isPlaying,
      first: from === 'ordered' && index == 0
    }"
    @click="handleOrder('parent')"
  >
    <div class="left">
      <h3 class="flex name">
        <span class="ellipsis">{{ songItem.music_name }}</span>
        <template v-if="themeClass === 'themeLight'">
          <img v-show="isPlaying && !videoPaused" src="./playing.webp" alt="">
        </template>
        <template v-else>
          <img v-show="isPlaying && !videoPaused" src="./playing-dark.webp" alt="">
        </template>
        <DynamicIcon v-show="isPlaying && videoPaused" name="playing" />
      </h3>
      <p class="desc flex">
        <DownloadStatus
          :state="songItem.downloadState"
          :progress="songItem.downloadProgress"
        ></DownloadStatus>
        <span
          v-if="songItem.singer"
          class="author flex"
          :class="singerCanClick && 'clickable'"
          @click="handleClickSinger"
        >
          <span class="ellipsis">{{ singerTxt }}</span>
          <DynamicIcon v-show="singerCanClick" name="right-arrow" />
        </span>
        <template v-if="!songItem.isAIMV">
          <span class="flag">{{ songItem.flag && songItem.flag.toString() }}</span>
          <img v-if="songItem.is_vip" class="vip" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" alt="">
          <img v-if="enableScoring && songItem.is_score" class="song-block-score" src="https://qncweb.ktvsky.com/20240205/other/151412e04f9df9e6bbc1a781ddd6ab7d.png" />
          <span
            v-if="showXiaomiFree"
            class="xiaomi flex-center"
          >
            <img src="https://qncweb.ktvsky.com/20241231/other/b66b6a18dd20b2425d2643dff4f3db5e.png" />
          </span>
        </template>
      </p>
    </div>
    <div class="control flex">
      <div
        @click.stop="handleOrder"
        v-show="from !== 'ordered' || index != 0"
        v-log="{
          event_type: 'click',
          event_name: '6012',
          event_data: {
            str1: '通用',
            str2: '歌曲',
            str3: '立即开唱',
            str4: 'click',
          }
        }"
      >
        <DynamicIcon name="mic1" />
      </div>
      <div v-show="from !== 'ordered' || index > 1" @click.stop="handleStickSongToTop">
        <DynamicIcon name="zhiding" />
      </div>
      <div
        v-if="!isPlaying"
        @click.stop="handleDelete"
      >
        <DynamicIcon name="delete" />
         <!-- <div class="svg-icon">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.4">
              <path d="M7.25391 1.06641C7.25391 0.514122 7.70162 0.0664062 8.25391 0.0664062H11.7441C12.2964 0.0664062 12.7441 0.514122 12.7441 1.06641C12.7441 1.61869 12.2964 2.06641 11.7441 2.06641H8.2539C7.70162 2.06641 7.25391 1.61869 7.25391 1.06641Z" fill="#1D1D1F"/>
              <path d="M1.33301 5C1.33301 4.44772 1.78072 4 2.33301 4H17.6663C18.2186 4 18.6663 4.44772 18.6663 5C18.6663 5.55228 18.2186 6 17.6663 6H2.33301C1.78072 6 1.33301 5.55228 1.33301 5Z" fill="#1D1D1F"/>
              <path d="M4.59578 5.85913C4.54647 5.49752 4.51893 5.28917 4.5113 5.13653C4.50899 5.09031 4.50934 5.0602 4.5101 5.04257C4.51441 5.0363 4.51943 5.03055 4.52506 5.02544C4.54243 5.0223 4.57221 5.01789 4.61832 5.01393C4.77059 5.00086 4.98075 5 5.3457 5H14.6536C15.0186 5 15.2288 5.00086 15.381 5.01393C15.4271 5.01789 15.4569 5.0223 15.4743 5.02544C15.4799 5.03055 15.4849 5.0363 15.4892 5.04257C15.49 5.0602 15.4904 5.09031 15.488 5.13653C15.4804 5.28917 15.4529 5.49752 15.4036 5.85912L13.8827 17.0121C13.8439 17.2965 13.8218 17.454 13.7973 17.5677C13.7902 17.6007 13.7844 17.6219 13.7807 17.6343C13.7755 17.6401 13.7695 17.6453 13.7631 17.6497C13.7503 17.6517 13.7285 17.6545 13.6949 17.6571C13.5789 17.666 13.4198 17.6667 13.1328 17.6667H6.86656C6.57952 17.6667 6.42046 17.666 6.30445 17.6571C6.27081 17.6545 6.24901 17.6517 6.2363 17.6497C6.22982 17.6453 6.22389 17.6401 6.21864 17.6342C6.21494 17.6219 6.20919 17.6007 6.20209 17.5677C6.17759 17.454 6.15542 17.2965 6.11664 17.0121L4.59578 5.85913Z" stroke="#1D1D1F" stroke-width="2"/>
              </g>
            </svg>

         </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs, computed, defineEmits, defineProps, inject } from 'vue'
import { useStore } from 'vuex'
import { sendLog } from '@/directives/v-log/log'
import split from 'lodash/split'
import { getSongInfo } from '@/service/singer'
import config from '@/config'
import DownloadStatus from '@/components/download-status/index.vue'

const props = defineProps({
  index: Number,
  songItem: {
    type: Object,
    default() {
      return {
        acc: '1',
        org: '2',
        flag: [],
        m3u8: {
          480: '',
          720: '',
          1080: '',
        },
        music_name: '',
        played: 0,
        singer: '',
        songid: 0,
      }
    }
  },
  isLandscape: {
    type: Boolean,
    default: false,
  },
  from: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['delete', 'stick-top', 'order', 'singer-click'])
const orderedSongIdMap = inject('orderedSongIdMap')

const { songItem, index, isLandscape, from } = toRefs(props)
const store = useStore()

const singerCanClick = computed(() => (!store.state.mvIsHide && isLandscape.value) || store.state.mvIsHide)
const enableScoring = config.enableScoring
const videoPaused = computed(() => store.state.videoPaused)
const currentSize = computed(() => store.state.currentSize);
const themeClass = computed(() => store.state.themeClass)
const isOrdered = computed(() => orderedSongIdMap.value[songItem.value.songid])
const isPlaying = computed(() => from.value === 'ordered' && index.value === 0)
const mvIsHide = computed(() => store.state.mvIsHide)
const showXiaomiFree = computed(() => {
  return songItem.value.singerid == 115281
});

const singerTxt = computed(() => {
  const singer = songItem.value.singer.replace(/,$/, '');
  if (currentSize.value === 'small' && (showXiaomiFree.value || songItem.value.is_vip)) {
    if (from.value === 'ordered' && index.value == 0 ) return singer.substring(0, 10)
    return singer.substring(0, 3)
  }
  if (!mvIsHide.value && currentSize.value === 'medium' && (showXiaomiFree.value || songItem.value.is_vip)) {
    if (from.value === 'ordered' && index.value == 0 ) return singer.substring(0, 10)
    return singer.substring(0, 3)
  }
  return currentSize.value === 'small' ? (singer.length > 7 ? `${singer.substring(0, 7)}...` : singer) : (singer.length > 10 ? `${singer.substring(0, 10)}...` : singer);
})

const handleDelete = () => {
  emit('delete', index.value)
}

const handleStickSongToTop = () => {
  emit('stick-top', songItem.value, index.value)
}

const handleOrder = (payload) => {
  if (payload === 'parent' && from.value !== 'ordered') {
    emit('addOrdered', songItem.value)
    return
  }
  emit('order', songItem.value, index.value)
}

const handleClickSinger = async(e) => {
  if (!singerCanClick.value) return
  e.stopPropagation();
  sendLog({
    event_type: '10000~50000',
    event_name: 10109,
    event_data: {
      str1: '任意点歌页',
      str2: '歌曲列表',
      str3: '点击任意歌手',
      str4: 'click',
    },
  })
  if (!songItem.value.singerid) {
    const { song_info = {} } = await getSongInfo(songItem.value.songid)
    emit('singer-click', {
      singer: split(songItem.value.singer, ',')[0],
      singerhead: song_info.singer_head,
      singerid: song_info.singerid,
    })
    return
  }
  emit('singer-click', {
    singer: split(songItem.value.singer, ',')[0],
    singerhead: songItem.value.singer_head,
    singerid: songItem.value.singerid,
  })
}
</script>

<style lang="stylus" scoped>
.song-item
  width 100%
  height 140px
  position: relative

  // 三分之一屏
  @media (max-width: 700px)
    height calc(85px * 3)
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    height calc(77px * 1.48) !important

  &.first
    .left
      width 100%
      max-width 100% !important
  &::after
    content: ''
    position: absolute
    bottom: 0px
    left: 0
    width: 100%
    height: 2px
    background-color: var(--border-color)
  .flag
    height auto !important
  .left
    max-width 68%
    .name
      margin-bottom 0px
    .desc
      margin-top 0px
    .author
      max-width 60%
    h3
      line-height 42px

      .svg-icon, img
        width calc(20px * var(--scale))
        height calc(20px * var(--scale))
        margin-left calc(10px * var(--scale))

      // 三分之一屏
      @media (max-width: 700px)
        line-height unset
        margin-bottom calc(4px * 3) !important
      // img
      //   width 30px
      //   margin-left 8px
        
      //   // 三分之一屏
      //   @media (max-width: 700px)
      //     width calc(20px * 3)
    p
      line-height 36px
      margin-top 5px
      font-size var(--font-size-small)
  .control
    div
      width 64px
      height 64px
      margin-left 40px

      &:active
        background-color: var(--pressed-color);
        
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        zoom 1.2
      // 三分之一屏
      @media (max-width: 700px)
        width calc(32px * 3)
        height calc(32px * 3)
    div:first-child
      margin-left 0px
    .svg-icon
      color var(--song-control-color)

  &.playing
    .left
      .name
        color var(--highlight-color)
      .name span, .desc *
        color var(--highlight-color)

      .flag:after
        content ""
        background var(--highlight-color)

.mvOrder
  // 三分之二屏
  .song-item:not(.first)
    @media (max-width: 900px) and (min-width: 701px)  
      .left
        max-width 50% !important 
      // .control
      //   div
      //     width calc(36px * 1.48) !important 
      //     height calc(36px * 1.48) !important 
      // // .svg-icon
      // //   width 100% !important 
      // //   height 100% !important 
</style>