<template>
  <div
    v-show="showMobileOrder"
    class="global-mobile-order"
    :style="{
      opacity: isVisible ? 1 : 0
    }"
  >
    <div
      :style="styleObject"
      v-draggable="{ 
        threshold: 30,
        smallScreenThreshold: 20,
        mediumScreenThreshold: 30
      }"
      @click="togglePopup"
      ref="draggable"
      class="draggable"
    >
      <DynamicIcon name="global-mobile" />
      <p>手机点歌</p>
    </div>
    <div v-if="showPopup" class="popup flex-center">
      <div class="popup-content">
        <div class="bg">
          <img v-show="currentSize !=='small'" src="https://qncweb.ktvsky.com/20241223/other/5ff22572775f77137d445b74d6aefbe7.png" alt="">
          <img v-show="currentSize =='small'" :src="smallWxBG" alt="">
        </div>
        <div v-show="currentSize =='small'" class="title flex-center">
          <svg width="120" height="77" viewBox="0 0 120 77" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M28.59 18.73V22.27H17.19V26.14C17.19 27.68 16.93 28.76 16.41 29.38C15.91 30.02 15.02 30.34 13.74 30.34C12.22 30.34 10.71 30.28 9.21 30.16L8.43 26.53C9.91 26.71 11.13 26.8 12.09 26.8C12.55 26.8 12.88 26.66 13.08 26.38C13.28 26.08 13.38 25.59 13.38 24.91V22.27H1.47V18.73H13.38V15.19H3.57V11.62H13.38V7.99C9.64 8.23 6.33 8.4 3.45 8.5L3.21 4.93C6.27 4.85 10.03 4.65 14.49 4.33C18.97 4.01 22.78 3.65 25.92 3.25L26.43 6.85C24.07 7.13 20.99 7.42 17.19 7.72V11.62H26.49V15.19H17.19V18.73H28.59ZM40.8 20.2L40.08 19.48C39.46 18.82 38.94 18.29 38.52 17.89V30.64H35.1V18.97C34.24 20.67 33.25 22.28 32.13 23.8L30.54 20.2C31.26 19.22 32.04 17.93 32.88 16.33C33.74 14.71 34.46 13.09 35.04 11.47H31.32V7.96H35.1V2.92H38.52V7.96H41.94V11.47H38.52V13.69C39.1 14.19 39.8 14.83 40.62 15.61C41.44 16.39 42.14 17.07 42.72 17.65L40.8 20.2ZM58.89 20.8C58.89 21.5 58.87 22.45 58.83 23.65C58.81 24.83 58.78 25.83 58.74 26.65C58.7 27.55 58.58 28.23 58.38 28.69C58.18 29.15 57.85 29.47 57.39 29.65C56.95 29.83 56.32 29.92 55.5 29.92H53.82C52.72 29.92 51.94 29.67 51.48 29.17C51.04 28.67 50.82 27.77 50.82 26.47V8.02H46.8V15.01C46.8 17.95 46.7 20.26 46.5 21.94C46.32 23.6 45.93 25.11 45.33 26.47C44.75 27.81 43.84 29.27 42.6 30.85L39.45 28.39C40.59 27.05 41.42 25.86 41.94 24.82C42.46 23.76 42.8 22.53 42.96 21.13C43.14 19.71 43.23 17.66 43.23 14.98V4.3H54.33V25.24C54.33 25.72 54.36 26.03 54.42 26.17C54.48 26.31 54.62 26.38 54.84 26.38H55.47C55.67 26.38 55.8 26.3 55.86 26.14C55.94 25.96 55.99 25.64 56.01 25.18C56.07 23.74 56.1 22.07 56.1 20.17L58.89 20.8ZM76.05 10.99H85.77V21.73H64.23V10.99H72.27V2.95H76.05V5.14H87.93V8.74H76.05V10.99ZM67.86 18.25H82.14V14.59H67.86V18.25ZM85.47 22.42C85.99 23.26 86.58 24.3 87.24 25.54C87.9 26.76 88.46 27.87 88.92 28.87L85.8 30.49C85.08 28.93 83.87 26.66 82.17 23.68L85.47 22.42ZM61.11 29.29C61.73 28.23 62.31 27.08 62.85 25.84C63.41 24.6 63.81 23.53 64.05 22.63L67.44 23.44C67.12 24.56 66.68 25.8 66.12 27.16C65.58 28.5 65.05 29.7 64.53 30.76L61.11 29.29ZM78.87 22.93C79.25 23.81 79.66 24.86 80.1 26.08C80.56 27.3 80.91 28.32 81.15 29.14L77.76 30.04C77.54 29.2 77.22 28.21 76.8 27.07C76.4 25.91 76.01 24.87 75.63 23.95L78.87 22.93ZM72.63 23.41C72.87 24.25 73.13 25.31 73.41 26.59C73.69 27.85 73.9 28.9 74.04 29.74L70.44 30.43C70.32 29.45 70.15 28.38 69.93 27.22C69.71 26.04 69.48 24.99 69.24 24.07L72.63 23.41ZM105.6 13.06C106.5 11.44 107.28 9.78 107.94 8.08C108.62 6.38 109.2 4.5 109.68 2.44L112.8 3.01C112.54 4.19 112.2 5.4 111.78 6.64H118.77V9.88C118.67 10.3 118.42 11.15 118.02 12.43C117.6 13.85 117.35 14.7 117.27 14.98L114.06 14.29L114.69 12.37C115.09 11.23 115.36 10.38 115.5 9.82H110.67C109.77 12.12 108.88 13.95 108 15.31L105.6 13.06ZM105.27 14.71H102.03V6.76H91.56V3.73H106.95V6.76H105.27V14.71ZM100.5 8.05V14.14H92.4V8.05H100.5ZM97.59 10.33H95.07V11.89H97.59V10.33ZM117 30.61C114.78 28.33 113.13 25.68 112.05 22.66C111.01 25.54 109.28 28.22 106.86 30.7L105.09 28.54C104.89 29.28 104.49 29.78 103.89 30.04C103.29 30.3 102.34 30.43 101.04 30.43C100.48 30.43 99.68 30.39 98.64 30.31L98.04 27.37C98.88 27.49 99.72 27.55 100.56 27.55C101.16 27.55 101.56 27.44 101.76 27.22C101.96 26.98 102.06 26.52 102.06 25.84V18.43H91.11V15.46H107.37V18.43H105.3V26.65C105.3 27.03 105.29 27.31 105.27 27.49C107.11 25.59 108.43 23.61 109.23 21.55C110.03 19.47 110.45 17.07 110.49 14.35L110.52 11.44H113.43C113.43 12.56 113.42 13.44 113.4 14.08C113.4 14.5 113.38 15.09 113.34 15.85C113.6 18.29 114.19 20.43 115.11 22.27C116.05 24.11 117.4 25.85 119.16 27.49L117 30.61ZM92.34 19.84H100.44V26.47H92.34V19.84ZM95.01 24.07H97.56V22.09H95.01V24.07Z" fill="url(#paint0_linear_65_4155)"/>
            <path d="M10.59 55.93C10.07 55.43 9.27 54.75 8.19 53.89V72.37H4.89V44.74H8.19V49.93C10.23 51.35 11.63 52.39 12.39 53.05L10.59 55.93ZM20.79 61.45C21.51 63.17 22.53 64.67 23.85 65.95C25.19 67.23 26.92 68.34 29.04 69.28L27.24 72.49C23.26 70.59 20.37 67.74 18.57 63.94C17.91 65.52 16.91 67.01 15.57 68.41C14.25 69.79 12.5 71.2 10.32 72.64L8.25 69.49C10.35 68.25 12.02 66.99 13.26 65.71C14.5 64.43 15.37 63.01 15.87 61.45H9.84V58.03H16.38V57.94V52.42H11.7V49.03H16.38L16.41 44.74H19.83V49.03H26.46V58.03H28.71V61.45H20.79ZM0.48 60.43C0.8 59.01 1.1 57.29 1.38 55.27C1.66 53.23 1.86 51.34 1.98 49.6L4.62 49.9C4.48 51.62 4.26 53.56 3.96 55.72C3.68 57.88 3.42 59.61 3.18 60.91L0.48 60.43ZM19.77 58.03H23.01V52.42H19.83L19.77 57.7V58.03ZM46.65 51.01C47.21 54.89 48.51 58.33 50.55 61.33C52.61 64.33 55.41 67.04 58.95 69.46L56.34 72.76C53.5 70.5 51.16 68.22 49.32 65.92C47.48 63.62 46.06 61.19 45.06 58.63C44.12 61.19 42.72 63.61 40.86 65.89C39.02 68.15 36.68 70.33 33.84 72.43L31.14 69.34C34.08 67.3 36.39 65.26 38.07 63.22C39.77 61.16 40.99 58.91 41.73 56.47C42.49 54.03 42.89 51.17 42.93 47.89C42.97 47.17 42.99 46.23 42.99 45.07H46.74L46.71 49.33C46.71 49.73 46.69 50.29 46.65 51.01ZM61.26 55.63H88.74V59.77H61.26V55.63ZM118.35 57.88H107.49V64.48C108.73 63.78 109.87 62.98 110.91 62.08C111.97 61.16 112.98 60.1 113.94 58.9L116.79 61.39C114.85 63.55 112.83 65.34 110.73 66.76C108.65 68.16 106.23 69.34 103.47 70.3C100.71 71.26 97.34 72.09 93.36 72.79L92.1 69.19C95.5 68.59 98.38 67.94 100.74 67.24C103.1 66.54 105.16 65.73 106.92 64.81H103.71V57.88H91.62V54.34H95.52V46.99H99.3V54.34H103.41V44.8H107.19V47.86H116.28V51.31H107.19V54.34H118.35V57.88ZM101.22 61.09C99.56 62.87 97.04 64.99 93.66 67.45L91.56 64.18C94.28 62.38 96.66 60.46 98.7 58.42L101.22 61.09Z" fill="white"/>
            <path d="M10.59 55.93C10.07 55.43 9.27 54.75 8.19 53.89V72.37H4.89V44.74H8.19V49.93C10.23 51.35 11.63 52.39 12.39 53.05L10.59 55.93ZM20.79 61.45C21.51 63.17 22.53 64.67 23.85 65.95C25.19 67.23 26.92 68.34 29.04 69.28L27.24 72.49C23.26 70.59 20.37 67.74 18.57 63.94C17.91 65.52 16.91 67.01 15.57 68.41C14.25 69.79 12.5 71.2 10.32 72.64L8.25 69.49C10.35 68.25 12.02 66.99 13.26 65.71C14.5 64.43 15.37 63.01 15.87 61.45H9.84V58.03H16.38V57.94V52.42H11.7V49.03H16.38L16.41 44.74H19.83V49.03H26.46V58.03H28.71V61.45H20.79ZM0.48 60.43C0.8 59.01 1.1 57.29 1.38 55.27C1.66 53.23 1.86 51.34 1.98 49.6L4.62 49.9C4.48 51.62 4.26 53.56 3.96 55.72C3.68 57.88 3.42 59.61 3.18 60.91L0.48 60.43ZM19.77 58.03H23.01V52.42H19.83L19.77 57.7V58.03ZM46.65 51.01C47.21 54.89 48.51 58.33 50.55 61.33C52.61 64.33 55.41 67.04 58.95 69.46L56.34 72.76C53.5 70.5 51.16 68.22 49.32 65.92C47.48 63.62 46.06 61.19 45.06 58.63C44.12 61.19 42.72 63.61 40.86 65.89C39.02 68.15 36.68 70.33 33.84 72.43L31.14 69.34C34.08 67.3 36.39 65.26 38.07 63.22C39.77 61.16 40.99 58.91 41.73 56.47C42.49 54.03 42.89 51.17 42.93 47.89C42.97 47.17 42.99 46.23 42.99 45.07H46.74L46.71 49.33C46.71 49.73 46.69 50.29 46.65 51.01ZM61.26 55.63H88.74V59.77H61.26V55.63ZM118.35 57.88H107.49V64.48C108.73 63.78 109.87 62.98 110.91 62.08C111.97 61.16 112.98 60.1 113.94 58.9L116.79 61.39C114.85 63.55 112.83 65.34 110.73 66.76C108.65 68.16 106.23 69.34 103.47 70.3C100.71 71.26 97.34 72.09 93.36 72.79L92.1 69.19C95.5 68.59 98.38 67.94 100.74 67.24C103.1 66.54 105.16 65.73 106.92 64.81H103.71V57.88H91.62V54.34H95.52V46.99H99.3V54.34H103.41V44.8H107.19V47.86H116.28V51.31H107.19V54.34H118.35V57.88ZM101.22 61.09C99.56 62.87 97.04 64.99 93.66 67.45L91.56 64.18C94.28 62.38 96.66 60.46 98.7 58.42L101.22 61.09Z" fill="url(#paint1_linear_65_4155)"/>
            <path d="M10.59 55.93C10.07 55.43 9.27 54.75 8.19 53.89V72.37H4.89V44.74H8.19V49.93C10.23 51.35 11.63 52.39 12.39 53.05L10.59 55.93ZM20.79 61.45C21.51 63.17 22.53 64.67 23.85 65.95C25.19 67.23 26.92 68.34 29.04 69.28L27.24 72.49C23.26 70.59 20.37 67.74 18.57 63.94C17.91 65.52 16.91 67.01 15.57 68.41C14.25 69.79 12.5 71.2 10.32 72.64L8.25 69.49C10.35 68.25 12.02 66.99 13.26 65.71C14.5 64.43 15.37 63.01 15.87 61.45H9.84V58.03H16.38V57.94V52.42H11.7V49.03H16.38L16.41 44.74H19.83V49.03H26.46V58.03H28.71V61.45H20.79ZM0.48 60.43C0.8 59.01 1.1 57.29 1.38 55.27C1.66 53.23 1.86 51.34 1.98 49.6L4.62 49.9C4.48 51.62 4.26 53.56 3.96 55.72C3.68 57.88 3.42 59.61 3.18 60.91L0.48 60.43ZM19.77 58.03H23.01V52.42H19.83L19.77 57.7V58.03ZM46.65 51.01C47.21 54.89 48.51 58.33 50.55 61.33C52.61 64.33 55.41 67.04 58.95 69.46L56.34 72.76C53.5 70.5 51.16 68.22 49.32 65.92C47.48 63.62 46.06 61.19 45.06 58.63C44.12 61.19 42.72 63.61 40.86 65.89C39.02 68.15 36.68 70.33 33.84 72.43L31.14 69.34C34.08 67.3 36.39 65.26 38.07 63.22C39.77 61.16 40.99 58.91 41.73 56.47C42.49 54.03 42.89 51.17 42.93 47.89C42.97 47.17 42.99 46.23 42.99 45.07H46.74L46.71 49.33C46.71 49.73 46.69 50.29 46.65 51.01ZM61.26 55.63H88.74V59.77H61.26V55.63ZM118.35 57.88H107.49V64.48C108.73 63.78 109.87 62.98 110.91 62.08C111.97 61.16 112.98 60.1 113.94 58.9L116.79 61.39C114.85 63.55 112.83 65.34 110.73 66.76C108.65 68.16 106.23 69.34 103.47 70.3C100.71 71.26 97.34 72.09 93.36 72.79L92.1 69.19C95.5 68.59 98.38 67.94 100.74 67.24C103.1 66.54 105.16 65.73 106.92 64.81H103.71V57.88H91.62V54.34H95.52V46.99H99.3V54.34H103.41V44.8H107.19V47.86H116.28V51.31H107.19V54.34H118.35V57.88ZM101.22 61.09C99.56 62.87 97.04 64.99 93.66 67.45L91.56 64.18C94.28 62.38 96.66 60.46 98.7 58.42L101.22 61.09Z" fill="url(#paint2_linear_65_4155)"/>
            <defs>
            <linearGradient id="paint0_linear_65_4155" x1="1.21875" y1="17.5" x2="120" y2="17.5001" gradientUnits="userSpaceOnUse">
            <stop stop-color="#A554F8"/>
            <stop offset="0.979167" stop-color="#634AFF"/>
            </linearGradient>
            <linearGradient id="paint1_linear_65_4155" x1="1.21875" y1="59.5" x2="120" y2="59.5001" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E7CFFF"/>
            <stop offset="0.0677083" stop-color="#E7D0FF"/>
            <stop offset="0.979167" stop-color="#E6DCFF"/>
            </linearGradient>
            <linearGradient id="paint2_linear_65_4155" x1="1.21875" y1="59.5" x2="120" y2="59.5001" gradientUnits="userSpaceOnUse">
            <stop stop-color="#A554F8"/>
            <stop offset="0.979167" stop-color="#634AFF"/>
            </linearGradient>
            </defs>
          </svg>
        </div>
        <div class="qrcode">
          <img v-if="qrCodeURL" :src="qrCodeURL" />
          <div
            v-else
            class="popup-code-error flex-column"
            @click="handleClickRefresh"
          >
            <DynamicIcon name="refresh" />
            <p>网络异常</p>
            <p>点击刷新二维码</p>
          </div>
        </div>
        <DynamicIcon
          @click="togglePopup"
          name="close"
          class="close"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onBeforeUnmount, defineProps } from 'vue';
import useQRCode from '@/composables/useQRCode'
import { useStore } from 'vuex';
import { openDatabase, getItem } from '@/utils/IndexedDB';
import { cacheImages } from '@/constants'
import eventBus from '@/utils/event-bus'
import vDraggable from '@/directives/draggable'

const { getQRCodeURL } = useQRCode();
const store = useStore();

const draggable = ref(null);
const isDragging = ref(false);
const startY = ref(0);
const startX = ref(0); // 新增
const startTop = ref(0);
const startLeft = ref(0); // 新增
const showPopup = ref(false);
const qrCodeURL = ref('')
const smallWxBG = ref(cacheImages.smallWxBG)
const isVisible = ref(false)

const props = defineProps({
  canShow: {
    type: Boolean,
    default: false
  }
})

const phone_control_qr = computed(() => store.state.base.phone_control_qr);
const currentSize = computed(() => store.state.currentSize);
const mvIsHide = computed(() => store.state.mvIsHide);
const showMobileOrder = computed(() => {
  return mvIsHide.value && props.canShow
})

const styleObject = reactive({
  top: currentSize.value === 'small' ? '500px' : (currentSize.value === 'medium' ? '400px' : '693px'),
  left: currentSize.value === 'small' ? '14px' : '24px',
});

const handleShowMobileOrder = () => {
  isVisible.value = true
}

onMounted(async () => {
  eventBus.on('show-mobile-order', handleShowMobileOrder)

  if (draggable.value) {
    // styleObject.top = window.getComputedStyle(draggable.value).getPropertyValue('top');
    // styleObject.left = window.getComputedStyle(draggable.value).getPropertyValue('left');
    restPosition()
  }

  // 如果没有 phone_control_qr，重新调用 store.dispatch('base/getBaseInfo')
  if (!phone_control_qr.value) {
    await store.dispatch('base/getBaseInfo');
  }

  const qrCodeData = await getQRCodeURL(phone_control_qr.value);
  if (qrCodeData) {
    qrCodeURL.value = qrCodeData;
  }

  await openDatabase(); // 确保数据库已打开
  smallWxBG.value = await getItem('smallWxBG') || cacheImages.smallWxBG;

  // 添加窗口大小变化的监听
  window.addEventListener('resize', restPosition);
});

// 新增函数：更新 top 值
function restPosition() {
  styleObject.top  = currentSize.value === 'small' ? '73.9vh' : '70vh'; // 更新为 75vh
  styleObject.left = currentSize.value === 'small' ? '14px' : '24px'
}

// 在组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', restPosition);
});

function startDrag(event) {
  isDragging.value = true;
  startY.value = event.touches[0].clientY;
  startX.value = event.touches[0].clientX; // 新增
  startTop.value = parseInt(window.getComputedStyle(draggable.value).top);
  startLeft.value = parseInt(window.getComputedStyle(draggable.value).left); // 新增
}

function drag(event) {
  if (isDragging.value) {
    const dy = event.touches[0].clientY - startY.value;
    const dx = event.touches[0].clientX - startX.value; // 新增
    let newTop = startTop.value + dy;
    let newLeft = startLeft.value + dx; // 新增

    // 限制top在屏幕内
    const draggableHeight = draggable.value.offsetHeight + 90;
    const screenHeight = window.innerHeight;
    newTop = Math.max(0, Math.min(newTop, screenHeight - draggableHeight));

    // 限制left在屏幕内
    const draggableWidth = draggable.value.offsetWidth;
    const screenWidth = window.innerWidth;
    newLeft = Math.max(0, Math.min(newLeft, screenWidth - draggableWidth));

    styleObject.top = `${newTop}px`;
    styleObject.left = `${newLeft}px`; // 新增
  }
}

function stopDrag(event) {
  const num = currentSize.value === 'small' ? 20 : 30
  
  isDragging.value = false;
  const dragWidth = draggable.value.offsetWidth;
  const screenWidth = window.innerWidth;
  const currentLeft = parseInt(styleObject.left);
  const threshold = screenWidth / 2;

  if (currentLeft + dragWidth / 2 > threshold) {
    styleObject.left = `${screenWidth - dragWidth - num}px`;
  } else {
    styleObject.left = num + 'px';
  }
}

async function togglePopup() {
  showPopup.value = !showPopup.value;

  // 如果没有 phone_control_qr，重新调用 store.dispatch('base/getBaseInfo')
  if (!phone_control_qr.value) {
    await store.dispatch('base/getBaseInfo');
  }

  const qrCodeData = await getQRCodeURL(phone_control_qr.value);
  if (qrCodeData) {
    qrCodeURL.value = qrCodeData;
  }
}

const handleClickRefresh = async () => {
  await store.dispatch('base/getBaseInfo');

  const qrCodeData = await getQRCodeURL(phone_control_qr.value);
  if (qrCodeData) {
    qrCodeURL.value = qrCodeData;
  }
}
</script>

<style lang="stylus" scoped>
.dark-theme
  .global-mobile-order
    --background rgba(31, 31, 32, 0.2);
    --popup-background rgba(34, 32, 44, 1);

.global-mobile-order
  --background rgba(255, 255, 255, 0.2)
  --popup-background linear-gradient(180deg, rgba(255, 255, 255, 0.99) 0%, #E2E5ED 38.12%)
  
  .draggable
    display flex
    flex-direction column
    justify-content center
    align-items center
    background-color: var(--background)
    backdrop-filter: blur(100px)
    border: 2px solid var(--border-color);
    width calc(86px * var(--scale))
    height calc(86px * var(--scale))
    position fixed
    left 30px
    userSelect none
    border-radius 24px
    text-align center
    z-index 100

    // 三分之一屏
    @media (max-width: 700px)
      width calc(74px * 3)
      height calc(74px * 3)
      border-radius calc(14px * 3)

      p
        font-size calc(13px * 3)

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(86px * var(--scale)) !important
      height calc(86px * var(--scale)) !important
      border-radius calc(16px * var(--scale)) !important

    .svg-icon
      width 42px
      height 42px
      margin-bottom 6px

      // 三分之一屏
      @media (max-width: 700px)
        width calc(24px * 3)
        height calc(24px * 3)

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(28px * var(--scale)) !important
        height calc(28px * var(--scale)) !important

  .popup
    position: fixed;
    width 100vw
    height 100vh
    left 0
    top 0
    background rgba(0, 0, 0, 0.2)
    border-radius 0px
    z-index 101

  .popup-content
    text-align: center
    width 1000px
    height 675px
    border-radius  32px
    background var(--popup-background)
    position relative
    overflow hidden
    // 三分之一屏
    @media (max-width: 700px)
      width calc(328px * 3)
      height calc(388px * 3)
      border-radius calc(16px * 3)
      .title
        margin calc(30px * 3) auto 0
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      zoom 1.5
    .qrcode
      width 214px
      height 214px
      position absolute
      top 320px
      left 50%
      margin-left -107px

      // 三分之一屏
      @media (max-width: 700px)
        width calc(140px * 3)
        height calc(140px * 3)
        top calc(178px * 3)
        margin-left calc(-64px * 3)
    .bg
      width 100%
      height 100%
      position absolute
      left 0
      top 0px

    .close
      position absolute
      width 36px
      height 36px
      top 30px
      right 30px

      // 三分之一屏
      @media (max-width: 700px)
        width calc(24px * 3)
        height calc(24px * 3)

.popup-code-error
  background: #0000001A;
  width 100%
  height 100%
  justify-content center
  align-items center

  .svg-icon
    width calc(50px * var(--scale))
    height calc(50px * var(--scale))
    color: #00000066;
    margin-bottom calc(10px * var(--scale))
  
  p
    font-size var(--font-size-small)
    color: #00000066;
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      font-size var(--font-size-tiny) !important

</style>