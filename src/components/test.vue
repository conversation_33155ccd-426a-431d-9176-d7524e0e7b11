<template>
  <div>
    <button @click="connectWebSocket">Connect WebSocket</button>
  </div>
</template>

<script>
import { ref } from 'vue';

export default {
  name: 'WebSocketComponent',
  setup() {
    const connectWebSocket = () => {
      const socket = new WebSocket('ws://localhost:8080');

      socket.onopen = (event) => {
          console.log('WebSocket connection opened.');
      };

      socket.onclose = (event) => {
          console.log('WebSocket connection closed.');
          console.log('socketClose code:', event.code);
          console.log('socketClose reason:', event.reason);
      };

      socket.onerror = (error) => {
          console.error('WebSocket error:', error);
      };
    };

    return {
      connectWebSocket,
    };
  },
};
</script>

<style scoped>
/* 你的样式代码 */
</style>