<template>
  <ControlPopup ref="root">
    <template #control-popup-plugin>
      <div class="order-song-control-panel flex-column">
        <div class="header flex-center">
          <div class="tab flex-between">
            <div
              class="tab-item flex-center"
              v-for="(tab, index) in tabList"
              :key="index"
              :class="{ actived: curTab.name === tab.name }"
              @click="handleChangeTab(tab)"
              v-log="{
                event_type: 'click',
                event_name: '6012',
                event_data: {
                  str1: '通用',
                  str2: '已点/已唱弹窗',
                  str3: `${tab.keyword}tab`,
                  str4: 'click',
                }
              }"
            >
              {{ tab.text }}
            </div>
          </div>
          <div class="close" @click="handleClose('close')">
            <DynamicIcon name="close" />
          </div>
        </div>
        <component
          :is="currentComponent"
          @singer-click="handleClickSinger"
        ></component>
      </div>
    </template>
  </ControlPopup>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import eventBus from '@/utils/event-bus'
import ControlPopup from '@/components/control-popup/index.vue'
import AlreadySongList from '@/components/song-list/already/index.vue'
import OrderSongList from '@/components/song-list/order/index.vue'
import { sendLog } from '@/directives/v-log/log'

const store = useStore()
const router = useRouter()
const route = useRoute()

const orderedListNum = computed(() => store.state.orderedList.length)
const alreadyListNum = computed(() => store.state.alreadyList.length)
const currentSize = computed(() => store.state.currentSize);

const tabList = computed(() => {
  return [
    {
      name: 'ordered',
      text: `已点(${orderedListNum.value > 99 ? '99' : orderedListNum.value})`,
      keyword: '已点',
    },
    {
      name: 'already',
      text: `已唱(${alreadyListNum.value > 99 ? '99' : alreadyListNum.value})`,
      keyword: '已唱',
    },
  ]
})

let curTab = ref(tabList.value[0])

const currentComponent = computed(() => {
  return curTab.value.name === 'ordered' ? OrderSongList : AlreadySongList;
});

// const themeClass = computed(() => store.state.themeClass)

const handleChangeTab = (tab) => {
  if (curTab.value.name === tab.name) return
  curTab.value = tab

  // if (tab.text === '已唱') {
  //   sendLog({
  //     event_type: '10000~50000',
  //     event_name: 10061,
  //     event_data: {
  //       str1: '已点',
  //       str2: '已唱',
  //       str3: '进入已唱',
  //       str4: 'click',
  //     },
  //   })
  // }
}

const root = ref(null)

const handleShow = () => {
  if (currentSize.value === 'small') return
  
  root.value.show()
}

const handleClose = (payload) => {
  if (currentSize.value === 'small') return

  root.value.close()
  sendLog({
    event_type: 'click',
    event_name: '6012',
    event_data: {
      str1: '通用',
      str2: '已点/已唱弹窗',
      str3: '关闭弹窗',
      str4: 'click',
      str5: payload === 'close' ? 1 : 2
    }
  })
}

const handleClickSinger = ({ singer, singerhead, singerid }) => {
  router.push({
    name: 'songList',
    query: {
      name: singer,
      image: singerhead,
      singerid: singerid,
    },
  })
  if (currentSize.value === 'small') return

  root.value.close()
}

onMounted(() => {
  eventBus.on('show-order-song-control-popup', handleShow)
  eventBus.on('close-order-song-control-popup', handleClose)
})

onBeforeUnmount(() => {
  eventBus.off('show-order-song-control-popup', handleShow)
  eventBus.off('close-order-song-control-popup', handleClose)
})

watch(route, val => {
  if (document.querySelector('.control-popup-plugin')) {
    handleClose()
  }
})

watch(currentSize, (newSize) => {
  try {
    if (newSize === 'small') {
      root.value.close();
    }
  } catch (error) {
    console.error('Error in currentSize watcher:', error);
    // 可以根据需要处理错误，例如显示错误提示或记录日志
  }
});

</script>
<style lang="stylus" scoped>
.order-song-control-panel
  padding 30px
  width 1000px
  height 800px
  background var(--popup-background-color)
  overflow hidden
  border-radius 32px
  padding 0 !important
    
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width calc(578px * 1.48) !important
    height calc(533px * 1.48) !important
    border-radius calc(24px * 1.48) !important
  .header
    position relative
    padding 0px
    height 148px

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      margin calc(16px * 1.48) 0 !important

    .close
      position absolute
      top 50%
      right 46px
      transform translateY(-50%)
      .svg-icon
        width 40px
        height 40px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(26px * 1.48) !important
          height calc(26px * 1.48) !important
    .tab
      width 506px
      height 100px
      background var(--capsule-background)
      border-radius 28px
      padding 0 8px

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(300px * 1.48) !important
        height calc(58px * 1.48) !important
        border-radius calc(10px * 1.48) !important
        padding 0 calc(4px * 1.48) !important
      &-item
        width 240px
        height 84px
        color var(--text-tertiary-color)

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(146px * 1.48) !important
          height calc(50px * 1.48) !important
          font-size var(--font-size-large) !important
        &.actived
          color var(--capsule-highlight-color)
          background var(--capsule-highlight-background)
          border-radius 20px
  :deep(.song-item)
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      height calc(81px * 1.48) !important
      .left
        h3
          line-height calc(25px * 1.48) !important
</style>
