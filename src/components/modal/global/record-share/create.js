import { createComponent } from '@/components/modal/utils/index.js';
import MicVue from './index.vue';

export default function useMicOnline(globalProps = {}, globalSlots = {}) {

  return {
    show(props = globalProps, slots = globalSlots) {
      const forceProps = {
        programmatic: true,
        lockScroll: true,
        isFullPage: true,
        active: true,
        canCancel: false,
        ...props,
      }

      const propsData = Object.assign({
        onConfirm: () => {},
        onCancel: () => {}
      }, globalProps, props, forceProps);
      const container = document.body;

      const mergedSlots = Object.assign({}, globalSlots, slots);
      const instance = createComponent(MicVue, propsData, container, mergedSlots);

      return {
        hide: instance.refs.root.hide
      }
    },
  }
}
