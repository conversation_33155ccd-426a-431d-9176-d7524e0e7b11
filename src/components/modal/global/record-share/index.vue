<template>
  <CommonModal ref="root">
    <div class="miconline-modal-content">
      <img @click="handleCloseModal" class="close" :src="imgs[themeClass].close"/>
      <h3>歌曲分享</h3>
      <div class="code">
        <img :src="qrCodeData" alt="">
      </div>
      <p>扫描二维码分享歌曲</p>
      <p>《{{info.music_name}}》- {{info.singer}}</p>
      <!-- <p>{{ info.content }}</p> -->
    </div>
  </CommonModal>
</template>
<script>
import { ref, toRefs, onBeforeMount, computed } from 'vue'
import CommonModal from '@/components/modal/common/component.vue'
import useQRCode from '@/composables/useQRCode'
import store from '@/store'

export default {
  name: 'MicOnline',
  props: ['info'],
  components: {
    CommonModal,
  },
  setup(props) {
    const root = ref(null)
    const { info } = toRefs(props)
    const { getQRCodeURL } = useQRCode()
    const qrCodeData = ref('')

    const imgs = {
      themeDark: {
        close: require('@/assets/close_dark.png'),
      },
      themeLight: {
        close: require('@/assets/close.png'),
      },
      themeSystem: {
        close: require('@/assets/close_dark.png'),
      },
    }

    const themeClass = computed(() => store.state.themeClass)

    const getQrcode = async () => {
      qrCodeData.value = await getQRCodeURL(info.value.qr)
    }

    const handleCloseModal = () => {
      root.value.hide()
    }

    onBeforeMount(getQrcode)

    return {
      root,
      qrCodeData,
      handleCloseModal,
      imgs,
      themeClass,
    }
  }
}
</script>
<style lang="stylus" scoped>
.miconline-modal
  position fixed
  top 0
  right 0
  bottom 0
  left 0
  width 100vw
  height 100vh
  background rgba(0,0,0, 0.8)
  z-index 10
  display flex
  justify-content center
  align-items center
  &-content
    display flex
    flex-direction column
    align-items center
    padding: 60px 90px;
    position relative
    width: 820px;
    min-height: 296px;
    background: #1E1F21;
    box-shadow: 0px 20px 80px 0px rgba(0, 0, 0, 0.5)
    border-radius: 8px
    color rgba(255, 255, 255, 0.8)
    font-size 40px
    .close
      position absolute
      top 30px
      right 30px
      left unset!important
      width 40px
      height 40px
    .code
      width: 250px;
      height: 250px;
      border-radius: 4px;
      background #fff
      margin 50px auto
      img
        width 240px
        height 240px
        margin 5px
    p
      font-size 32px
      color: rgba(255, 255, 255, 0.50);
.theme-themeLight
  .miconline-modal-content
    background: linear-gradient(180deg, #FFFFFF 0%, #E1E5EE 38.12%);
    color rgba(29, 29, 31, 0.8)
    p
      color rgba(29, 29, 31, 0.5)
</style>