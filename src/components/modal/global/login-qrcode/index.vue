<template>
  <CommonModal
    ref="root"
  >
    <div
      class="login-modal-content"
    >
      <div v-show="currentSize !=='small'" class="background">
        <img v-show="themeClass === 'themeLight'" :src="loginWxBG1" alt="">
        <img v-show="themeClass === 'themeDark'" :src="loginWxBG2" alt="">
      </div>

      <div v-show="currentSize =='small'" class="background">
        <img :src="smallWxBG" alt="">
      </div>

      <div v-show="currentSize =='small'" class="title">
        <svg width="168" height="70" viewBox="0 0 168 70" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4.9 39.564C5.49733 40.1053 6.244 40.8613 7.14 41.832C8.05467 42.8027 8.80133 43.6427 9.38 44.352L6.86 46.788C6.31867 46.0973 5.58133 45.2387 4.648 44.212C3.73333 43.1853 2.97733 42.392 2.38 41.832L4.9 39.564ZM11.06 40.74H25.144V52.696H14.868V59.528C14.868 60.1067 14.9147 60.5267 15.008 60.788C15.12 61.0307 15.3067 61.1987 15.568 61.292C15.848 61.3853 16.3053 61.46 16.94 61.516C17.6493 61.572 18.3493 61.6 19.04 61.6C19.7493 61.6 20.4493 61.572 21.14 61.516C21.756 61.46 22.1947 61.3853 22.456 61.292C22.736 61.1987 22.9227 61.04 23.016 60.816C23.128 60.5733 23.2027 60.1813 23.24 59.64C23.2773 59.192 23.3053 58.632 23.324 57.96C23.3613 57.2693 23.38 56.7 23.38 56.252L26.824 56.98C26.7867 58.268 26.712 59.5467 26.6 60.816C26.5253 61.8987 26.3667 62.7013 26.124 63.224C25.9 63.7467 25.508 64.1293 24.948 64.372C24.4067 64.6147 23.576 64.7827 22.456 64.876C21.2053 64.9693 20.076 65.016 19.068 65.016C18.0787 65.016 16.9493 64.9693 15.68 64.876C14.5227 64.7827 13.6547 64.6147 13.076 64.372C12.4973 64.1293 12.0867 63.7187 11.844 63.14C11.62 62.5613 11.508 61.684 11.508 60.508V49.252H21.728V44.184H11.06V40.74ZM1.064 48.02H7.504V59.136C8.77333 58.1467 9.62267 57.4747 10.052 57.12L11.004 60.648C10.9107 60.704 10.332 61.1147 9.268 61.88C8.22267 62.6453 7.37333 63.3267 6.72 63.924C6.216 64.3533 5.86133 64.68 5.656 64.904L3.528 62.272C3.77067 61.992 3.92933 61.7307 4.004 61.488C4.07867 61.2453 4.116 60.8907 4.116 60.424V51.352H1.064V48.02ZM48.16 57.82C49.9147 59.1267 52.1733 60.284 54.936 61.292L53.312 64.4C50.904 63.3173 48.944 62.2533 47.432 61.208C45.9387 60.144 44.66 58.912 43.596 57.512V61.74C43.596 63.028 43.3627 63.9147 42.896 64.4C42.4293 64.8853 41.6733 65.1373 40.628 65.156C39.9373 65.1747 39.312 65.1653 38.752 65.128C38.2107 65.1093 37.7253 65.072 37.296 65.016L36.708 61.824C37.5853 61.936 38.416 61.992 39.2 61.992C39.6667 61.992 39.9747 61.908 40.124 61.74C40.2733 61.5533 40.348 61.1893 40.348 60.648V58.324C36.8387 60.6013 33.628 62.4307 30.716 63.812L29.12 60.816C31.304 59.92 33.5067 58.8653 35.728 57.652C35.0747 57.092 34.3093 56.476 33.432 55.804C32.5547 55.132 31.8267 54.6 31.248 54.208L32.9 52.08H29.484V49.168H47.74V47.544H32.536V44.94H47.74V43.26H31.752V40.32H51.044V49.168H54.656V52.08H50.848L52.64 53.956C51.576 55.1507 50.0827 56.4387 48.16 57.82ZM35.896 57.54C37.6693 56.5507 39.1533 55.636 40.348 54.796V52.08H33.432C34.7387 52.9013 36.1573 53.9187 37.688 55.132L35.896 57.54ZM43.596 52.08V52.472C44.1933 53.648 44.9493 54.7493 45.864 55.776C47.376 54.656 48.7107 53.424 49.868 52.08H43.596ZM67.872 40.74H81.088V51.436H67.872V40.74ZM65.912 61.348H57.988V41.832H65.912V61.348ZM77.952 44.884V43.372H70.98V44.884H77.952ZM62.888 45.248H61.124V57.96H62.888V45.248ZM70.98 47.264V48.776H77.952V47.264H70.98ZM67.172 65.268V52.976H81.928V65.24H78.736V64.148H70.224V65.268H67.172ZM78.736 57.4V55.72H70.224V57.4H78.736ZM70.224 59.836V61.6H78.736V59.836H70.224ZM90.3 46.676C89.4787 45.5933 88.1533 43.8947 86.324 41.58L88.844 39.536L92.988 44.548L90.3 46.676ZM103.74 47.012H93.884V43.708H103.74V39.592H107.1V43.708H110.292V47.012H107.1V55.748C107.1 56.9987 106.969 57.932 106.708 58.548C106.447 59.164 105.98 59.584 105.308 59.808C104.655 60.032 103.656 60.144 102.312 60.144C101.341 60.144 100.165 60.0787 98.784 59.948L98.084 56.7C99.3907 56.868 100.697 56.952 102.004 56.952C102.527 56.9333 102.909 56.868 103.152 56.756C103.395 56.644 103.553 56.448 103.628 56.168C103.703 55.888 103.74 55.4493 103.74 54.852V47.012ZM97.02 47.992L98.588 50.036C99.26 50.876 100.007 51.8373 100.828 52.92L98.224 54.88C97.496 53.7787 96.264 52.0987 94.528 49.84L97.02 47.992ZM98.756 61.432C101.145 61.5627 103.18 61.628 104.86 61.628C106.559 61.628 108.603 61.5627 110.992 61.432L110.348 64.68C108.463 64.8107 106.447 64.876 104.3 64.876C102.135 64.876 100.109 64.8107 98.224 64.68C96.7493 64.5867 95.5453 64.4187 94.612 64.176C93.6787 63.952 92.8667 63.588 92.176 63.084C91.504 62.58 90.8507 61.8613 90.216 60.928C89.6747 61.432 88.9747 62.1227 88.116 63C87.276 63.8773 86.6227 64.5867 86.156 65.128L84.84 61.46L88.564 58.044V51.856H85.036V48.552H91.896V58.016C92.344 58.8373 92.8293 59.472 93.352 59.92C93.8933 60.368 94.5747 60.7133 95.396 60.956C96.236 61.18 97.356 61.3387 98.756 61.432ZM138.264 43.288C138.245 45.976 138.171 48.9253 138.04 52.136C137.928 55.328 137.779 58.324 137.592 61.124C137.517 62.5427 137.172 63.532 136.556 64.092C135.959 64.652 134.997 64.9413 133.672 64.96C132.571 64.9787 131.189 64.932 129.528 64.82L128.912 61.376C130.256 61.5253 131.32 61.6 132.104 61.6C132.701 61.6 133.14 61.5533 133.42 61.46C133.7 61.3667 133.905 61.1707 134.036 60.872C134.185 60.5547 134.288 60.0507 134.344 59.36C134.68 54.8613 134.839 50.624 134.82 46.648H129.444C128.865 47.9733 128.268 49.168 127.652 50.232L124.712 48.496C125.515 47.1893 126.261 45.6587 126.952 43.904C127.661 42.1493 128.184 40.5347 128.52 39.06L131.852 39.788C131.591 40.852 131.236 42.0187 130.788 43.288H138.264ZM121.744 39.872C121.501 41.048 121.193 42.2707 120.82 43.54H124.684V64.372H114.128V43.54H117.152C117.581 42.1027 117.936 40.6747 118.216 39.256L121.744 39.872ZM117.376 51.632H121.408V46.844H117.376V51.632ZM130.704 58.072C130.051 57.0267 129.351 55.9813 128.604 54.936C127.876 53.8907 127.213 53.0133 126.616 52.304L129.136 50.372C130.275 51.7347 131.656 53.5733 133.28 55.888L130.704 58.072ZM117.376 54.936V61.012H121.408V54.936H117.376ZM154.56 49.056C155.4 47.544 156.128 45.9947 156.744 44.408C157.379 42.8213 157.92 41.0667 158.368 39.144L161.28 39.676C161.037 40.7773 160.72 41.9067 160.328 43.064H166.852V46.088C166.759 46.48 166.525 47.2733 166.152 48.468C165.76 49.7933 165.527 50.5867 165.452 50.848L162.456 50.204L163.044 48.412C163.417 47.348 163.669 46.5547 163.8 46.032H159.292C158.452 48.1787 157.621 49.8867 156.8 51.156L154.56 49.056ZM154.252 50.596H151.228V43.176H141.456V40.348H155.82V43.176H154.252V50.596ZM149.8 44.38V50.064H142.24V44.38H149.8ZM147.084 46.508H144.732V47.964H147.084V46.508ZM165.2 65.436C163.128 63.308 161.588 60.8347 160.58 58.016C159.609 60.704 157.995 63.2053 155.736 65.52L154.084 63.504C153.897 64.1947 153.524 64.6613 152.964 64.904C152.404 65.1467 151.517 65.268 150.304 65.268C149.781 65.268 149.035 65.2307 148.064 65.156L147.504 62.412C148.288 62.524 149.072 62.58 149.856 62.58C150.416 62.58 150.789 62.4773 150.976 62.272C151.163 62.048 151.256 61.6187 151.256 60.984V54.068H141.036V51.296H156.212V54.068H154.28V61.74C154.28 62.0947 154.271 62.356 154.252 62.524C155.969 60.7507 157.201 58.9027 157.948 56.98C158.695 55.0387 159.087 52.7987 159.124 50.26L159.152 47.544H161.868C161.868 48.5893 161.859 49.4107 161.84 50.008C161.84 50.4 161.821 50.9507 161.784 51.66C162.027 53.9373 162.577 55.9347 163.436 57.652C164.313 59.3693 165.573 60.9933 167.216 62.524L165.2 65.436ZM142.184 55.384H149.744V61.572H142.184V55.384ZM144.676 59.332H147.056V57.484H144.676V59.332Z" fill="white"/>
          <path d="M4.9 39.564C5.49733 40.1053 6.244 40.8613 7.14 41.832C8.05467 42.8027 8.80133 43.6427 9.38 44.352L6.86 46.788C6.31867 46.0973 5.58133 45.2387 4.648 44.212C3.73333 43.1853 2.97733 42.392 2.38 41.832L4.9 39.564ZM11.06 40.74H25.144V52.696H14.868V59.528C14.868 60.1067 14.9147 60.5267 15.008 60.788C15.12 61.0307 15.3067 61.1987 15.568 61.292C15.848 61.3853 16.3053 61.46 16.94 61.516C17.6493 61.572 18.3493 61.6 19.04 61.6C19.7493 61.6 20.4493 61.572 21.14 61.516C21.756 61.46 22.1947 61.3853 22.456 61.292C22.736 61.1987 22.9227 61.04 23.016 60.816C23.128 60.5733 23.2027 60.1813 23.24 59.64C23.2773 59.192 23.3053 58.632 23.324 57.96C23.3613 57.2693 23.38 56.7 23.38 56.252L26.824 56.98C26.7867 58.268 26.712 59.5467 26.6 60.816C26.5253 61.8987 26.3667 62.7013 26.124 63.224C25.9 63.7467 25.508 64.1293 24.948 64.372C24.4067 64.6147 23.576 64.7827 22.456 64.876C21.2053 64.9693 20.076 65.016 19.068 65.016C18.0787 65.016 16.9493 64.9693 15.68 64.876C14.5227 64.7827 13.6547 64.6147 13.076 64.372C12.4973 64.1293 12.0867 63.7187 11.844 63.14C11.62 62.5613 11.508 61.684 11.508 60.508V49.252H21.728V44.184H11.06V40.74ZM1.064 48.02H7.504V59.136C8.77333 58.1467 9.62267 57.4747 10.052 57.12L11.004 60.648C10.9107 60.704 10.332 61.1147 9.268 61.88C8.22267 62.6453 7.37333 63.3267 6.72 63.924C6.216 64.3533 5.86133 64.68 5.656 64.904L3.528 62.272C3.77067 61.992 3.92933 61.7307 4.004 61.488C4.07867 61.2453 4.116 60.8907 4.116 60.424V51.352H1.064V48.02ZM48.16 57.82C49.9147 59.1267 52.1733 60.284 54.936 61.292L53.312 64.4C50.904 63.3173 48.944 62.2533 47.432 61.208C45.9387 60.144 44.66 58.912 43.596 57.512V61.74C43.596 63.028 43.3627 63.9147 42.896 64.4C42.4293 64.8853 41.6733 65.1373 40.628 65.156C39.9373 65.1747 39.312 65.1653 38.752 65.128C38.2107 65.1093 37.7253 65.072 37.296 65.016L36.708 61.824C37.5853 61.936 38.416 61.992 39.2 61.992C39.6667 61.992 39.9747 61.908 40.124 61.74C40.2733 61.5533 40.348 61.1893 40.348 60.648V58.324C36.8387 60.6013 33.628 62.4307 30.716 63.812L29.12 60.816C31.304 59.92 33.5067 58.8653 35.728 57.652C35.0747 57.092 34.3093 56.476 33.432 55.804C32.5547 55.132 31.8267 54.6 31.248 54.208L32.9 52.08H29.484V49.168H47.74V47.544H32.536V44.94H47.74V43.26H31.752V40.32H51.044V49.168H54.656V52.08H50.848L52.64 53.956C51.576 55.1507 50.0827 56.4387 48.16 57.82ZM35.896 57.54C37.6693 56.5507 39.1533 55.636 40.348 54.796V52.08H33.432C34.7387 52.9013 36.1573 53.9187 37.688 55.132L35.896 57.54ZM43.596 52.08V52.472C44.1933 53.648 44.9493 54.7493 45.864 55.776C47.376 54.656 48.7107 53.424 49.868 52.08H43.596ZM67.872 40.74H81.088V51.436H67.872V40.74ZM65.912 61.348H57.988V41.832H65.912V61.348ZM77.952 44.884V43.372H70.98V44.884H77.952ZM62.888 45.248H61.124V57.96H62.888V45.248ZM70.98 47.264V48.776H77.952V47.264H70.98ZM67.172 65.268V52.976H81.928V65.24H78.736V64.148H70.224V65.268H67.172ZM78.736 57.4V55.72H70.224V57.4H78.736ZM70.224 59.836V61.6H78.736V59.836H70.224ZM90.3 46.676C89.4787 45.5933 88.1533 43.8947 86.324 41.58L88.844 39.536L92.988 44.548L90.3 46.676ZM103.74 47.012H93.884V43.708H103.74V39.592H107.1V43.708H110.292V47.012H107.1V55.748C107.1 56.9987 106.969 57.932 106.708 58.548C106.447 59.164 105.98 59.584 105.308 59.808C104.655 60.032 103.656 60.144 102.312 60.144C101.341 60.144 100.165 60.0787 98.784 59.948L98.084 56.7C99.3907 56.868 100.697 56.952 102.004 56.952C102.527 56.9333 102.909 56.868 103.152 56.756C103.395 56.644 103.553 56.448 103.628 56.168C103.703 55.888 103.74 55.4493 103.74 54.852V47.012ZM97.02 47.992L98.588 50.036C99.26 50.876 100.007 51.8373 100.828 52.92L98.224 54.88C97.496 53.7787 96.264 52.0987 94.528 49.84L97.02 47.992ZM98.756 61.432C101.145 61.5627 103.18 61.628 104.86 61.628C106.559 61.628 108.603 61.5627 110.992 61.432L110.348 64.68C108.463 64.8107 106.447 64.876 104.3 64.876C102.135 64.876 100.109 64.8107 98.224 64.68C96.7493 64.5867 95.5453 64.4187 94.612 64.176C93.6787 63.952 92.8667 63.588 92.176 63.084C91.504 62.58 90.8507 61.8613 90.216 60.928C89.6747 61.432 88.9747 62.1227 88.116 63C87.276 63.8773 86.6227 64.5867 86.156 65.128L84.84 61.46L88.564 58.044V51.856H85.036V48.552H91.896V58.016C92.344 58.8373 92.8293 59.472 93.352 59.92C93.8933 60.368 94.5747 60.7133 95.396 60.956C96.236 61.18 97.356 61.3387 98.756 61.432ZM138.264 43.288C138.245 45.976 138.171 48.9253 138.04 52.136C137.928 55.328 137.779 58.324 137.592 61.124C137.517 62.5427 137.172 63.532 136.556 64.092C135.959 64.652 134.997 64.9413 133.672 64.96C132.571 64.9787 131.189 64.932 129.528 64.82L128.912 61.376C130.256 61.5253 131.32 61.6 132.104 61.6C132.701 61.6 133.14 61.5533 133.42 61.46C133.7 61.3667 133.905 61.1707 134.036 60.872C134.185 60.5547 134.288 60.0507 134.344 59.36C134.68 54.8613 134.839 50.624 134.82 46.648H129.444C128.865 47.9733 128.268 49.168 127.652 50.232L124.712 48.496C125.515 47.1893 126.261 45.6587 126.952 43.904C127.661 42.1493 128.184 40.5347 128.52 39.06L131.852 39.788C131.591 40.852 131.236 42.0187 130.788 43.288H138.264ZM121.744 39.872C121.501 41.048 121.193 42.2707 120.82 43.54H124.684V64.372H114.128V43.54H117.152C117.581 42.1027 117.936 40.6747 118.216 39.256L121.744 39.872ZM117.376 51.632H121.408V46.844H117.376V51.632ZM130.704 58.072C130.051 57.0267 129.351 55.9813 128.604 54.936C127.876 53.8907 127.213 53.0133 126.616 52.304L129.136 50.372C130.275 51.7347 131.656 53.5733 133.28 55.888L130.704 58.072ZM117.376 54.936V61.012H121.408V54.936H117.376ZM154.56 49.056C155.4 47.544 156.128 45.9947 156.744 44.408C157.379 42.8213 157.92 41.0667 158.368 39.144L161.28 39.676C161.037 40.7773 160.72 41.9067 160.328 43.064H166.852V46.088C166.759 46.48 166.525 47.2733 166.152 48.468C165.76 49.7933 165.527 50.5867 165.452 50.848L162.456 50.204L163.044 48.412C163.417 47.348 163.669 46.5547 163.8 46.032H159.292C158.452 48.1787 157.621 49.8867 156.8 51.156L154.56 49.056ZM154.252 50.596H151.228V43.176H141.456V40.348H155.82V43.176H154.252V50.596ZM149.8 44.38V50.064H142.24V44.38H149.8ZM147.084 46.508H144.732V47.964H147.084V46.508ZM165.2 65.436C163.128 63.308 161.588 60.8347 160.58 58.016C159.609 60.704 157.995 63.2053 155.736 65.52L154.084 63.504C153.897 64.1947 153.524 64.6613 152.964 64.904C152.404 65.1467 151.517 65.268 150.304 65.268C149.781 65.268 149.035 65.2307 148.064 65.156L147.504 62.412C148.288 62.524 149.072 62.58 149.856 62.58C150.416 62.58 150.789 62.4773 150.976 62.272C151.163 62.048 151.256 61.6187 151.256 60.984V54.068H141.036V51.296H156.212V54.068H154.28V61.74C154.28 62.0947 154.271 62.356 154.252 62.524C155.969 60.7507 157.201 58.9027 157.948 56.98C158.695 55.0387 159.087 52.7987 159.124 50.26L159.152 47.544H161.868C161.868 48.5893 161.859 49.4107 161.84 50.008C161.84 50.4 161.821 50.9507 161.784 51.66C162.027 53.9373 162.577 55.9347 163.436 57.652C164.313 59.3693 165.573 60.9933 167.216 62.524L165.2 65.436ZM142.184 55.384H149.744V61.572H142.184V55.384ZM144.676 59.332H147.056V57.484H144.676V59.332Z" fill="url(#paint0_linear_65_4185)"/>
          <path d="M4.9 39.564C5.49733 40.1053 6.244 40.8613 7.14 41.832C8.05467 42.8027 8.80133 43.6427 9.38 44.352L6.86 46.788C6.31867 46.0973 5.58133 45.2387 4.648 44.212C3.73333 43.1853 2.97733 42.392 2.38 41.832L4.9 39.564ZM11.06 40.74H25.144V52.696H14.868V59.528C14.868 60.1067 14.9147 60.5267 15.008 60.788C15.12 61.0307 15.3067 61.1987 15.568 61.292C15.848 61.3853 16.3053 61.46 16.94 61.516C17.6493 61.572 18.3493 61.6 19.04 61.6C19.7493 61.6 20.4493 61.572 21.14 61.516C21.756 61.46 22.1947 61.3853 22.456 61.292C22.736 61.1987 22.9227 61.04 23.016 60.816C23.128 60.5733 23.2027 60.1813 23.24 59.64C23.2773 59.192 23.3053 58.632 23.324 57.96C23.3613 57.2693 23.38 56.7 23.38 56.252L26.824 56.98C26.7867 58.268 26.712 59.5467 26.6 60.816C26.5253 61.8987 26.3667 62.7013 26.124 63.224C25.9 63.7467 25.508 64.1293 24.948 64.372C24.4067 64.6147 23.576 64.7827 22.456 64.876C21.2053 64.9693 20.076 65.016 19.068 65.016C18.0787 65.016 16.9493 64.9693 15.68 64.876C14.5227 64.7827 13.6547 64.6147 13.076 64.372C12.4973 64.1293 12.0867 63.7187 11.844 63.14C11.62 62.5613 11.508 61.684 11.508 60.508V49.252H21.728V44.184H11.06V40.74ZM1.064 48.02H7.504V59.136C8.77333 58.1467 9.62267 57.4747 10.052 57.12L11.004 60.648C10.9107 60.704 10.332 61.1147 9.268 61.88C8.22267 62.6453 7.37333 63.3267 6.72 63.924C6.216 64.3533 5.86133 64.68 5.656 64.904L3.528 62.272C3.77067 61.992 3.92933 61.7307 4.004 61.488C4.07867 61.2453 4.116 60.8907 4.116 60.424V51.352H1.064V48.02ZM48.16 57.82C49.9147 59.1267 52.1733 60.284 54.936 61.292L53.312 64.4C50.904 63.3173 48.944 62.2533 47.432 61.208C45.9387 60.144 44.66 58.912 43.596 57.512V61.74C43.596 63.028 43.3627 63.9147 42.896 64.4C42.4293 64.8853 41.6733 65.1373 40.628 65.156C39.9373 65.1747 39.312 65.1653 38.752 65.128C38.2107 65.1093 37.7253 65.072 37.296 65.016L36.708 61.824C37.5853 61.936 38.416 61.992 39.2 61.992C39.6667 61.992 39.9747 61.908 40.124 61.74C40.2733 61.5533 40.348 61.1893 40.348 60.648V58.324C36.8387 60.6013 33.628 62.4307 30.716 63.812L29.12 60.816C31.304 59.92 33.5067 58.8653 35.728 57.652C35.0747 57.092 34.3093 56.476 33.432 55.804C32.5547 55.132 31.8267 54.6 31.248 54.208L32.9 52.08H29.484V49.168H47.74V47.544H32.536V44.94H47.74V43.26H31.752V40.32H51.044V49.168H54.656V52.08H50.848L52.64 53.956C51.576 55.1507 50.0827 56.4387 48.16 57.82ZM35.896 57.54C37.6693 56.5507 39.1533 55.636 40.348 54.796V52.08H33.432C34.7387 52.9013 36.1573 53.9187 37.688 55.132L35.896 57.54ZM43.596 52.08V52.472C44.1933 53.648 44.9493 54.7493 45.864 55.776C47.376 54.656 48.7107 53.424 49.868 52.08H43.596ZM67.872 40.74H81.088V51.436H67.872V40.74ZM65.912 61.348H57.988V41.832H65.912V61.348ZM77.952 44.884V43.372H70.98V44.884H77.952ZM62.888 45.248H61.124V57.96H62.888V45.248ZM70.98 47.264V48.776H77.952V47.264H70.98ZM67.172 65.268V52.976H81.928V65.24H78.736V64.148H70.224V65.268H67.172ZM78.736 57.4V55.72H70.224V57.4H78.736ZM70.224 59.836V61.6H78.736V59.836H70.224ZM90.3 46.676C89.4787 45.5933 88.1533 43.8947 86.324 41.58L88.844 39.536L92.988 44.548L90.3 46.676ZM103.74 47.012H93.884V43.708H103.74V39.592H107.1V43.708H110.292V47.012H107.1V55.748C107.1 56.9987 106.969 57.932 106.708 58.548C106.447 59.164 105.98 59.584 105.308 59.808C104.655 60.032 103.656 60.144 102.312 60.144C101.341 60.144 100.165 60.0787 98.784 59.948L98.084 56.7C99.3907 56.868 100.697 56.952 102.004 56.952C102.527 56.9333 102.909 56.868 103.152 56.756C103.395 56.644 103.553 56.448 103.628 56.168C103.703 55.888 103.74 55.4493 103.74 54.852V47.012ZM97.02 47.992L98.588 50.036C99.26 50.876 100.007 51.8373 100.828 52.92L98.224 54.88C97.496 53.7787 96.264 52.0987 94.528 49.84L97.02 47.992ZM98.756 61.432C101.145 61.5627 103.18 61.628 104.86 61.628C106.559 61.628 108.603 61.5627 110.992 61.432L110.348 64.68C108.463 64.8107 106.447 64.876 104.3 64.876C102.135 64.876 100.109 64.8107 98.224 64.68C96.7493 64.5867 95.5453 64.4187 94.612 64.176C93.6787 63.952 92.8667 63.588 92.176 63.084C91.504 62.58 90.8507 61.8613 90.216 60.928C89.6747 61.432 88.9747 62.1227 88.116 63C87.276 63.8773 86.6227 64.5867 86.156 65.128L84.84 61.46L88.564 58.044V51.856H85.036V48.552H91.896V58.016C92.344 58.8373 92.8293 59.472 93.352 59.92C93.8933 60.368 94.5747 60.7133 95.396 60.956C96.236 61.18 97.356 61.3387 98.756 61.432ZM138.264 43.288C138.245 45.976 138.171 48.9253 138.04 52.136C137.928 55.328 137.779 58.324 137.592 61.124C137.517 62.5427 137.172 63.532 136.556 64.092C135.959 64.652 134.997 64.9413 133.672 64.96C132.571 64.9787 131.189 64.932 129.528 64.82L128.912 61.376C130.256 61.5253 131.32 61.6 132.104 61.6C132.701 61.6 133.14 61.5533 133.42 61.46C133.7 61.3667 133.905 61.1707 134.036 60.872C134.185 60.5547 134.288 60.0507 134.344 59.36C134.68 54.8613 134.839 50.624 134.82 46.648H129.444C128.865 47.9733 128.268 49.168 127.652 50.232L124.712 48.496C125.515 47.1893 126.261 45.6587 126.952 43.904C127.661 42.1493 128.184 40.5347 128.52 39.06L131.852 39.788C131.591 40.852 131.236 42.0187 130.788 43.288H138.264ZM121.744 39.872C121.501 41.048 121.193 42.2707 120.82 43.54H124.684V64.372H114.128V43.54H117.152C117.581 42.1027 117.936 40.6747 118.216 39.256L121.744 39.872ZM117.376 51.632H121.408V46.844H117.376V51.632ZM130.704 58.072C130.051 57.0267 129.351 55.9813 128.604 54.936C127.876 53.8907 127.213 53.0133 126.616 52.304L129.136 50.372C130.275 51.7347 131.656 53.5733 133.28 55.888L130.704 58.072ZM117.376 54.936V61.012H121.408V54.936H117.376ZM154.56 49.056C155.4 47.544 156.128 45.9947 156.744 44.408C157.379 42.8213 157.92 41.0667 158.368 39.144L161.28 39.676C161.037 40.7773 160.72 41.9067 160.328 43.064H166.852V46.088C166.759 46.48 166.525 47.2733 166.152 48.468C165.76 49.7933 165.527 50.5867 165.452 50.848L162.456 50.204L163.044 48.412C163.417 47.348 163.669 46.5547 163.8 46.032H159.292C158.452 48.1787 157.621 49.8867 156.8 51.156L154.56 49.056ZM154.252 50.596H151.228V43.176H141.456V40.348H155.82V43.176H154.252V50.596ZM149.8 44.38V50.064H142.24V44.38H149.8ZM147.084 46.508H144.732V47.964H147.084V46.508ZM165.2 65.436C163.128 63.308 161.588 60.8347 160.58 58.016C159.609 60.704 157.995 63.2053 155.736 65.52L154.084 63.504C153.897 64.1947 153.524 64.6613 152.964 64.904C152.404 65.1467 151.517 65.268 150.304 65.268C149.781 65.268 149.035 65.2307 148.064 65.156L147.504 62.412C148.288 62.524 149.072 62.58 149.856 62.58C150.416 62.58 150.789 62.4773 150.976 62.272C151.163 62.048 151.256 61.6187 151.256 60.984V54.068H141.036V51.296H156.212V54.068H154.28V61.74C154.28 62.0947 154.271 62.356 154.252 62.524C155.969 60.7507 157.201 58.9027 157.948 56.98C158.695 55.0387 159.087 52.7987 159.124 50.26L159.152 47.544H161.868C161.868 48.5893 161.859 49.4107 161.84 50.008C161.84 50.4 161.821 50.9507 161.784 51.66C162.027 53.9373 162.577 55.9347 163.436 57.652C164.313 59.3693 165.573 60.9933 167.216 62.524L165.2 65.436ZM142.184 55.384H149.744V61.572H142.184V55.384ZM144.676 59.332H147.056V57.484H144.676V59.332Z" fill="url(#paint1_linear_65_4185)"/>
          <path d="M8.288 4.132C7.63467 5.38267 6.79467 6.69867 5.768 8.08C4.76 9.46133 3.668 10.7307 2.492 11.888L0.756 9.452C1.67067 8.46267 2.58533 7.33333 3.5 6.064C4.41467 4.776 5.13333 3.6 5.656 2.536L8.288 4.132ZM25.34 28.464C23.94 26.896 22.82 25.3653 21.98 23.872C21.028 25.3093 19.7773 26.896 18.228 28.632L16.184 26.112C18.0507 24.3013 19.488 22.4813 20.496 20.652C19.88 18.9533 19.404 16.9747 19.068 14.716L18.48 15.724L16.94 12.588C17.164 12.1773 17.3227 11.86 17.416 11.636H7.98V11.58C7.51333 12.588 6.98133 13.5867 6.384 14.576V28.268H3.444V18.916C2.92133 19.6067 2.44533 20.1853 2.016 20.652L0.644 17.432C1.57733 16.4613 2.492 15.276 3.388 13.876C4.284 12.476 5.04 11.0853 5.656 9.704L7.98 11.048V4.776H10.248V8.92H11.564V2.592H14.028V8.92H15.316V4.776H17.5V11.496C18.0973 10.2267 18.6387 8.78 19.124 7.156C19.628 5.51333 19.9827 4.02 20.188 2.676L23.072 3.04C22.8667 4.216 22.5773 5.51333 22.204 6.932H27.104V10.04H26.04C25.8533 12.3173 25.5733 14.3053 25.2 16.004C24.8267 17.7027 24.3227 19.2613 23.688 20.68C24.6213 22.4533 25.8813 24.124 27.468 25.692L25.34 28.464ZM21.028 10.432C21.2333 12.7653 21.6067 14.856 22.148 16.704C22.708 14.8187 23.1187 12.5973 23.38 10.04H21.168C21.1493 10.096 21.1027 10.2267 21.028 10.432ZM7.896 13.372H17.248V16.06H7.896V13.372ZM18.48 23.06L17.892 23.508C16.7907 24.3853 16.1 24.964 15.82 25.244C15.5027 25.524 15.1853 25.832 14.868 26.168L13.02 24.292C13.3747 23.9 13.552 23.48 13.552 23.032V20.484H11.396V21.492C11.396 22.9107 11.228 24.1053 10.892 25.076C10.556 26.028 9.884 27.1387 8.876 28.408L6.524 26.476C7.32667 25.6733 7.85867 24.8987 8.12 24.152C8.4 23.4053 8.54 22.3133 8.54 20.876V17.6H16.38V22.136C16.66 21.8933 16.94 21.6693 17.22 21.464C17.3693 21.3333 17.5093 21.212 17.64 21.1C17.7893 20.988 17.92 20.8853 18.032 20.792L18.48 23.06ZM54.992 5.224V8.332H36.736V5.224H44.072C43.6053 3.97333 43.2973 3.18 43.148 2.844L46.704 2.2L47.32 3.74C47.4693 4.16933 47.656 4.664 47.88 5.224H54.992ZM37.072 3.068C36.512 5.308 35.896 7.296 35.224 9.032V28.352H31.948V15.416C31.2947 16.3867 30.66 17.264 30.044 18.048L28.56 14.408C29.6053 12.9707 30.6227 11.104 31.612 8.808C32.62 6.512 33.3387 4.356 33.768 2.34L37.072 3.068ZM38.08 9.816H52.948V12.504H38.08V9.816ZM52.948 16.788H38.08V14.016H52.948V16.788ZM41.132 28.324H37.8V18.328H53.172V28.324H49.84V26.98H41.132V28.324ZM49.84 23.984V21.296H41.132V23.984H49.84ZM67.2 16.284L66.052 16.732L64.064 17.46V24.796C64.064 25.7293 63.9707 26.4387 63.784 26.924C63.5973 27.4093 63.2707 27.7453 62.804 27.932C62.356 28.1373 61.684 28.24 60.788 28.24C59.9293 28.24 59.08 28.1933 58.24 28.1L57.568 24.796C58.296 24.908 59.0333 24.964 59.78 24.964C60.172 24.964 60.4333 24.8893 60.564 24.74C60.6947 24.572 60.76 24.2453 60.76 23.76V18.636C59.808 18.9907 58.7067 19.3733 57.456 19.784L56.784 16.396C57.8853 16.0413 59.2107 15.6027 60.76 15.08V10.376H57.428V7.044H60.76V2.592H64.064V7.044H67.004V10.376H64.064V13.904C64.456 13.7733 65.0253 13.5587 65.772 13.26L66.78 12.896L67.2 16.284ZM67.928 7.688V4.244H81.9V28.212H78.484V26.672H66.78V23.256H78.484V16.872H68.46V13.4H78.484V7.688H67.928ZM107.464 13.96H110.628C110.479 17.5627 110.227 20.8107 109.872 23.704C109.723 24.9547 109.499 25.8973 109.2 26.532C108.92 27.1853 108.491 27.6427 107.912 27.904C107.352 28.184 106.54 28.324 105.476 28.324C103.628 28.2867 102.237 28.2213 101.304 28.128L100.688 24.936C102.405 25.1227 103.693 25.216 104.552 25.216C105.224 25.216 105.691 25.0667 105.952 24.768C106.213 24.4507 106.419 23.872 106.568 23.032C106.867 20.9227 107.072 18.9627 107.184 17.152H96.544L97.664 7.912L100.884 8.108L100.128 13.96H104.104L104.804 6.764H96.768V3.544H108.444L107.464 13.96ZM85.624 7.268V3.992H95.62V7.268H92.12C91.728 8.836 91.224 10.4133 90.608 12H95.116V26.812H87.164V18.356C86.7347 19.0093 86.24 19.7 85.68 20.428L84.756 16.564C85.5213 15.444 86.268 14.0347 86.996 12.336C87.7427 10.6187 88.3307 8.92933 88.76 7.268H85.624ZM90.132 23.62H92.092V15.248H90.132V23.62ZM96.012 19.392H105.56V22.584H96.012V19.392ZM132.664 22.948L131.236 24.824H138.096V27.652H113.82V24.824H120.764C120.428 24.376 119.887 23.7413 119.14 22.92L120.876 21.8H116.984V15.08C116.163 15.584 115.229 16.0973 114.184 16.62L112.728 13.848C114.632 12.9333 116.191 12.0467 117.404 11.188C116.359 10.348 115.295 9.536 114.212 8.752L116.172 6.736C117.385 7.61333 118.515 8.5 119.56 9.396C120.512 8.46267 121.305 7.45467 121.94 6.372H114.38V3.516H125.272V6.4C124.581 7.66933 123.76 8.892 122.808 10.068H129.024C127.661 8.23867 126.579 5.88667 125.776 3.012L128.744 2.396C129.117 3.53466 129.472 4.468 129.808 5.196C131.227 4.28133 132.533 3.39467 133.728 2.536L135.548 4.916C133.607 6.11067 132.141 6.988 131.152 7.548C131.507 8.08933 131.889 8.57467 132.3 9.004C133.681 8.164 135.072 7.25867 136.472 6.288L138.32 8.64C137.069 9.424 135.781 10.18 134.456 10.908C135.669 11.7853 137.256 12.6253 139.216 13.428L137.704 16.368C136.584 15.864 135.669 15.4067 134.96 14.996V21.8H130.62L132.664 22.948ZM121.352 11.664C120.381 12.6347 119.308 13.5213 118.132 14.324H133.84C132.627 13.5773 131.525 12.728 130.536 11.776V12.812H121.352V11.664ZM131.74 16.984H120.176V19.056H131.74V16.984ZM122.08 21.8C122.752 22.472 123.396 23.2 124.012 23.984L122.864 24.824H127.568C128.389 23.76 129.108 22.752 129.724 21.8H122.08ZM160.16 20.82C161.915 22.1267 164.173 23.284 166.936 24.292L165.312 27.4C162.904 26.3173 160.944 25.2533 159.432 24.208C157.939 23.144 156.66 21.912 155.596 20.512V24.74C155.596 26.028 155.363 26.9147 154.896 27.4C154.429 27.8853 153.673 28.1373 152.628 28.156C151.937 28.1747 151.312 28.1653 150.752 28.128C150.211 28.1093 149.725 28.072 149.296 28.016L148.708 24.824C149.585 24.936 150.416 24.992 151.2 24.992C151.667 24.992 151.975 24.908 152.124 24.74C152.273 24.5533 152.348 24.1893 152.348 23.648V21.324C148.839 23.6013 145.628 25.4307 142.716 26.812L141.12 23.816C143.304 22.92 145.507 21.8653 147.728 20.652C147.075 20.092 146.309 19.476 145.432 18.804C144.555 18.132 143.827 17.6 143.248 17.208L144.9 15.08H141.484V12.168H159.74V10.544H144.536V7.94H159.74V6.26H143.752V3.32H163.044V12.168H166.656V15.08H162.848L164.64 16.956C163.576 18.1507 162.083 19.4387 160.16 20.82ZM147.896 20.54C149.669 19.5507 151.153 18.636 152.348 17.796V15.08H145.432C146.739 15.9013 148.157 16.9187 149.688 18.132L147.896 20.54ZM155.596 15.08V15.472C156.193 16.648 156.949 17.7493 157.864 18.776C159.376 17.656 160.711 16.424 161.868 15.08H155.596Z" fill="url(#paint2_linear_65_4185)"/>
          <defs>
          <linearGradient id="paint0_linear_65_4185" x1="1.70625" y1="53.5" x2="168" y2="53.5002" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E7CFFF"/>
          <stop offset="0.0677083" stop-color="#E7D0FF"/>
          <stop offset="0.979167" stop-color="#E6DCFF"/>
          </linearGradient>
          <linearGradient id="paint1_linear_65_4185" x1="1.70625" y1="53.5" x2="168" y2="53.5002" gradientUnits="userSpaceOnUse">
          <stop stop-color="#A554F8"/>
          <stop offset="0.979167" stop-color="#634AFF"/>
          </linearGradient>
          <linearGradient id="paint2_linear_65_4185" x1="1.70625" y1="16.5" x2="168" y2="16.5002" gradientUnits="userSpaceOnUse">
          <stop stop-color="#A554F8"/>
          <stop offset="0.979167" stop-color="#634AFF"/>
          </linearGradient>
          </defs>
        </svg>
      </div>

      <div
        @click="handleCloseLogin"
        class="close"
        :class="currentSize =='small' && 'small-close'"
      >
        <DynamicIcon
          v-show="currentSize =='small'"
          @click="togglePopup"
          name="close"
          class="close"
        />
      </div>
      <div class="qrcode-small">
        <img v-if="!needReload" :src="qrCodeURL" />
        <div v-else class="net-error" @click.stop="getLoginQrcode('reload')">
          <svg :class="isRequest && 'active'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4" clip-path="url(#clip0_1270_101510)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M50.9798 10.5088C49.0366 10.1743 47.0387 10 45 10C25.67 10 10 25.67 10 45C10 56.1164 15.1825 66.0224 23.2632 72.4337L27.1902 70.3766C19.2129 64.7677 14 55.4926 14 45C14 27.8792 27.8792 14 45 14C45.4133 14 45.8247 14.0081 46.2341 14.0241L50.9798 10.5088ZM62.6381 19.5035C70.7122 25.0996 76 34.4323 76 45C76 62.1208 62.1208 76 45 76C44.4595 76 43.9222 75.9862 43.3885 75.9588L38.697 79.434C40.7416 79.8058 42.8481 80 45 80C64.33 80 80 64.33 80 45C80 33.8105 74.7491 23.8474 66.577 17.4403L62.6381 19.5035Z" fill="black" style="fill:black;fill-opacity:1;"/>
            <path d="M45.0424 80L48.3127 72.3843C48.3712 72.2479 48.4006 72.1796 48.4134 72.1425C48.7162 71.2627 47.8202 70.4473 46.9728 70.8315C46.9371 70.8477 46.8719 70.8833 46.7416 70.9545L37.0398 76.2512C36.3988 76.6012 36 77.2732 36 78.0035C36 78.5837 36.5272 78.9725 37.0924 79.103C39.6336 79.6899 42.2806 80 45 80C45.0141 80 45.0283 80 45.0424 80Z" fill="black" style="fill:black;fill-opacity:1;"/>
            <path d="M44.5523 10.0028L41.2832 17.6158C41.2246 17.7522 41.1953 17.8204 41.1825 17.8575C40.8796 18.7373 41.7757 19.5527 42.623 19.1685C42.6588 19.1523 42.7239 19.1167 42.8543 19.0455L52.5561 13.7488C53.1971 13.3988 53.5959 12.7268 53.5959 11.9965C53.5959 11.3276 52.9615 10.9029 52.3073 10.7639C49.9505 10.2634 47.506 10 45 10C44.8505 10 44.7013 10.0009 44.5523 10.0028Z" fill="black" style="fill:black;fill-opacity:1;"/>
            </g>
            <defs>
            <clipPath id="clip0_1270_101510">
            <rect width="90" height="90" fill="white" style="fill:white;fill-opacity:1;"/>
            </clipPath>
            </defs>
          </svg>
          <p>网络异常</p>
          <p>点击刷新二维码</p>
        </div>
      </div>
      
    </div>
  </CommonModal>
</template>

<script setup>
import { ref, computed, watch, onBeforeMount, defineProps } from 'vue'
import CommonModal from '@/components/modal/common/component.vue'
import { getCarplayInfo } from '@/service/carplay-info'
import useQRCode from '@/composables/useQRCode'
import { sendLog } from '@/directives/v-log/log'
import { vipLogFrom } from '@/constants/index'
import store from '@/store'
import { withTimeoutHandling } from '@/utils/promiseUtils';
import { openDatabase, getItem } from '@/utils/IndexedDB';
import { cacheImages } from '@/constants'
import DynamicIcon from '@/components/DynamicIcon.vue';

const props = defineProps({
  songid: {
    type: Number,
    default: 0
  },
  log: {
    type: String,
    default: ''
  },
})

const { getQRCodeURL } = useQRCode()

const root = ref(null)
const qrCodeURL = ref('')
const needReload = ref(false)
const isRequest = ref(false)
const net_status = computed(() => store.state.base.net_status);
const themeClass = computed(() => store.state.themeClass)
const carplayInfo = computed(() => store.state.carplayInfo);
const currentSize = computed(() => store.state.currentSize);

const smallWxBG = ref(cacheImages.smallWxBG)
const loginWxBG1 = ref(cacheImages.loginWxBG1)
const loginWxBG2 = ref(cacheImages.loginWxBG2)

const getLoginQrcode = async (payload) => {
  console.log('请求登录二维码，当前状态:', props.log, { isRequest: isRequest.value, net_status: net_status.value });
  
  // 检查请求状态和网络状态
  if (isRequest.value || !net_status.value && payload !== 'reload') return;

  isRequest.value = true;

  try {
    // 获取支付二维码
    const pay_qr = payload === 'reload' 
      ? (await getCarplayInfo(true)).data.pay_qr 
      : carplayInfo.value.pay_qr || (await withTimeoutHandling(getCarplayInfo())).data.pay_qr;

    // 处理二维码数据
    if (pay_qr) {
      needReload.value = false;
      const qrCodeData = await withTimeoutHandling(getQRCodeURL(`${pay_qr}&songid=${props.songid || ''}&log=${vipLogFrom.get(props.log || '其他')}`));
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData;
      }
    } else {
      needReload.value = true;
    }
  } catch (error) {
    console.error('获取登录二维码失败:', error);
    needReload.value = true;
  } finally {
    isRequest.value = false;
  }
}

const handleCloseLogin = () => {
  root.value.hide()
  sendLog({
    event_type: '10000~50000',
    event_name: 10098,
    event_data: {
      str1: '任意页',
      str2: '登录弹窗',
      str3: '关闭弹窗',
      str4: 'click',
    },
  })
}

onBeforeMount(async() => {
  getLoginQrcode()

  await openDatabase(); // 确保数据库已打开
  smallWxBG.value = await getItem('smallWxBG') || cacheImages.smallWxBG;
  loginWxBG1.value = await getItem('loginWxBG1') || cacheImages.loginWxBG1;
  loginWxBG2.value = await getItem('loginWxBG2') || cacheImages.loginWxBG2;
})

watch(net_status, async(val) => {
  console.log('net_status', val)
  if (val) {
    getLoginQrcode()
  } else {
    needReload.value = true
  }
}, {
  immediate: true,
  deep: true
})

</script>

<style lang="stylus" scoped>
--popup-background = linear-gradient(180deg, rgba(255, 255, 255, 0.99) 0%, #E2E5ED 38.12%)

.dark-theme
  --popup-background = rgba(34, 32, 44, 1);
    
.login-modal
  &-content
    position relative
    width 1000px
    height 100%
    border-radius 24px
    display flex
    flex-direction column
    align-items center
    overflow hidden
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      zoom 1.5
    // 三分之一屏
    @media (max-width: 700px)
      width calc(328px * 3)
      height calc(388px * 3)
      background --popup-background
      border-radius calc(16px * 3)
      zoom .96
      .title
        position absolute
        top calc(30px * 3)
        left 50%
        transform translateX(-50%)
        font-size calc(24px * 3)
        font-weight 600
        color #000
      .background
        width 100%
        height 100%
        img
          width 100%
      .close
        position absolute
        top calc(30px * 3)
        right calc(15px * 3)
        left unset!important
        width auto
        height auto

        &.small-close *
          color #1D1D1F
          width 100%
          height 100%

    .title
      position absolute
      top 30px
      left 50%
      transform translateX(-50%)
    // 三分之一屏
    @media (max-width: 700px)
      width calc(328px * 3)
      height calc(388px * 3)
      border-radius calc(16px * 3)
      .title
        position absolute
        top calc(30px * 3)
        left 50%
        transform translateX(-50%)

    .background
      width 100%
      height 100%
      img
        width 100%
    .close
      position absolute
      top 30px
      right 15px
      left unset!important
      width 200px
      height 70px

      // 三分之一屏
      @media (max-width: 700px)
        width calc(24px * 3)
        height calc(24px * 3)

    .qrcode-small
      position absolute
      bottom 123px
      left 50%
      margin-left -125px
      width 230px
      height 230px
      display flex
      justify-content center
      align-items center
      background #ffffff
      border-radius 20px

      // 三分之一屏
      @media (max-width: 700px)
        width calc(140px * 3)
        height calc(140px * 3)
        top calc(178px * 3)
        margin-left calc(-64px * 3)
      img
        width 214px
        height 214px

        // 三分之一屏
        @media (max-width: 700px)
          width 100%
          height 100%
</style>
