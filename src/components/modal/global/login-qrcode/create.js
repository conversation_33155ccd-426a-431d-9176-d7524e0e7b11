import { createComponent } from '@/components/modal/utils/index.js';
import LoginModalVue from "./index.vue";
import ForceLoginVue from "./forceLogin.vue";

// 公共函数，用于创建组件实例
function createModalInstance(component, globalProps, globalSlots, forceProps) {
  const propsData = { ...globalProps, ...forceProps };
  const container = document.body;
  const mergedSlots = { ...globalSlots };

  try {
    const instance = createComponent(component, propsData, container, mergedSlots);
    return {
      hide: instance.refs.root.hide
    };
  } catch (error) {
    console.error('创建组件实例失败:', error);
    return null;
  }
}

export default function useLoginQrcode(globalProps = {}, globalSlots = {}) {
  const forceProps = {
    programmatic: true,
    lockScroll: true,
    isFullPage: true,
    active: true,
  };

  return {
    show(props = {}, slots = {}) {
      return createModalInstance(LoginModalVue, { ...globalProps, ...props }, { ...globalSlots, ...slots }, forceProps);
    },
  };
}

export function useForceLoginModal(globalProps = {}, globalSlots = {}) {
  const forceProps = {
    programmatic: true,
    lockScroll: true,
    isFullPage: true,
    active: true,
    canCancel: false,
  };

  return {
    show(props = {}, slots = {}) {
      return createModalInstance(ForceLoginVue, { ...globalProps, ...props }, { ...globalSlots, ...slots }, forceProps);
    },
  };
}