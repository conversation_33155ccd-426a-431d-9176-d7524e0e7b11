<template>
  <CommonModal ref="root" :zIndex="10">
    <div class="vip-loggedin-modal-content" :class="packages.length > 3 && 'vip-loggedin-modal-content-large'">
      <!-- <svg @click="handleCloseModal" class="close" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="90" height="90" rx="14" fill="#1E1F21" style="fill:#1E1F21;fill:color(display-p3 0.1176 0.1216 0.1294);fill-opacity:1;"/>
        <path opacity="0.800874" fill-rule="evenodd" clip-rule="evenodd" d="M31.0607 30.0604C30.4749 29.4746 29.5251 29.4746 28.9393 30.0604C28.3536 30.6462 28.3536 31.596 28.9393 32.1818L42.3788 45.6212L28.948 59.052C28.3622 59.6378 28.3622 60.5876 28.948 61.1734C29.5337 61.7591 30.4835 61.7591 31.0693 61.1734L44.5001 47.7425L57.9307 61.1731C58.5165 61.7589 59.4662 61.7589 60.052 61.1731C60.6378 60.5873 60.6378 59.6376 60.052 59.0518L46.6214 45.6212L60.0607 32.182C60.6464 31.5962 60.6464 30.6464 60.0607 30.0607C59.4749 29.4749 58.5251 29.4749 57.9393 30.0607L44.5001 43.4999L31.0607 30.0604Z" fill="white" style="fill:white;fill-opacity:1;"/>
      </svg> -->
      <div class="bg">
        <div @click="handleCloseModal" class="close flex-center">
          <DynamicIcon name="close" />
        </div>
        <div class="top">
          <img v-show="themeClass === 'themeDark'" src="https://qncweb.ktvsky.com/20241231/other/1bb4e3ab266dba317402f309daeeef72.png" alt="">
          <img v-show="themeClass === 'themeLight'" src="https://qncweb.ktvsky.com/20241231/other/39c784f1331f42731fa8924be2bdfc94.png" alt="">
        </div>
        <div class="vip-packages">
          <div
            class="vip-packages-item"
            v-for="item in packages"
            :key="item.id"
            :class="{ active: item.id === selectItem.id }"
            @click="handleClickItem(item)"
          >
            <div v-if="item.tips" :class="['tips', { orange: item.isOrange }]">{{ item.tips }}</div>
            <div class="days">{{ item.title }}</div>
            <div class="price">
              <span>¥{{ formatValue(item.fee) }}</span>
              <span v-if="item.old_fee !== item.fee" class="origin">¥{{ formatValue(item.old_fee) }}</span>
              <QuestionMarkTip  v-if="item.old_fee !== item.fee" />
            </div>
            <div class="day-price">¥<span>{{ formatValue(item.day_fee) }}</span>元/天</div>
          </div>
        </div>
        <div class="pay-info flex-between">
          <div class="left flex-column">
            <h3>微信扫码支付</h3>
            <div>
              <div class="price">
                <span>{{ formatValue(selectItem.fee) }}</span>元
              </div>
              <p>有效期至-{{ expirationDate }}</p>
            </div>
          </div>
          <div class="code flex-center">
            <img :src="qrCodeURL" alt="QR Code">
          </div>
        </div>
      </div>
    </div>
  </CommonModal>
</template>

<script setup>
import { onBeforeMount, ref, computed, onUnmounted, defineProps } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import useQRCode from '@/composables/useQRCode'
import get from 'lodash/get'
import { addDays, format } from 'date-fns'
import { sendLog } from '@/directives/v-log/log'
import { getVipPkg, getVipPkgQr } from '@/service/vip'
import eventBus from '@/utils/event-bus'
import DynamicIcon from '@/components/DynamicIcon.vue'
import { vipLogFrom } from '@/constants/index'
import QuestionMarkTip from '@/subMoudle/underline-price-copywriting/QuestionMarkTip.vue'

const props = defineProps({
  fr: { type: Number, default: 0 },
  logData: {
    type: Object,
    default: () => ({})
  }
})

const { getQRCodeURL } = useQRCode()
const root = ref(null)
const qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')
const packages = ref([])
const selectItem = ref({})
const hasNetworkError = ref(false)

// 计算属性
const vipInfo = computed(() => store.state.vipInfo)
const isVip = computed(() => !!vipInfo.value.end_time)
const unionid = computed(() => store.state.userInfo.unionid)
const userType = computed(() => store.state.userInfo.userType)
const themeClass = computed(() => store.state.themeClass)

const expirationDate = computed(() => {
  if (!selectItem.value.days) return ''
  const currentDate = isVip.value ? new Date(vipInfo.value.end_time) : new Date()
  return format(addDays(currentDate, selectItem.value.days), 'yyyy.MM.dd')
})

const formatValue = (value) => {
  return (value === undefined || isNaN(value)) ? 'N/A' : value / 100
}

const getVipQrcode = async () => {
  try {
    hasNetworkError.value = false
    const fr = props.fr || vipLogFrom.get('通用-全局弹窗-支付二维码展示')

    const data = await getVipPkgQr({
      unionid: unionid.value,
      pkg_id: selectItem.value.id,
      fr,
    })
    const qr = get(data, 'qr', '')
    if (qr) {
      qrCodeURL.value = await getQRCodeURL(qr) || qrCodeURL.value

      // 支付埋点 - 曝光
      sendLog({
        event_type: 'show',
        event_name: '1021',
        event_data: {
          str1: '通用',
          str2: '全局弹窗',
          str3: '任意支付档位',
          str4: 'click',
          str5: '已登录',
          str6: userType.value,
          str9: '车机端',
          str10: fr,
          ...props.logData,
        }
      })
    }
  } catch (error) {
    hasNetworkError.value = true
    store.dispatch('getCarplayInfo', {
      noLoading: true
    })
  }
}

const handleCloseModal = () => {
  store.dispatch('getCarplayInfo', {
    noLoading: true
  })
  root.value.hide()
  logEvent('关闭弹窗')
  store.commit('base/SET_NET_LOADING', false)
  eventBus.emit('handle-close-vip-modal')
}

const handleClickItem = (item) => {
  if (item.id !== selectItem.value.id) {
    qrCodeURL.value = 'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
    selectItem.value = item
    getVipQrcode()
  }
}

const logEvent = (action) => {
  sendLog({
    event_type: '10000~50000',
    event_name: 10094,
    event_data: {
      str1: '任意页',
      str2: 'VIP弹窗',
      str3: action,
      str4: 'click'
    }
  })
}

const handleRefreshQrcode = () => {
  getVipQrcode()
}

// 生命周期
onBeforeMount(async () => {
  const res = await getVipPkg(unionid.value)
  packages.value = res.data

  const index = isVip.value 
    ? packages.value.findIndex(item => !!item.tips) 
    : packages.value.slice().reverse().findIndex(item => !!item.tips)

  if (index >= 0) {
    packages.value[index].isOrange = true
  }

  selectItem.value = packages.value.find(item => item.id === res.recommend_pkg) || packages.value[0]
  getVipQrcode()

  eventBus.on('nats-vip-pay', handleCloseModal)
})

onUnmounted(() => {
  eventBus.off('nats-vip-pay', handleCloseModal)
})
</script>

<style lang="stylus" scoped>
.dark-theme
  .vip-loggedin-modal-content
    --modal-background: #22202C
    --item-background: rgba(255, 255, 255, 0.9)
    --payinfo-background #FFFFFF1A
    --payinfo-h3-color #FFFFFF66
    --payinfo-p-color #DEB868

.vip-loggedin-modal
  
  &-content
    --modal-background: #E8EAEE
    --item-background: #FFFFFF
    --payinfo-background #FFFFFF
    --payinfo-h3-color #00000066
    --payinfo-p-color #66320F
    width 1208px
    height 806px
    border-radius 32px
    overflow hidden
    background: var(--modal-background)
    background-repeat no-repeat
    position relative
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      zoom 0.9
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(762px * var(--scale)) !important
      height calc(508px * var(--scale)) !important
      border-radius calc(20px * var(--scale)) !important
    & > img
      position absolute
      left 0
      top 0
      width 100%
      height 100%
    & > *
      position relative
      z-index 2
    .bg
      position relative
      background: linear-gradient(172.95deg, rgba(255, 209, 1, 0.3) 6.46%, rgba(255, 132, 1, 0) 60.14%);

    .close
      position absolute
      top 30px
      right 30px
      left unset
      width 64px
      height 64px
      z-index 3
      .svg-icon
        width 42px
        height 42px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(28px * var(--scale)) !important
          height calc(28px * var(--scale)) !important
    .top
      width calc(1080 / 1208 * 100%)
      height auto
      margin 0 auto
      display flex
      align-items center
      img
        width 100%
        height auto
    .vip-packages
      display grid
      grid-template-columns: repeat(3, 280px);
      column-gap: 30px;
      justify-content center
      margin 30px 154px 0
      height auto

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        grid-template-columns: repeat(3, calc(176px * var(--scale))) !important
        margin-right calc(97px * var(--scale)) !important
        margin-left calc(97px * var(--scale)) !important
        column-gap: calc(20px * var(--scale)) !important
      &-item
        width: 280px;
        height: 232px;
        border-radius: 20px;
        background: var(--item-background)
        position relative
        text-align left
        padding-left 30px

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(176px * var(--scale)) !important
          height calc(146px * var(--scale)) !important
          border-radius calc(10px * var(--scale)) !important
        *
         color #66320F
        .tips
          padding 0 14px
          height 47px
          line-height 47px
          font-size 20px
          position absolute
          left 4px
          top -20px
          background #FF114D
          border-radius 12px
          color #FFFFFF
        .days
          font-size: 28px;
          font-weight: 500;
          margin 35px 0 2px
        .price
          // color rgba(255, 255, 255, 0.6)
          font-size: 24px;
          display flex
          align-items center
          .origin
            // color rgba(255, 255, 255, 0.3)
            text-decoration line-through
            margin-left 8px
            opacity 0.5
        .day-price
          font-size: 22px;
          margin 30px 0 0
          color #DD9949
          span
            font-size: 40px;
            color #DD9949
            font-weight bold

      .active
        background: linear-gradient(321.82deg, #FFEAB7 5.61%, #FFF0C8 34.88%, #FFECB9 96.2%);
        .day-price
          color #883700
          span
            color #883700

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        .tips
          zoom 1.2
        .days
          font-size calc(17px * var(--scale)) !important
        .price
          span
            font-size calc(12px * var(--scale)) !important
        .day-price
          font-size calc(13px * var(--scale)) !important
          span
            font-size calc(24px * var(--scale)) !important
    .pay-info
      height 258px
      background var(--payinfo-background)
      margin 20px 154px 0
      border-radius 16px
      padding 21px 50px

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        margin-right calc(97px * var(--scale)) !important
        margin-left calc(97px * var(--scale)) !important
        margin-top calc(16px * var(--scale)) !important
      .left
        flex 1
        text-align left
        justify-content space-between
        height 100%
        .price
          font-size 28px
          margin-bottom 0px
          color #DD2B2B
          span
            font-size 64px
            line-height 85px
            color #DD2B2B
            font-weight 600
        h3
          // color rgba(255, 255, 255, 0.8)
          font-size 24px
          margin-bottom 0px
          color var(--payinfo-h3-color)
        p
          // color rgba(255, 255, 255, 0.6)
          font-size 22px
          color var(--payinfo-p-color)

      .code
        width 192px
        height 192px
        background #fff
        margin-left 0px
        img
          width calc(186 / 192 * 100%)
          height calc(186 / 192 * 100%)

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        height calc(160px * var(--scale)) !important
        padding calc(20px * var(--scale)) calc(30px * var(--scale)) !important

        .code
          width calc(122px * var(--scale)) !important
          height calc(122px * var(--scale)) !important

        .left
          h3
            font-size calc(15px * var(--scale)) !important
          .price
            font-size calc(17px * var(--scale)) !important
            span
              font-size calc(40px * var(--scale)) !important
          p
            font-size calc(15px * var(--scale)) !important

    &-large
      .pay-info, .vip-packages
        margin-left 64px
        margin-right 64px

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          margin-right calc(40px * var(--scale)) !important
          margin-left calc(40px * var(--scale)) !important
      .vip-packages
        grid-template-columns: repeat(auto-fill, 200px);
        column-gap: 20px;

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          grid-template-columns: repeat(auto-fill, calc(126px * var(--scale))) !important
        &-item
          width 200px

          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            width calc(126px * var(--scale)) !important

// 三分之一屏
.vip-loggedin-modal-content .vip-packages-item .price span
  @media screen and (max-width: 410px)
    font-size calc(24px) !important
</style>
