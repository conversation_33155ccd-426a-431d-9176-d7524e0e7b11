import { createComponent } from '@/components/modal/utils/index.js'
import VipModalVue from './index.vue'
import UnloginModalVue from './unlogin.vue'

import VipLoggedInModalVue from './loggedIn-v1.vue'

export default function useVipQrcode(globalProps = {}, globalSlots = {}) {
  function show(Component, props = globalProps, slots = globalSlots) {
    const forceProps = {
      programmatic: true,
      lockScroll: true,
      isFullPage: true,
      active: true,
    }

    const propsData = { ...globalProps, ...props, ...forceProps }
    const container = document.body

    const mergedSlots = { ...globalSlots, ...slots }
    const instance = createComponent(
      Component,
      propsData,
      container,
      mergedSlots
    )

    if (instance && instance.refs && instance.refs.root) {
      return {
        hide: instance.refs.root.hide,
      }
    }
    
    console.error('Component instance is null or refs.root is not available');
    return {
      hide: () => {},
    }
  }
  return {
    show(props = globalProps, slots = globalSlots) {
      console.log('SSSSSSSS', props)
      if (props.isLogin) {
        return this.showLoggedin(props, slots)
      }
      // return vip
      if (props.log === '通用-底部运营位') return show(VipModalVue, props, slots)

      // return login
      return show(UnloginModalVue, props, slots)
    },
    showLoggedin(props = globalProps, slots = globalSlots) {
      return show(VipLoggedInModalVue, props, slots)
    },
  }
}
