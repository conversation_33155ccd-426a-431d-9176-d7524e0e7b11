<template>
  <CommonModal ref="root">
    <div class="unlogin-vip-modal-content">
      <div
        @click="handleCloseModal"
        class="close"
        :key="themeClass"
      >
        
        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.8" clip-path="url(#clip0_1517_20964)">
          <path d="M13.9989 12.8899L22.9558 3.93302L23.8984 4.8756L14.9414 13.8325L23.8984 22.7894L22.9551 23.7327L13.9982 14.7758L5.04195 23.732L4.09937 22.7894L13.0556 13.8332L4.09868 4.87629L5.04195 3.93302L13.9989 12.8899Z" fill="#1D1D1F"/>
          </g>
          <defs>
          <clipPath id="clip0_1517_20964">
          <rect width="26.6667" height="26.6667" fill="white" transform="translate(0.666016 0.5)"/>
          </clipPath>
          </defs>
        </svg>

      </div>
      <div class="title">
        <div class="title-text">扫码解锁点歌特权</div>
        <div class="subtitle-text">- 海量好歌嗨到爆 -</div>
      </div>
      <div class="qrcode">
        <template v-if="!needReload">
          <div class="qrcode-content flex-center">
            <img :src="qrCodeURL" :key="qrCodeURL">
          </div>
          <div class="qrcode-text">微信扫码登录</div>
        </template>
        
        <div v-else class="net-error" @click.stop="getVipQrcode('reload')">
          <!-- <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M12 4C7.58172 4 4 7.58172 4 12C4 14.7924 5.63505 17.2281 8.00538 18.5689L9.05089 17.5234C7.08401 16.4118 5.75 14.3716 5.75 12C5.75 8.54822 8.54822 5.75 12 5.75C12.1033 5.75 12.2062 5.7521 12.3085 5.75624L13.7449 4.31982C13.1824 4.1109 12.5995 4 12 4ZM16.7991 6.43087C18.3651 7.57186 19.4167 9.33307 19.5 11.3333L17.75 11.3333C17.6722 9.82644 16.8519 8.48956 15.6524 7.62725L16.7991 6.43087ZM6.25 11.3333L4.5 11.3333C4.58333 9.33307 5.63494 7.57186 7.20091 6.43087L8.34756 7.62725C7.14814 8.48956 6.32779 9.82644 6.25 11.3333ZM12 19.25C11.8967 19.25 11.7938 19.2479 11.6915 19.2438L10.2551 20.6802C10.8176 20.8891 11.4005 21 12 21C16.4183 21 20 17.4183 20 12C20 11.5995 19.9724 11.2065 19.9189 10.8229L18.1689 10.8229C18.2214 11.2025 18.25 11.5977 18.25 12C18.25 15.4518 15.4518 18.25 12 18.25Z" fill="#1D1D1F"/>
          </svg> -->
          <div class="error-text">网络异常</div>
          <div class="error-text">点击刷新二维码</div>

        </div>
      </div>
    </div>
  </CommonModal>
</template>

<script setup>
import { onBeforeMount, ref, computed, watch, defineProps } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import { getCarplayInfo } from '@/service/carplay-info'
import useQRCode from '@/composables/useQRCode'
import { vipLogFrom } from '@/constants/index'
import { sendLog } from '@/directives/v-log/log'
import { debounce } from 'lodash'
import useLoading from '@/composables/useLoading';
import { withTimeoutHandling } from '@/utils/promiseUtils';
import { openDatabase, getItem } from '@/utils/IndexedDB';
import { cacheImages } from '@/constants'

const props = defineProps({
  songid: {
    type: Number,
    default: 0
  },
  log: {
    type: String,
    default: ''
  },
  logData: {
    type: Object,
    default: () => {}
  },
  fr: {
    type: String,
    default: ''
  },
})

const net_status = computed(() => store.state.base.net_status)
const userInfo = computed(() => store.state.userInfo);
const isLogin = computed(() => !!userInfo.value.unionid);
const carplayInfo = computed(() => store.state.carplayInfo);
const currentSize = computed(() => store.state.currentSize);
const userType = computed(() => store.state.userInfo.userType)

const { getQRCodeURL } = useQRCode()
const { showLoading, hideLoading } = useLoading();

const root = ref(null)
const qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')
const needReload = ref(false)
const isRequest = ref(false)
const smallVipBG = ref(cacheImages.smallVipBG)

const imgs = {
  themeDark: {
    title: 'https://qncweb.ktvsky.com/20231226/vadd/a4e8232a41055a42361d438ecb297d65.png',
  },
  themeLight: {
    title: 'https://qncweb.ktvsky.com/20240222/other/2d49d3d87044995eac64e3a9eaf0e8c1.png',
  },
  themeSystem: {
    title: 'https://qncweb.ktvsky.com/20231226/vadd/a4e8232a41055a42361d438ecb297d65.png',
  },
}

const themeClass = computed(() => store.state.themeClass)

const getVipQrcode = debounce(async (payload) => {
  if (isRequest.value || !net_status.value && payload !== 'reload') return;
  isRequest.value = true;

  try {
    const pay_qr = payload === 'reload' 
      ? (await getCarplayInfo(true)).data.pay_qr 
      : carplayInfo.value.pay_qr || (await getCarplayInfo()).data.pay_qr;

    const fr = (props.fr || vipLogFrom.get('通用-全局弹窗-支付二维码展示'));

    if (pay_qr) {
      const qrCodeData = await getQRCodeURL(`${pay_qr}&songid=${props.songid || ''}&log=${fr}`);
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData;
        // 支付埋点 - 曝光
        sendLog({
          event_type: 'show',
          event_name: '1021',
          event_data: {
            str1: '通用',
            str2: '全局弹窗',
            str3: '支付二维码展示',
            str4: 'show',
            str5: isLogin.value ? '已登录' : '未登录',
            str6: userType.value,
            str9: '手机端',
            str10: fr,
            ...props.logData,
          }
        })
        
        console.log('vip弹窗埋点上报', {
          str1: '通用',
          str2: '全局弹窗',
          str3: '支付二维码展示',
          str4: 'show',
          str5: isLogin.value ? '已登录' : '未登录',
          str6: userType.value,
          str9: 'show',
          str10: fr,
          ...props.logData,
        })
      }
      needReload.value = false;
      return;
    }

    needReload.value = true;
  } catch (error) {
    console.error('getVipQrcode error:', error);
    needReload.value = true;
  } finally {
    isRequest.value = false;
    hideLoading();
  }
}, 500, { leading: true, trailing: false });

const handleCloseModal = () => {
  store.dispatch('getCarplayInfo')
  root.value.hide()

  sendLog({
    event_type: 'click',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '会员弹窗',
      str3: '会员弹窗关闭',
      str4: 'click',
      str5: 1,
    },
  })
}

onBeforeMount(async() => {
  getVipQrcode()

  await openDatabase(); // 确保数据库已打开
  smallVipBG.value = await getItem('smallVipBG') || cacheImages.smallVipBG;
})

watch(net_status, async (val) => {
  if (!val) {
    console.log('net_status', val)
    qrCodeURL.value = 'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
    needReload.value = true
    if (root.value) {
      root.value.hide()
    }
  } else {
    needReload.value = false
    getVipQrcode()
  }
}, {
  immediate: true,
  deep: true
})

</script>

<style lang="stylus" scoped>
.dark-theme
  .unlogin-vip-modal-content
    background rgba(34, 32, 44, 1)
    .title-text
      color rgba(255, 255, 255, 0.8) !important
    .subtitle-text
      color rgba(255, 255, 255, 0.5) !important
    .qrcode-text
      color rgba(255, 255, 255, 0.5) !important
    .error-text
      color rgba(255, 255, 255, 0.5) !important
    .close svg path
      fill #FFFFFF
    .qrcode .error-text
      color rgba(0, 0, 0, 0.5)!important

.unlogin-vip-modal
  &-content
    position relative
    width 546px
    height 368px
    padding-top 0 !important
    border-radius 21px
    background rgba(232, 234, 238, 1)
    // box-shadow 0px 4px 20px rgba(0, 0, 0, 0.08)
    display flex
    flex-direction column
    align-items center
    zoom 1.5

    .close
      position absolute
      top 16px
      right 16px
      width 32px
      height 32px
      display flex
      justify-content center
      align-items center
      cursor pointer
      transition all 0.2s ease-in-out
      &:hover
        opacity 0.8
      svg
        width 24px
        height 24px

    .title
      margin-top 40px
      text-align center
      .title-text
        font-size 22px
        font-weight 600
        line-height 33px
        color rgba(29, 29, 31, 0.8)
        margin-bottom 16px
      .subtitle-text
        font-size 18px
        line-height 20px
        color rgba(29, 29, 31, 0.5)

    .qrcode
      margin-top 16px
      width 166px
      height auto
      min-height 150px
      border-radius 4px
      position relative
      // box-shadow 0px 2px 10px rgba(0, 0, 0, 0.05)

      &-content
        width 166px
        height 166px
        background #FFFFFF
        border-radius 4px
        overflow hidden
        display flex
        flex-direction column
        align-items center

        img
          width 154px
          height 154px
          margin-top 0 !important

      .qrcode-text
        font-size 14px
        line-height 20px
        color rgba(29, 29, 31, 0.5)
        margin-top 12px
        text-align center

      .net-error
        position absolute
        left 0
        top 0
        display flex
        flex-direction column
        align-items center
        justify-content center
        width 100%
        height 100%
        background #FFFFFF
        border-radius 16px
        cursor pointer
        transition all 0.2s ease-in-out
        &:hover
          opacity 0.8
        svg
          margin-bottom 12px
          &.active
            animation rotate 1s linear infinite
        .error-text
          font-size 14px
          line-height 20px
          color rgba(29, 29, 31, 0.6)

@media (max-width: 1000px)
  .unlogin-vip-modal-content
    zoom 1.8

@media (max-width: 410px)
  .unlogin-vip-modal-content
    zoom 1.8

@keyframes rotate
  from
    transform rotate(0deg)
  to
    transform rotate(360deg)
</style>
