<template>
  <CommonModal ref="root">
    <div class="mobile-order-modal-content">
      <img
        @click="handleCloseLogin"
        class="close"
        src="https://qncweb.ktvsky.com/20211109/vadd/8c10127469ec6e740a3de5c76dee9f66.png"
      />
      <div class="title">
        <img src="https://qncweb.ktvsky.com/20230705/other/3f63bd63e3d2d27e98a6b979623199f1.png" alt="" />
        手机点歌
      </div>
      <div class="qrcode">
        <img :src="qrCodeURL" />
      </div>
    </div>
  </CommonModal>
</template>
<script>
import { onBeforeMount, ref } from 'vue';
import CommonModal from '@/components/modal/common/component.vue';
import { getBaseInfo } from '@/service/base';
import useQRCode from '@/composables/useQRCode';
import Toast from '@/utils/toast'
import get from 'lodash/get';

export default {
  name: 'MobileOrderQRcode',
  components: {
    CommonModal,
  },
  setup() {
    const { getQRCodeURL } = useQRCode();
    const root = ref(null);
    let qrCodeURL = ref(
      'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
    );

    const init = async () => {
      const { data } = await getBaseInfo();
      const url = get(data, 'phone_control_qr', '');
      if (url) {
        // const qrcodeUrl = props.from === 'loginSendVipActivity' ? `${data.login_qr}&fr=1` : data.login_qr
        const qrCodeData = await getQRCodeURL(url);
        if (qrCodeData) {
          qrCodeURL.value = qrCodeData;
        }
        return;
      }
      Toast('未获取到手机点歌二维码');
    };

    const handleCloseLogin = () => {
      root.value.hide();
    };

    onBeforeMount(init);

    return {
      qrCodeURL,
      root,
      handleCloseLogin,
    };
  },
};
</script>
<style lang="stylus" scoped>
.mobile-order-modal {
  &-content {
    padding-top: 52px;
    position: relative;
    width: 1000px;
    height: 700px;
    background: #1E1F21;
    border-radius: 20px;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;

    .close {
      position: absolute;
      top: 30px;
      left: 30px;
      width: 90px;
      height: 90px;
    }

    .title {
      display: flex;
      align-items: center;
      margin-bottom: 77px;
      font-size: 40px;

      img {
        width: 44px;
        height: 44px;
        margin-right: 10px;
      }
    }

    .qrcode {
      width: 320px;
      height: 320px;
      margin-bottom: 70px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #ffffff;
      border-radius: 10px;

      img {
        width: 300px;
        height: 300px;
      }
    }

    .tip {
      font-size: 32px;
    }
  }
}
</style>
