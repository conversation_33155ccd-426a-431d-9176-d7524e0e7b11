<template>
  <CommonModal ref="root" class="root-c">
    <!-- 过期会员运营弹窗 -->
    <div class="activity-modal-vip">
      <img class="activity-modal-vip-icon" src="https://qncweb.ktvsky.com/20250117/other/e5093a1e8372c440c0619238eab2681e.png" alt="" srcset="">
      <div @click="handleCloseModal" class="activity-modal-vip-close"></div>
      <div class="activity-modal-vip-bottom">
        <div class="activity-modal-vip-code">
          <img :src="qrCodeURL" alt="">
          <!-- <span>微信扫码 立享优惠</span> -->
        </div>
      </div>
    </div>
  </CommonModal>
</template>

<script setup>
import { computed, ref, onBeforeMount, defineProps } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import { getCarplayInfo } from '@/service/carplay-info'
import useQRCode from '@/composables/useQRCode'
import Toast from '@/utils/toast'
import get from 'lodash/get'
import { vipLogFrom } from '@/constants/index'
import { sendLog } from '@/directives/v-log/log'

const props = defineProps({
  closeEvent: {
    type: Function,
    default: () => {}
  }
})

const root = ref(null)
const { getQRCodeURL } = useQRCode()

const userType = computed(() => store.state.userInfo.userType)
const qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')

const getVipQrcode = async () => {
  const fr = vipLogFrom.get('通用-开屏-过期续费弹窗')
  
  const { data } = await getCarplayInfo()
  if (get(data, 'pay_qr', '')) {
    const qrCodeData = await getQRCodeURL(`${data.pay_qr}&log=${fr}`)
    if (qrCodeData) {
      qrCodeURL.value = qrCodeData
    }

    // 支付埋点 - 曝光
    sendLog({
      event_type: 'show',
      event_name: '1021',
      event_data: {
        str1: '通用',
        str2: '开屏-过期续费弹窗',
        str3: '展示',
        str4: 'show',
        str5: '已登录',
        str6: userType.value,
        str9: '车机端',
        str10: fr
      }
    })
    return
  }
  Toast('未获取到登录二维码')
}

const handleCloseModal = () => {
  props.closeEvent.call()
  root.value.hide()
}

onBeforeMount(getVipQrcode)
</script>

<style lang="stylus" scoped>
.activity-modal-vip
  padding-top 122px
  position relative
  width 1000px
  height 730px
  //background url('https://qncweb.ktvsky.com/20240227/other/1f9a32e6abf3dbf73094ff67bf55cfd7.png') no-repeat
  background url('https://qncweb.ktvsky.com/20250114/vadd/e2f4cc914dc2bf7432f0afc1d1a1f4c1.png') no-repeat
  background-size 100% 100%
  background-position center
  color rgba(255, 51, 100, 1)
  display flex
  flex-direction column
  align-items center
  position relative
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width calc(462px * 1.48) !important
    height calc(336px * 1.48) !important
  &-icon
    position absolute
    width calc(278px * var(--scale))
    height auto
    top calc(-142px * var(--scale))
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(278px * var(--scale)) !important
      top calc(-142px * var(--scale)) !important
    // // 三分之一屏
    // @media (max-width: 700px)
    //   width calc(278px * var(--scale)) !important
    //   top calc(-35px * 1.48) !important
  @media screen and (max-width: 1950px) and (min-width: 1700px) and (max-height: 720px) and (min-height: 650px)
    zoom 0.7
  &-close
    width 90px
    height 90px
    position absolute
    top 0
    right 0
    left initial!important
  &-bottom
    width 100%
    height 193px
    display flex
    justify-content center
    position absolute
    bottom 40px
    left 0
  &-code
    width 250px
    height 250px
    background #fff
    border-radius 8px
    //display flex
    //flex-direction column
    //align-items center
    position absolute
    right 170px
    bottom 75px
    // 三分之一屏
    @media (max-width: 700px)
      width calc(158px * 1.48) !important
      height calc(158px * 1.48) !important
      bottom calc(54px * 1.48) !important
      right calc(115px * 1.48) !important
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(108px * 1.48) !important
      height calc(108px * 1.48) !important
      bottom calc(40px * 1.48) !important
      right calc(80px * 1.48) !important
    img
      width 250px
      height 250px
      //margin-top 6px 
      // 三分之一屏
      @media (max-width: 700px)
        width calc(158px * 1.48) !important
        height calc(158px * 1.48) !important
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(108px * 1.48) !important
        height calc(108px * 1.48) !important
    //span
      //height 21px
      //line-height 21px
      //font-weight 400
      //font-size 15px
      //color rgba(0, 0, 0, 0.8)
      //margin-top 8px
.root-c
  z-index 999 !important
.theme-themeLight
  .activity-modal-vip
    background-image url('https://qncweb.ktvsky.com/20240227/other/6cfbd926364d0c8fa2ce9789acec9092.png')
</style>
