<template>
  <CommonModal 
    ref="root"
  >
    <div 
      v-if="currentSize === 'small' && (!isFirst || isLastImage)" 
      @click="handleCloseModal" 
      class="activity-modal-aiface-close"
    >
      <svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g filter="url(#filter0_b_1531_40977)">
        <ellipse cx="24.6832" cy="24.3589" rx="23.9898" ry="24.0146" fill="black" fill-opacity="0.2"/>
        </g>
        <path d="M17.7578 31.4336L31.8939 17.2828" stroke="white" stroke-width="3" stroke-linecap="round"/>
        <path d="M31.8965 31.4336L17.7604 17.2828" stroke="white" stroke-width="3" stroke-linecap="round"/>
        <defs>
        <filter id="filter0_b_1531_40977" x="-65.9733" y="-66.3224" width="181.314" height="181.363" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="33.3333"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1531_40977"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1531_40977" result="shape"/>
        </filter>
        </defs>
      </svg>
    </div>
    <div v-if="isFirst" class="activity-modal-aiface">
      <img
        v-show="currentSize != 'small'"
        class="bg-icon" 
        src="./images/aiface-bg.png" 
        alt=""
      >
      <div class="custom-swipe-indicators">
        <div 
          v-for="(image, index) in images" 
          :key="'indicator-' + index" 
          class="indicator-dot" 
          :class="{ active: currentIndex === index }"
          @click="goToSwipeIndex(index)"
        ></div>
      </div>
      <Swipe 
        class="my-swipe" 
        :autoplay="0"
        :loop="false"
        :show-indicators="false" 
        :touchable="false"
        @change="handleSwipeChange"
        ref="swipe"
        v-if="currentSize !== 'small'"
      >
        <Swipe-item 
          v-for="image in images" 
          :key="image"
        >
          <img :src="image" alt="">
        </Swipe-item>
      </Swipe>

      <Swipe 
        class="my-swipe" 
        :autoplay="0"
        :loop="false"
        :show-indicators="false" 
        :touchable="false"
        @change="handleSwipeChange"
        ref="swipe"
        v-if="currentSize === 'small'"
      >
        <Swipe-item 
          v-for="image in imagesSmall" 
          :key="image"
        >
          <img :src="image" alt="">
        </Swipe-item>
      </Swipe>
      <div 
        v-if="isLastImage && currentSize !== 'small'" 
        @click="handleCloseModal" 
        class="activity-modal-aiface-close"
      >
        <svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g filter="url(#filter0_b_1531_40977)">
          <ellipse cx="24.6832" cy="24.3589" rx="23.9898" ry="24.0146" fill="black" fill-opacity="0.2"/>
          </g>
          <path d="M17.7578 31.4336L31.8939 17.2828" stroke="white" stroke-width="3" stroke-linecap="round"/>
          <path d="M31.8965 31.4336L17.7604 17.2828" stroke="white" stroke-width="3" stroke-linecap="round"/>
          <defs>
          <filter id="filter0_b_1531_40977" x="-65.9733" y="-66.3224" width="181.314" height="181.363" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="33.3333"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1531_40977"/>
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1531_40977" result="shape"/>
          </filter>
          </defs>
        </svg>
      </div>

      <div 
        v-show="!isLastImage" 
        @click="nextImage('button')" 
        class="next-button flex-center"
      >
        下一步
        <CountDown
          :time="3000"
          @finish="nextImage"
          format="SS"
          ref="countdown"
        >
          <template #default="{ seconds }">
            {{ seconds + 1 }}S
          </template>
        </CountDown>
      </div>
      <div v-show="isLastImage" class="aiface-code">
        <img :src="AIQrcode" alt="" />
      </div>
    </div>
    <div v-else class="activity-modal-aiface not-first">
      <img 
        class="bg-icon" 
        src="./images/aiface-bg.png" 
        alt=""
      >
      <div
        v-show="currentSize !== 'small'"
        @click="handleCloseModal" 
        class="activity-modal-aiface-close"
      >
        <svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g filter="url(#filter0_b_1531_40977)">
          <ellipse cx="24.6832" cy="24.3589" rx="23.9898" ry="24.0146" fill="black" fill-opacity="0.2"/>
          </g>
          <path d="M17.7578 31.4336L31.8939 17.2828" stroke="white" stroke-width="3" stroke-linecap="round"/>
          <path d="M31.8965 31.4336L17.7604 17.2828" stroke="white" stroke-width="3" stroke-linecap="round"/>
          <defs>
          <filter id="filter0_b_1531_40977" x="-65.9733" y="-66.3224" width="181.314" height="181.363" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="33.3333"/>
          <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1531_40977"/>
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1531_40977" result="shape"/>
          </filter>
          </defs>
        </svg>
      </div>
      <div class="cover">
        <img v-show="currentSize !== 'small'" :src="aiFaceLast" />
        <img v-show="currentSize === 'small'" :src="aiFaceSmallLast" />
      </div>
      <div class="aiface-code">
        <img :src="AIQrcode" alt="" />
      </div>
    </div>
  </CommonModal>
</template>

<script setup>
import { computed, ref, onBeforeMount, defineProps } from 'vue'
import store from '@/store'
import CommonModal from '@/components/modal/common/component.vue'
import { sendLog } from '@/directives/v-log/log'
import { Swipe, SwipeItem, CountDown } from 'vant'
import useQRCode from '@/composables/useQRCode'
import store2 from 'store2'
import { cacheImages } from '@/constants'
import { openDatabase, getItem } from '@/utils/IndexedDB';

// Props
const props = defineProps({
  closeEvent: {
    type: Function,
    default: () => {}
  }
})

// Refs
const root = ref(null)
const swipe = ref(null)
const countdown = ref(null)
const currentIndex = ref(0)
const isFirst = ref(true)
const AIQrcode = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')
const images = ref([
  cacheImages.aiFaceSwipe1,
  cacheImages.aiFaceSwipe2,
  cacheImages.aiFaceSwipe3,
])

const imagesSmall = ref([
  cacheImages.aiFaceSwipeSmall1,
  cacheImages.aiFaceSwipeSmall2,
  cacheImages.aiFaceSwipeSmall3,
])
const aiFaceSmallLast = ref(cacheImages.aiFaceSmallLast)
const aiFaceLast = ref(cacheImages.aiFaceLast)

// Computed
const aiFaceQr = computed(() => store.state.aiFaceQr)
const userType = computed(() => store.state.userInfo.userType)
const currentSize = computed(() => store.state.currentSize);
const isLastImage = computed(() => currentIndex.value === images.value.length - 1)

// Methods
const handleCloseModal = () => {
  props.closeEvent.call()
  root.value.hide()
  sendLog({
    event_type: '10000~50000',
    event_name: isFirst.value ? 20293 : 20295,
    event_data: {
      str1: '换脸MV',
      str2: `${isFirst.value ? '首次点击-' : ''}关闭`,
      str3: '点击',
      str4: 'click'
    }
  })
  if (isFirst.value) {
    store2('aiface_first', true)
  }
}

const nextImage = (payload) => {
  swipe.value.next()
  console.log(swipe.value)
  if (payload === 'button') {
    sendLog({
      event_type: '10000~50000',
      event_name: 20291 + currentIndex.value,
      event_data: {
        str1: '换脸MV',
        str2: `下一步-${currentIndex.value + 1}`,
        str3: '点击',
        str4: 'click'
      }
    })
  }
}

const handleSwipeChange = (index) => {
  currentIndex.value = index
  if (countdown.value) {
    countdown.value.reset()
  }
}

const { getQRCodeURL } = useQRCode()
const getQrcode = async () => {
  console.log('aiFaceQr', aiFaceQr.value)
  const log = store2('aiface_first') ? 1890 : 1889
  AIQrcode.value = await getQRCodeURL(
    `${aiFaceQr.value}&log=${log}&ut=${userType.value}&fromType=carplay_xiaomi`
  )
}

// Lifecycle Hooks
onBeforeMount(async () => {
  try {
    await openDatabase(); // 确保数据库已打开

    if (store2('aiface_first')) {
      isFirst.value = false;
      aiFaceLast.value = await getItem('aiFaceLast') || cacheImages.aiFaceLast;
      aiFaceSmallLast.value = await getItem('aiFaceSmallLast') || cacheImages.aiFaceSmallLast;
    } else {
      const isSmall = currentSize.value === 'small';
      const imageKeys = isSmall 
        ? ['aiFaceSwipeSmall1', 'aiFaceSwipeSmall2', 'aiFaceSwipeSmall3']
        : ['aiFaceSwipe1', 'aiFaceSwipe2', 'aiFaceSwipe3'];

      const images = await Promise.all(imageKeys.map(key => getItem(key) || cacheImages[key]));

      if (isSmall) {
        imagesSmall.value = images;
      } else {
        images.value = images;
      }
    }

    getQrcode();
  } catch (error) {
    console.error('Error in aiface initialization:', error);
  }
});
</script>


<style lang="stylus" scoped>
.activity-modal-aiface
  padding 0px
  position relative
  width 1177px
  height 820px
  background: linear-gradient(180deg, #C7B497 0%, #FFFFFF 100%)
  border-radius 40px
  color rgba(255, 255, 255, .8)
  display flex
  flex-direction column
  align-items center
  overflow hidden
  
  // 三分之一屏
  @media (max-width: 700px)
    width calc(328px * 3)
    height calc(494px * 3) !important
    border-radius calc(16px * 3)
  & > *
    position relative
    z-index 2
  .aiface-code
    position absolute
    right 300px
    bottom 72px
    width 252px
    height 252px
    animation: fadeIn 1s ease-in-out
    @keyframes fadeIn
      0%
        opacity 0
      100%
        opacity 1

    // 三分之一屏
    @media (max-width: 700px)
      width calc(144px * 3)
      height calc(144px * 3)
      right unset
      left 50%
      transform translateX(-50%)
      bottom calc(52px * 3)

  .custom-swipe-indicators
    width 186px
    height 45px
    border-radius 36px
    background rgba(0, 0, 0, 0.2)
    position absolute
    top 40px
    left 50%
    transform translateX(-50%)
    display flex
    align-items center
    justify-content center
    .indicator-dot
      width 18px
      height 18px
      border-radius 100%
      background #FFFFFF
      opacity 0.3
      margin 0 10px
    .active
      opacity 1

    // 三分之一屏
    @media (max-width: 700px)
      width calc(80px * 3)
      height calc(24px * 3)
      border-radius calc(20px * 3)
      .indicator-dot
        width calc(8px * 3)
        height calc(8px * 3)
  .bg-icon
    position absolute
    width 1227px
    left -26px
    top -40px
    opacity 0.5
    z-index 1
  .my-swipe
    width 100%
    height 100%
  .next-button
    width 285px
    height 96px
    border-radius 15px
    position absolute
    bottom 75px
    right 279px
    cursor pointer
    background #AA8349
    color #ffffff
    font-size calc(25px * 1.5)
    font-weight 700

    // 三分之一屏
    @media (max-width: 700px)
      width calc(230px * 3)
      height calc(44px * 3)
      right unset
      left 50%
      transform translateX(-50%)
      bottom calc(20px * 3)
      font-size calc(16px * 3)


    :deep(.van-count-down)
      color #ffffff
      font-size calc(25px * 1.5)
      font-weight 700
      margin-left 20px

  &-close
    width 90px
    height 90px
    position absolute
    top 27px
    right 27px
    cursor pointer
    z-index 5

    // 三分之一屏
    @media (max-width: 700px)
      width calc(44px * 3)
      height calc(44px * 3)
      right calc(20px * 3)
      top calc(22px * 3)
      background: #FFFFFF33;
      border-radius 50%

  &.not-first
    height auto
    .cover
      // // 三分之一屏
      // @media (max-width: 700px)
      //   margin-top calc(38px * 3)
      //   margin-left calc(8px * 3)
      //   img
      //     width 100%
      //     height calc(426px * 3)
    .bg-icon
      top unset
      bottom -80px
      opacity 0.2
      // 三分之一屏
      @media (max-width: 700px)
        width calc(368px * 3)
        bottom unset
        left 0px
        top 0px
        opacity 1
      
    .aiface-code
      width 222px
      height auto
      aspect-ratio: 1 / 1; /* 1:1 的宽高比，高度等于宽度 */
      right unset
      left 50%
      transform translateX(-50%)
      bottom 86px
      animation none
      margin-left 6px

      // 三分之一屏
      @media (max-width: 700px)
        width calc(138px * 3)
        bottom calc(74px * 3)
        margin-left 0px
</style>
