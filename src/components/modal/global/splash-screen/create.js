import { createComponent } from '@/components/modal/utils/index.js';
import SplashScreenVue from "./index.vue";

/**
 * 创建弹窗组件实例
 * @param {Object} component - 组件对象
 * @param {Object} globalProps - 全局属性
 * @param {Object} globalSlots - 全局插槽
 * @param {Object} forceProps - 强制属性
 * @returns {Object} - 返回包含hide方法的对象
 */
function createModalInstance(component, globalProps, globalSlots, forceProps) {
  const propsData = { ...globalProps, ...forceProps };
  const container = document.body;
  const mergedSlots = { ...globalSlots };

  try {
    const instance = createComponent(component, propsData, container, mergedSlots);
    
    // 如果有onClose回调，监听组件的close事件
    if (globalProps.onClose && typeof globalProps.onClose === 'function') {
      instance.refs.root.$on = instance.refs.root.$on || function() {};
      
      // 重写hide方法，在关闭时触发onClose回调
      const originalHide = instance.refs.root.hide;
      instance.refs.root.hide = function() {
        originalHide.apply(instance.refs.root);
        // 调用onClose回调，传递弹窗类型
        globalProps.onClose(propsData.splashType);
      };
    }
    
    return {
      hide: instance.refs.root.hide,
      refs: instance.refs
    };
  } catch (error) {
    console.error('创建组件实例失败:', error);
    return null;
  }
}

/**
 * 开屏弹窗钩子函数
 * @param {Object} globalProps - 全局属性，可包含onClose回调函数
 * @param {Object} globalSlots - 全局插槽
 * @returns {Object} - 返回包含show方法的对象
 */
export default function useSplashScreen(globalProps = {}, globalSlots = {}) {
  const forceProps = {
    programmatic: true,
    lockScroll: true,
    isFullPage: true,
    active: true,
    canCancel: true,
  };

  return {
    show(props = {}, slots = {}) {
      return createModalInstance(SplashScreenVue, { ...globalProps, ...props }, { ...globalSlots, ...slots }, forceProps);
    },
  };
}