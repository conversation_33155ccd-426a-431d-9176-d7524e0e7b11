<template>
  <CommonModal ref="root">
    <div class="splash-screen-modal" :class="splashType2 === 'second'&&'splash-screen-modal2'">
      <div class="splash-screen-modal-content">
        <!-- 关闭按钮 -->
        <div class="splash-screen-modal-close" @click="close">
          <img src="https://qncweb.ktvsky.com/20250515/vadd/d86838ac909e20ac00d723f5854c65a2.png" alt="关闭">
        </div>
        
        <!-- 弹窗内容 - 根据类型显示不同内容 -->
        <template v-if="splashType2 === 'first'">
          <!-- 二维码区域 -->
          <div class="splash-screen-modal-qrcode">
            <div class="qrcode-container">
              <img :src="qrCodeURL" alt="二维码">
            </div>
          </div>
        </template>
        
        <!-- 第二个弹窗内容 -->
        <template v-else-if="splashType2 === 'second'">
          <div class="splash-screen-modal-body">
            <img src="https://qncweb.ktvsky.com/20250416/vadd/c79bf466d348a3b1e17423649dca1ee1.gif" alt="会员专享图片">
          </div>
          <!-- 二维码区域 -->
          <div class="splash-screen-modal-qrcode">
            <div class="qrcode-container">
              <img :src="qrCodeURL" alt="二维码">
            </div>
          </div>
        </template>
      </div>
    </div>
  </CommonModal>
</template>

<script>
import { ref, onMounted, nextTick } from 'vue'
import CommonModal from '@/components/modal/common/component.vue'
import useQRCode from '@/composables/useQRCode'
import { getCarplayInfo } from '@/service/carplay-info'
import { get } from 'lodash'
import { sendLog } from '@/directives/v-log/log';

export default {
  name: 'SplashScreenModal',
  components: {
    CommonModal
  },
  props: {
    // 弹窗类型：first - 第一个弹窗，second - 第二个弹窗
    splashType: {
      type: String,
      default: 'first',
      validator: (value) => ['first', 'second'].includes(value)
    }
  },
  setup(props, { emit }) {
    const root = ref(null)
    const qrCodeURL = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png')
    const { getQRCodeURL } = useQRCode()
    
    /**
     * 生成二维码
     * @param {String} url - 二维码链接
     */
    const generateQRCode = async () => {
      // console.log('generateQRCode')
      // 根据弹窗类型生成不同的二维码链接
      let res = {}
      try {
        res = await getCarplayInfo()
        // console.log('generateQRCode res111', res)
      } catch (error) {
        console.log('error', error)
        return
      }

      const url = get(res, 'data.pay_qr', '')

      if (!url) {
        return
      }
      
      const qrCode = await getQRCodeURL(`${url}&log=${props.splashType === 'first' ? 1860 : 1861}`)
      if (qrCode) {
        qrCodeURL.value = qrCode
      }
    }
    
    /**
     * 显示弹窗
     */
    const show = () => {
      root.value.show()
    }
    
    /**
     * 关闭弹窗
     */
    const close = () => {
      root.value.hide()
      emit('close', props.splashType) // 关闭时传递弹窗类型
    }
    
    onMounted(() => {
      nextTick(() => {
        generateQRCode()

        sendLog({
          event_type: 'click',
          event_name: '6001',
          event_data: {
            str1: '首页',
            str2: props.splashType === 'first' ? '开屏弹窗1' : '开屏弹窗2',
            str3: '展示',
            str4: 'show',
          },
        });

        sendLog({
          event_type: 'click',
          event_name: '1021',
          event_data: {
            str1: '首页',
            str2: props.splashType === 'first' ? '开屏弹窗1' : '开屏弹窗2',
            str3: '展示',
            str4: 'show',
            str5: '未登录',
            str9: '手机端',
            str10: props.splashType === 'first' ? 1860 : 1861,
          },
        });
      })
    })
    
    return {
      root,
      show,
      close,
      qrCodeURL,
      splashType2: props.splashType
    }
  }
}
</script>

<style lang="stylus" scoped>
div.splash-screen-modal2
  width 1000px
  height 680px // 增加高度以适应二维码区域
  position relative
  background url('https://qncweb.ktvsky.com/20250515/vadd/32b1cad1e67a8b34a6880189e904a253.png') no-repeat center center
  background-size contain
  &-content
    width 100%
    height 100%
    position relative
  .splash-screen-modal-qrcode
    position absolute
    top 430px!important
    right 410px!important
    .qrcode-container
      width 180px
      height 180px
      background #FFFFFF
      border-radius 10px
      display flex
      align-items center
      justify-content center
      margin-bottom 15px
      img
        width 140px
        height 140px
.splash-screen-modal
  width 1000px
  height 680px // 增加高度以适应二维码区域
  position relative
  background url('https://qncweb.ktvsky.com/20250515/vadd/b40ac9195732018d36a681aa98fb6fc2.png') no-repeat center center
  background-size cover
  background-position center center
  &-content
    width 100%
    height 100%
    position relative
    display flex
    flex-direction column
    align-items center
    justify-content center
    padding 40px
  
  &-close
    position absolute
    bottom -60px
    left 50%
    transform translateX(-50%)
    width 45px
    height 45px
    cursor pointer
    z-index 10
    
    img
      width 100%
      height 100%
  
  &-title
    font-size 48px
    color rgba(255, 255, 255, 0.9)
    margin-bottom 40px
    text-align center
  
  &-body
    display flex
    flex-direction column
    align-items center
    justify-content center
    margin-bottom 30px
    position absolute
    top 180px
    right 206px
    img
      width 281px
      height 163px
      margin-bottom 30px
      border-radius 10px
    
    p
      font-size 32px
      color rgba(255, 255, 255, 0.7)
      text-align center
  
  &-qrcode
    position absolute
    top 277px
    right 167px
    display flex
    flex-direction column
    align-items center
    justify-content center
    margin-top 20px
    .qrcode-container
      width 180px
      height 180px
      background #FFFFFF
      border-radius 10px
      display flex
      align-items center
      justify-content center
      margin-bottom 15px
      
      img
        width 170px
        height 170px
    
    .qrcode-desc
      font-size 24px
      color rgba(255, 209, 1, 0.9)
      text-align center
</style>