import SplashScreenComponent from './index.vue'
import useSplashScreen from './create.js';

/**
 * 注册开屏弹窗插件
 * @param {Object} app - Vue应用实例
 * @param {Object} props - 属性
 * @param {Object} slots - 插槽
 */
const Plugin = (app, props = {}, slots = {}) => {
  const splashScreen = useSplashScreen(props, slots);
  app.config.globalProperties.$splashScreen = splashScreen;
  app.provide('$splashScreen', splashScreen);
};

SplashScreenComponent.install = Plugin;

export default SplashScreenComponent;