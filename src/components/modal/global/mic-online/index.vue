<template>
  <CommonModal ref="root">
    <div class="miconline-modal-content">
      <DynamicIcon @click="handleCloseModal" class="close" name="close" />
      <h3>{{ title }}</h3>
      <div class="code flex-center">
        <img :src="qrCodeData" alt="">
      </div>
      <p>{{ info.content }}</p>
    </div>
  </CommonModal>
</template>
<script setup>
import { ref, toRefs, onBeforeMount, defineProps } from 'vue'
import CommonModal from '@/components/modal/common/component.vue'
import useQRCode from '@/composables/useQRCode'
import DynamicIcon from '@/components/DynamicIcon.vue';

const props = defineProps({
  info: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '您还没有连接麦克风'
  }
});

const root = ref(null);
const { info } = toRefs(props);
const { getQRCodeURL } = useQRCode();
const qrCodeData = ref('https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png');

const getQrcode = async () => {
  if (info.value.qr) {
    qrCodeData.value = await getQRCodeURL(info.value.qr);
  }
};

const handleCloseModal = () => {
  root.value.hide();
};

onBeforeMount(getQrcode);

</script>

<style lang="stylus" scoped>
.miconline-modal
  position fixed
  top 0
  right 0
  bottom 0
  left 0
  width 100vw
  height 100vh
  z-index 10
  display flex
  justify-content center
  align-items center
  &-content
    display flex
    flex-direction column
    align-items center
    padding: 60px 90px;
    position relative
    width: 820px;
    min-height: 296px;
    background var(--van-dialog-background-color) !important
    box-shadow: 0px 20px 80px 0px rgba(0, 0, 0, 0.5)
    border-radius: 8px
    font-size 40px

    // 三分之一屏
    @media (max-width: 700px)
      width calc(328px * 3)
      height calc(388px * 3)
      border-radius calc(16px * 3)
      padding-top 0px
      h3
        font-size var(--font-size-extra-large)
        padding calc(40px * 3) 0 0

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(547px * var(--scale)) !important
      height calc(368px * var(--scale)) !important
      border-radius calc(22px * var(--scale)) !important
      h3
        font-size var(--font-size-extra-large)
        padding calc(10px * var(--scale)) 0 0
    .close
      position absolute
      top 30px
      right 30px
      left unset!important
      width 40px
      height 40px

      // 三分之一屏
      @media (max-width: 700px)
        width calc(24px * 3)
        height calc(24px * 3)
    .code
      width: 250px;
      height: 250px;
      border-radius: 4px;
      background #fff
      margin 50px auto
      img
        width 240px
        height 240px

      // 三分之一屏
      @media (max-width: 700px)
        width: calc(158px * 3)
        height: calc(158px * 3)
        margin-top calc(50px * 3)
        border-radius calc(16px * 3)
        img
          width: calc(140px * 3)
          height: calc(140px * 3)
    p
      font-size 32px
      opacity 0.5

      // 三分之一屏
      @media (max-width: 700px)
        font-size var(--font-size-medium)
</style>