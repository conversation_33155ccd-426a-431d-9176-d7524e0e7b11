import { h, render } from 'vue';

// 移除元素的函数
export function removeElement(el) {
  if (el.remove) {
    el.remove();
  } else {
    el.parentNode?.removeChild(el);
  }
}

// 创建组件的函数
export function createComponent(component, props, parentContainer, slots = {}) {
  const vNode = h(component, props, slots);
  const container = createContainer();

  try {
    parentContainer.appendChild(container);
    render(vNode, container);
    return vNode.component;
  } catch (error) {
    console.error('创建组件失败:', error);
    return null;
  }
}

// 创建容器元素的函数
function createContainer() {
  const container = document.createElement('div');
  container.classList.add('thunder-modal-container');
  return container;
}

// 检查是否存在 window 对象
export function hasWindow() {
  return typeof window !== 'undefined';
}

// 定义可能的 HTMLElement 类型
export const MayBeHTMLElement = hasWindow() ? window.HTMLElement : Object;