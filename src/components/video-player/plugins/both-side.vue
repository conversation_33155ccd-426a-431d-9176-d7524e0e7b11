<template>
  <div class="both-sides-menu">
    <div
      class="menu"
      :class="item.pos"
      v-for="(item, i) in bothSides"
      :key="i"
    >
      <!-- 个人中心 -->
      <div
        class="menu-header-img"
        @click.stop="handleShowSideBar('我的特权', item.pos)"
      >
        <img v-if="headerimg" :src="headerimg"/>
        <img v-else style="background-color: #CFCFCF;" src="https://qncweb.ktvsky.com/20230424/vadd/608c9e8b386f6493599d2cc79ba361d4.png" class="header-img" alt="">
      </div>
      <!-- 快速点歌 -->
      <div class="menu-item" @click.stop="handleShowSideBar('快速点歌', item.pos, 10070)">
        <!-- <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/fe6a1fb414456f4274c71df747a3c4a4.png"/> -->
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M45.0788 26.8037C50.1502 31.8751 50.1502 40.0975 45.0788 45.1689C44.9235 45.3242 44.7653 45.4747 44.6043 45.6205C44.4458 45.6656 44.2963 45.7505 44.1716 45.8752C44.0876 45.9592 44.0217 46.0544 43.9738 46.1561C38.8758 50.2157 31.4314 49.8866 26.7137 45.1689C21.6423 40.0975 21.6423 31.8751 26.7137 26.8037C31.7851 21.7322 40.0074 21.7322 45.0788 26.8037ZM44.8719 47.9897C39.002 52.3905 30.6379 51.9216 25.2995 46.5831C19.447 40.7307 19.447 31.2419 25.2995 25.3894C31.1519 19.537 40.6406 19.537 46.493 25.3894C52.3455 31.2419 52.3455 40.7307 46.493 46.5831C46.4598 46.6164 46.4264 46.6494 46.3929 46.6823L54.7684 55.0578C55.1589 55.4483 55.1589 56.0815 54.7684 56.472C54.3779 56.8626 53.7447 56.8626 53.3542 56.472L44.8719 47.9897Z" fill="white"/>
        </svg>

        <div class="menu-item-text">快速点歌</div>
      </div>
      <!-- 调音 -->
      <div class="menu-item" @click.stop="handleShowSideBar('调音', item.pos, 10072)">
        <!-- <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/ba6d3128f6e4a8c9f52ed9ebb81f83fc.png"/> -->
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M25.0001 21.9999H27.0001V31.1C26.677 31.0344 26.3425 31 26.0001 31C25.6576 31 25.3232 31.0344 25.0001 31.1V21.9999ZM26.0001 44C26.3425 44 26.677 43.9656 27.0001 43.9V57.9997H25.0001V43.9C25.3232 43.9656 25.6576 44 26.0001 44ZM26 31.2856C23.7909 31.2856 22 33.0765 22 35.2856V39.5713C22 41.7804 23.7909 43.5713 26 43.5713C28.2091 43.5713 30 41.7804 30 39.5713V35.2856C30 33.0765 28.2091 31.2856 26 31.2856ZM24 35.2856C24 34.1811 24.8954 33.2856 26 33.2856C27.1046 33.2856 28 34.1811 28 35.2856V39.5713C28 40.6759 27.1046 41.5713 26 41.5713C24.8954 41.5713 24 40.6759 24 39.5713V35.2856Z" fill="white"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M39.0001 21.9999H41.0001V40.1C40.677 40.0344 40.3425 40 40.0001 40C39.6576 40 39.3232 40.0344 39.0001 40.1V21.9999ZM40.0001 53C40.3425 53 40.677 52.9656 41.0001 52.9V57.9997H39.0001V52.9C39.3232 52.9656 39.6576 53 40.0001 53ZM40 40.2856C37.7909 40.2856 36 42.0765 36 44.2856V48.5713C36 50.7804 37.7909 52.5713 40 52.5713C42.2091 52.5713 44 50.7804 44 48.5713V44.2856C44 42.0765 42.2091 40.2856 40 40.2856ZM38 44.2856C38 43.1811 38.8954 42.2856 40 42.2856C41.1046 42.2856 42 43.1811 42 44.2856V48.5713C42 49.6759 41.1046 50.5713 40 50.5713C38.8954 50.5713 38 49.6759 38 48.5713V44.2856Z" fill="white"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M53.0001 21.9999H55.0001V31.1C54.677 31.0344 54.3425 31 54.0001 31C53.6576 31 53.3232 31.0344 53.0001 31.1V21.9999ZM54.0001 44C54.3425 44 54.677 43.9656 55.0001 43.9V57.9997H53.0001V43.9C53.3232 43.9656 53.6576 44 54.0001 44ZM54 31.2856C51.7909 31.2856 50 33.0765 50 35.2856V39.5713C50 41.7804 51.7909 43.5713 54 43.5713C56.2091 43.5713 58 41.7804 58 39.5713V35.2856C58 33.0765 56.2091 31.2856 54 31.2856ZM52 35.2856C52 34.1811 52.8954 33.2856 54 33.2856C55.1046 33.2856 56 34.1811 56 35.2856V39.5713C56 40.6759 55.1046 41.5713 54 41.5713C52.8954 41.5713 52 40.6759 52 39.5713V35.2856Z" fill="white"/>
        </svg>

        <div class="menu-item-text">调音</div>
      </div>
      <!-- 画质 -->
      <div class="menu-item" @click.stop="handleShowSideBar('画质', item.pos, 10076)">
        <!-- <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/15836e466ffdfcd42ed465c66124c817.png"/> -->
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 24H18V25V55V56H19H61H62V55V25V24H61H19Z" stroke="white" stroke-width="2"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M38.571 32.732H36.569V38.958H29.023V32.732H27.043V48H29.023V40.608H36.569V48H38.571V32.732ZM54.3891 40.366C54.3891 34.008 51.3971 32.732 47.0191 32.732H42.6191V48H47.0191C51.3971 48 54.3891 46.262 54.3891 40.366ZM52.3431 40.366C52.3431 44.964 50.1651 46.262 46.9751 46.262H44.5991V34.448H46.9751C50.0771 34.448 52.3431 35.482 52.3431 40.366Z" fill="white"/>
        </svg>

        <div class="menu-item-text">画质</div>
      </div>
       <!-- 录音 -->
      <div v-if="enableRecording" class="menu-item" @click.stop="handleClickRecord(item.pos)" :class="isDisableRecord && 'disable'">
        <img v-show="!isRecording" class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/14cb230001b7d0e24de50919ad9fe316.png"/>
        <img v-show="isRecording" class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/b9080c00e829da04a546d608380b03d8.png"/>
        <div class="menu-item-text">录音</div>
      </div>
      <!-- 已点 -->
      <div class="menu-item" @click.stop="handleShowSideBar('已点', item.pos, 10078)">
        <!-- <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20240131/other/bc9fe847ad90aa026aa6f2eebf5ad5b4.png"/> -->
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22 24L42 24V26L22 26V24Z" fill="white"/>
          <path d="M22 38H37V40H22V38Z" fill="white"/>
          <path d="M22 52H32V54H22V52Z" fill="white"/>
          <circle cx="46.5" cy="47.5" r="7.5" stroke="white" stroke-width="2"/>
          <path d="M54 46V24L58 27.5652" stroke="white" stroke-width="2" stroke-linecap="square"/>
        </svg>

        <div class="menu-item-text">已点</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { sendLog } from '@/directives/v-log/log'
import useMvMode from '@/composables/useMvMode';
import useRecord from '@/composables/useRecord'
import { Toast } from 'vant'
import { TSBaseInfoInstance } from '@/packages/TSJsbridge'
import debounce from 'lodash/debounce';
import config from '@/config'

defineProps({
  headerimg: {
    type: String,
    default: ''
  },
  audioTrackMap: {
    type: Object,
    default() {
      return {
        acc: {},
        org: {}
      }
    }
  },
  enabledAudioTrackId: {
    type: Number,
  },
  isRecording: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['switch-audio-track', 'show-side-bar'])

const { isMvMode } = useMvMode()
const { startRecording, isDisableRecord } = useRecord();
const enableRecording = config.enableRecording

const bothSides = ref([{
  value: 0,
  label: '左侧',
  pos: 'left',
},{
  value: 1,
  label: '右侧',
  pos: 'right',
}])

const handleShowSideBar = (name, pos, logname) => {
  if (!isMvMode.value && name == '画质') {
    Toast('行车时，不可选择画质')
    return
  }

  sendLog({
    event_type: 'click',
    event_name: '6007',
    event_data: {
      str1: '欢唱页',
      str2: '侧边栏',
      str3: `${name}${pos === 'left' ? '左' : '右'}`,
      str4: 'click',
    }
  })
  emit('show-side-bar', { name, pos })

  TSBaseInfoInstance.controlMVScaling(pos === 'left' ? 1 : 0)

  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: pos === 'left' ? logname : logname + 1,
  //   event_data: {
  //     str1: 'MV',
  //     str2: '播控',
  //     str3: `${name}${pos === 'left' ? '左' : '右'}`,
  //     str4: 'click',
  //   },
  // })
}

const handleClickRecord = debounce((pos) => {
  sendLog({
    event_type: '10000~50000',
    event_name: pos === 'left' ? 30090 : 30091,
    event_data: {
      str1: '欢唱页',
      str2: '播控侧边栏',
      str3: `录音${pos === 'left' ? '左' : '右'}`,
      str4: 'click',
    },
  });
  startRecording();
}, 300, { immediate: true });

</script>

<style lang="stylus" scoped>
.both-sides-menu
  position absolute
  top 0
  width 100%
  height 100%
  display flex
  align-items center
  justify-content center
  z-index 9
  // @media screen and (max-width 1200px) and (min-height 1421px)
  //   display none
  // @media screen and (max-width 1180px) and (min-height 1200px)
  //   display none
  .menu
    width 130px
    height auto
    padding 30px 0px 16px
    background: rgba(30, 31, 33, 0.7);
    border-radius 10px
    backdrop-filter: blur(30px)
    position absolute
    top 230px
    border-radius 10px
    display flex
    flex-direction column
    align-items center
    // @media screen and (min-aspect-ratio: 1920/720) and (max-aspect-ratio: 1792/660)
    //   zoom 0.8
    //   top 100px !important

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(72px * 1.48) !important
      top calc(160px * 1.48) !important
      padding calc(30px * 1.48) 0px calc(20px * 1.48) !important
    &.left
      left 40px
    &.right
      right 40px
    &-item
      width 130px
      height 120px
      margin-bottom 10px
      margin-right 0
      display flex
      flex-direction column
      justify-content center
      align-items center

      @media (max-width: 900px) and (min-width: 701px)  
        width calc(72px * 1.48) !important
        height calc(62px * 1.48) !important
      &-text
      &.disable
        opacity 0.8
        img
          opacity 0.2
      &-icon, svg
        width 80px
        height 80px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(40px * 1.48) !important
          height calc(40px * 1.48) !important
      &-text
        font-size var(--font-size-extra-small)
        color rgba(255, 255, 255, 0.50)
        height 25px
        line-height 25px
        position relative
        top -4px
        white-space nowrap

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          font-size var(--font-size-small) !important
    &-header-img
      width 80px
      height 80px
      margin-bottom 30px
      border-radius 100%
      overflow hidden
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(44px * 1.48) !important
        height calc(44px * 1.48) !important
</style>
