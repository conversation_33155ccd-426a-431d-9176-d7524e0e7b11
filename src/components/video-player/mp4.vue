<template>
  <div
    class="mplayer"
    @click="$emit('on-video-click')"
  >
    <template v-if="!isNative">
      <video
        id="mplayer"
        muted="true"
        autoplay
        :poster="poster"
        :src="src"
        @play="$emit('play')"
        @pause="$emit('pause')"
        @canplay="handleCanPlay"
        @ended="handleVideoEnded"
        @timeupdate="handleTimeupdate"
      >
        <source :src="src" type="video/mp4">
        您的浏览器不支持 HTML5 视频。
      </video>
      <!-- <div class="mv-bar-btn" @click="toggleMute">
        <img :src="muteIconSrc" class="icon" alt="静音图标" />
      </div> -->
    </template>

    <!-- 错误提示层 -->
    <div v-if="showError" class="error-overlay" @click.stop="handleRetry">
      <div class="error-content">
        <div class="icon flex-center">
          <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="1">
            <rect x="8.8002" y="8.8002" width="54.4" height="54.4" rx="27.2" stroke="white" stroke-width="3.2"/>
            <circle cx="35.9998" cy="47.9998" r="3.2" fill="white"/>
            <path d="M32.842 25.1377C32.628 23.2518 34.1032 21.5996 36.0013 21.5996C37.8993 21.5996 39.3745 23.2518 39.1605 25.1377L37.6402 38.533C37.5456 39.3667 36.8403 39.9964 36.0013 39.9964C35.1624 39.9964 34.4571 39.3667 34.3625 38.533L32.842 25.1377Z" fill="white"/>
            </g>
          </svg>
        </div>
        <div class="error-text">当前网络环境较差</div>
        <div class="error-text">请您耐心等待或将车移至网络通畅区域</div>
        <div class="flex-center retry-button">重试</div>
      </div>
    </div>
    <!-- <div v-show="showCover && isNative" class="cover">
    </div> -->
  </div>
</template>
<script setup>
import {
  onMounted,
  onUnmounted,
  ref,
  defineProps,
  defineEmits,
  computed,
  watch,
  toRefs,
} from 'vue';
import { useStore } from 'vuex';
import eventBus from '@/utils/event-bus';
import { TSMediaInstance, TSWebEventInstance } from '@/packages/TSJsbridge';

const props = defineProps({
  src: String,
  autoplay: {
    type: Boolean,
    default: true,
  },
  poster: String,
  startPosition: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits([
  'play', 
  'pause', 
  'canplay', 
  'ended', 
  'timeupdate', 
  'error', 
  'onPlayingChange'
]);

const { src } = toRefs(props);
const startPosition = ref(props.startPosition);
const store = useStore();
const mvIsHide = computed(() => store.state.mvIsHide);
const orderedList = computed(() => store.state.orderedList)
const orderedListNum = computed(() => orderedList.value.length)

let video = null;
const isNative = !!window.TSMedia

const retryCount = ref(0)
const maxRetries = ref(3)
const showError = computed({
  get: () => store.state.videoError.show,
  set: (value) => store.commit('SET_VIDEO_ERROR', { show: value })
})

// const handleVideoError = (event, data) => {
//   console.log('handleVideoError', data);
//   emit('error', data);
// };

const handleVideoEnded = () => {
  emit('ended', video);
};

const handleTimeupdate = (t) => {
  emit('timeupdate', t);
};

const handleControlVideoPlay = () => {
  if (orderedListNum.value > 0) return
  
  console.log('0107 handleControlVideoPlay', src.value)
  TSMediaInstance.playSmallVideo({
    src: src.value
  });
};

const handleCanPlay = () => {
  if (video) {
    video.play();
    video.volume = 0.4;
    emit('onPlayingChange', true);
  }
  console.log('canplay');
  emit('canplay');
};

const handleMuted = () => {
  store.dispatch('miniMv/setMuted', true);
};

watch(src, (srcVal) => {
  if (orderedListNum.value > 0) return

  if (isNative && mvIsHide.value) {
    console.log('watch src', srcVal)
    TSMediaInstance.playSmallVideo({
      src: srcVal
    });
  }
})

// watch(mvIsHide, (mvIsHideVal) => {
//   console.log('watch mvIsHide', src.value)
//   if (isNative && mvIsHideVal) {
//     setTimeout(() => {
//       TSMediaInstance.playSmallVideo({
//         src: src.value
//       });
//     }, 1000); // 延时1秒
//   }
// }, {
//   immediate: true
// })

onMounted(() => {
  if (orderedListNum.value > 0) return
  
  console.log('onMounted: VideoPlayer', {
    src: src.value
  });

  setTimeout(() => {
    TSMediaInstance.playSmallVideo({
      src: src.value
    });
  }, 1000)
  if (isNative) {
    // 注册事件
    TSWebEventInstance.on('handleSmallTimeupdate', handleSmallTimeupdate);
    TSWebEventInstance.on('handleSmallPlayPrepared', handleSmallPlayPrepared);
    TSWebEventInstance.on('handleSmallPlayStart', handleSmallPlayStart);
    TSWebEventInstance.on('handleSmallPlayPause', handleSmallPlayPause);
    TSWebEventInstance.on('handleSmallPlayStop', handleSmallPlayStop);
    TSWebEventInstance.on('handleSmallPlayCompletion', handleSmallPlayCompletion);
    TSWebEventInstance.on('handleSmallPlayError', handleSmallPlayError);
  } else {
    video = document.getElementById('mplayer');
    video.src = src.value;
    video.currentTime = startPosition.value;
    handleControlVideoPlay();
    video.addEventListener('ended', handleVideoEnded);
    video.addEventListener('error', handleVideoError);
  }

  eventBus.on('mp4-control-play', handleControlVideoPlay);
  // eventBus.on('mp4-control-pause', handleControlVideoPause);
  // eventBus.on('mp4-control-replay', handleControlVideoPlay);
  eventBus.on('handle-video-muted', handleMuted);
});

onUnmounted(() => {
  console.log('onUnmounted: VideoPlayer');
  if (video) {
    video.removeEventListener('ended', handleVideoEnded);
    video.removeEventListener('error', handleVideoError);
  }
  eventBus.off('mp4-control-play', handleControlVideoPlay);
  // eventBus.off('mp4-control-pause', handleControlVideoPause);
  // eventBus.off('mp4-control-replay', handleControlVideoPlay);
  eventBus.off('handle-video-muted', handleMuted);
});

const handleSmallTimeupdate = (t) => {
  emit('timeupdate', t);
};

const handleSmallPlayPrepared = () => {
  console.log('小视频准备完成');
};

const handleSmallPlayStart = () => {
  emit('onPlayingChange', true);
  // showCover.value = false
  eventBus.emit('toggle-cover', false)
};

const handleSmallPlayPause = () => {
  emit('onPlayingChange', false);
};

const handleSmallPlayStop = () => {
  // emit('ended', video);
  // showCover.value = true
  eventBus.emit('toggle-cover', true)
};

const handleSmallPlayCompletion = () => {
  emit('ended', video);
  eventBus.emit('toggle-cover', true)
};

const handleVideoError = (event, data) => {
  console.log('handleVideoError', data);
  
  if (retryCount.value < maxRetries.value) {
    retryCount.value++
    console.log(`开始第 ${retryCount.value} 次重试...`)
    
    setTimeout(() => {
      TSMediaInstance.playSmallVideo({
        src: src.value
      });
    }, 2000)
  } else {
    console.error('达到最大重试次数，停止重试')
    store.commit('SET_VIDEO_ERROR', { 
      show: true,
      message: '视频加载失败，请检查网络连接' 
    })
    retryCount.value = 0
    emit('error', data);
  }
};

const handleSmallPlayError = (data) => {
  console.log('小视频错误', data);
  emit('error', data);
  showError.value = true
  eventBus.emit('toggle-cover', true)
};

const handleRetry = () => {
  store.commit('SET_VIDEO_ERROR', { show: false })
  retryCount.value = 0
  if (isNative) {
    TSMediaInstance.playSmallVideo({ src: src.value })
  } else if (video) {
    video.load()
    video.play().catch(handleVideoError)
  }
}

</script>
<style lang="stylus" scoped>
.mplayer
  position relative
  display flex
  flex-direction column
  justify-content center
  align-items center
  width 100%
  height 100%
  &-bottom
    display none
  video
    width 100%
    height 100%
    z-index 5
    background black
    position absolute
    transition-property all
    transition-duration .5s
    transition-timing-function linear

  &-plugins
    position absolute
    top 0
    bottom 0
    right 0
    left 0
    width 100vw
    height 100vh
    z-index 10
  .full-video
    top 0
  // .cover
  //   width 100%
  //   height 100%
  //   background url('https://qncweb.ktvsky.com/20241227/other/b1e6cb49a76d753245ec0a5dd71356dc.png') no-repeat center
  //   background-size auto 100%
  //   position absolute
  //   top 0
  //   left 0
  //   z-index 100

.error-tip
  position absolute
  top 50%
  left 50%
  transform translate(-50%, -50%)
  color white
  background rgba(0,0,0,0.7)
  padding 10px 20px
  border-radius 8px
  z-index 100

.error-overlay
  position absolute
  top 0
  left 0
  width 100%
  height 100%
  background: rgba(0, 0, 0, 0.8);
  z-index 200
  display flex
  justify-content center
  align-items center

  *
    font-size var(--font-size-extra-small)

  .error-content
    text-align center
    color #fff
    padding 0px
    .icon
      width 120px
      height 120px
      margin 0 auto 18px
      svg
        width 100%
        height 100%

  .error-text
    line-height 1.5
    margin-bottom 10px
    color rgba(255, 255, 255, 0.7)
    font-size var(--font-size-small1) 

  .retry-button
    width 280px
    height 100px
    border-radius 10px
    margin 18px auto 0
    font-size var(--font-size-medium)
    background: rgba(120, 120, 128, 0.2);
    color rgba(255, 255, 255, 0.6)
    backdrop-filter: blur(55.94405746459961px)
</style>
