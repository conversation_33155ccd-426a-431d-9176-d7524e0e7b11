<template>
  <div v-show="isMvMode" class="video-player" @click.stop="$emit('on-video-click')">
  </div>
</template>

<script>
import { toRefs } from 'vue'
import useVideoPlayer from '@/composables/useVideoPlayer';
import useMvMode from '@/composables/useMvMode';

export default {
  props: {
    song: Object,
  },
  setup(props, { emit }) {
    const { song } = toRefs(props);
    const { isMvMode } = useMvMode()

    useVideoPlayer(song, emit);

    return {
      isMvMode
    }
  },
}
</script>

<style scoped>
.video-player {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
}

#video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-player-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
}
</style>

<style lang="stylus" scoped>
.video-player {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;

  @media screen and (max-width: 1200px) {
    background: transparent;
    background-position: center;

    &-bottom {
      position: absolute;
      bottom: 120px;
      display: flex !important;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      img {
        // width 100px
        width: 300px;
      }

      p {
        color: #999999;
        font-size: 30px;
        margin-top: 40px;
      }
    }
  }

  @media screen and (max-width: 1180px) and (min-height: 1200px) {
    &-bottom {
      bottom: 50px !important;
    }
  }

  &-bottom {
    display: none;
  }

  #video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    background: transparent;
    object-fit: contain;

    @media screen and (max-width: 1200px) and (min-height: 1421px) {
      width: 100vw;
      height: 675px;
      top: 572px;
    }

    @media screen and (max-width: 1180px) and (min-height: 1200px) {
      width: 100vw;
      height: 675px;
      top: 378px;
    }
  }

  &-plugins {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10;
  }
}
</style>