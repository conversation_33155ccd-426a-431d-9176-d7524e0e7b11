<template>
  <div :class="className">
    <div class="wrapper avatar-default-wrapper">
      <div
        :class="`${className}-cover`"
        :style="{ backgroundImage: `url(${singerHead})` }"
      ></div>
    </div>
    <p>{{ singerName }}</p>
  </div>
</template>
<script>
import { computed, toRefs } from 'vue'
import { useStore } from 'vuex'

export default {
  props: {
    singer: {
      type: Object,
      default() {
        return {
          musiccount: 0,
          singerheader: '',
          singerhead: '',
          singerid: 0,
          singerjp: '',
          singer: '',
          singername: '',
          singerqp: '',
          singertypename: '',
        }
      }
    },
    className: {
      type: String,
      default: 'singer-item'
    }
  },
  name: 'SingerItem',
  setup(props) {
    const { singer } = toRefs(props)
    const store = useStore()

    const imgs = {
      themeDark: {
        imgFallback: {
          loading: require('@/assets/images/singer-dark.png'),
          error: require('@/assets/images/singer-dark.png')
        }
      },
      themeLight: {
        imgFallback: {
          loading: require('@/assets/images/singer-light.png'),
          error: require('@/assets/images/singer-light.png'),
        }
      },
      themeSystem: {
        imgFallback: {
          loading: require('@/assets/images/singer-dark.png'),
          error: require('@/assets/images/singer-dark.png')
        }
      },
    };

    const themeClass = computed(() => store.state.themeClass);

    const singerHead = computed(() => singer.value.singerheader || singer.value.singerhead)
    const singerName = computed(() => singer.value.singername || singer.value.singer)

    return {
      singerName,
      singerHead,
      imgs,
      themeClass
    }
  },
}
</script>
<style lang="stylus" scoped>
.singer-item
  width calc(144px * var(--scale))
  margin-bottom calc(40px * var(--scale))
  // 三分之一屏
  @media (max-width: 700px)
    margin-bottom calc(24px * var(--scale))
  .wrapper
    width 100%
    height auto
  &-cover
    width calc(144px * var(--scale))
    height calc(144px * var(--scale))
    border-radius 50%
    margin-bottom 20px
    background-size 100%
    background-repeat no-repeat
  p
    //width 200px !important
    //white-space nowrap
    //overflow hidden
    //text-overflow ellipsis
    //text-align center
    width 150px !important
    overflow hidden !important // 隐藏超出内容
    text-overflow ellipsis !important // 显示省略号
    white-space nowrap !important // 禁止换行
</style>