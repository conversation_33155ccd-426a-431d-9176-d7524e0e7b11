<template>
  <div :class="className" class="common-singer-item">
    <div class="wrapper avatar-default-wrapper">
      <div
        class="cover"
        :class="`${className}-cover`"
        :style="{ backgroundImage: `url(${singerHead})` }"
      ></div>
    </div>
    <p >{{ singerName }}</p>
  </div>

</template>
<script>
import { computed, toRefs } from 'vue'
import { useStore } from 'vuex'

export default {
  props: {
    singer: {
      type: Object,
      default() {
        return {
          musiccount: 0,
          singerheader: '',
          singerhead: '',
          singerid: 0,
          singerjp: '',
          singer: '',
          singername: '',
          singerqp: '',
          singertypename: '',
        }
      }
    },
    className: {
      type: String,
      default: 'singer-item'
    }
  },
  name: 'SingerItem',
  setup(props) {
    const { singer } = toRefs(props)
    const store = useStore()

    const imgs = {
      themeDark: {
        imgFallback: {
          loading: require('@/assets/images/singer-dark.png'),
          error: require('@/assets/images/singer-dark.png')
        }
      },
      themeLight: {
        imgFallback: {
          loading: require('@/assets/images/singer-light.png'),
          error: require('@/assets/images/singer-light.png'),
        }
      },
      themeSystem: {
        imgFallback: {
          loading: require('@/assets/images/singer-dark.png'),
          error: require('@/assets/images/singer-dark.png')
        }
      },
    };

    const themeClass = computed(() => store.state.themeClass);

    const singerHead = computed(() => singer.value.singerheader || singer.value.singerhead)
    const singerName = computed(() => singer.value.singername || singer.value.singer)

    return {
      singerName,
      singerHead,
      imgs,
      themeClass
    }
  },
}
</script>
<style lang="stylus" scoped>
.common-singer-item
  width calc(144px * var(--scale))
  margin-bottom calc(40px * var(--scale))
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width calc(144px * 1.48) !important
  // 三分之一屏
  @media (max-width: 700px)
    width calc(100px * var(--scale))
    margin-bottom calc(24px * var(--scale))
  .wrapper
    width 100%
    aspect-ratio: 1 / 1; /* 1:1 的宽高比，高度等于宽度 */
    margin-bottom calc(14px * var(--scale))
  .cover
    width 100%
    height 100%
    border-radius 50%
    background-size 100%
    background-repeat no-repeat
  p
    width 100%
    white-space nowrap
    overflow hidden
    text-overflow ellipsis
    text-align center
</style>