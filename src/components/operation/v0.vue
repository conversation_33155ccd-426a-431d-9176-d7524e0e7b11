<template>
  <div
    v-show="currentSize !== 'small' && isShowOperationSus && isYSTipAccept"
    class="operation-sus"
    :class="[route_page === 'search' && 'operation-sus-search', route_page === 'singer' && 'operation-sus-singer']"
    @click="handleShowVip"
  >
    <div @click.stop="$emit('close')" class="operation-sus-close"></div>
    <img v-show="!['search', 'singer'].includes(route_page)" class="operation-sus-icon" src="https://qncweb.ktvsky.com/20241122/other/1861781b2bcb80a8e2f70c3350cb3312.png">
  </div>
</template>
<script setup>
import { computed, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import useVip from '@/composables/useVip'
import { sendLog } from '@/directives/v-log/log'

const store = useStore()
const route = useRoute()
const { showVipQrcode } = useVip()

const frObj = {
  search: 1765,
  singer: 1767,
}

const route_page = computed(() => route.name)
const userInfo = computed(() => store.state.userInfo)
const isLogin = computed(() => !!userInfo.value.unionid)
const mvIsHide = computed(() => store.state.mvIsHide)
const isYSTipAccept = computed(() => store.state.storageYSTipAccept)
const currentSize = computed(() => store.state.currentSize);

const isShowOperationSus = ref(true)

const handleShowVip = () => {
  sendLog({
    event_type: 'click',
    event_name: 1737
  })
  showVipQrcode({
    log: frObj[route.name] ? `${route.name}-底部运营位` : '首页-底部运营位',
    isLogin: isLogin.value,
    fr: frObj[route.name] ? frObj[route.name] : 1759
  })
  sendLog({
    event_type: 'show',
    event_name: 1738
  })
}

watch(mvIsHide, (val) => {
  isShowOperationSus.value = val
})

</script>

<style lang="stylus" scoped>
.operation-sus
  width 800px
  height 90px
  display flex
  align-items center
  justify-content flex-end
  position fixed
  bottom 50px
  left calc(50vw - 400px)
  background url(https://qncweb.ktvsky.com/20241122/other/91f162704d82d7abdc1f5bcee0e60cf8.png) no-repeat
  background-size 100% 100%
  background-position center
  z-index 0

  &-icon {
    position absolute
    width 106px
    height auto
    top -40px
    left 80px
  }
  &-close
    width 80px
    height 80px
    margin-right 20px
.operation-sus-search
  width 740px
  height 116px
  background url(https://qncweb.ktvsky.com/20231213/vadd/037524b0c62adf8b6782e420936c1492.png) no-repeat
  background-size 100% 100%
  z-index 10
.operation-sus-singer
  width 766px
  height 141px
  background url(https://qncweb.ktvsky.com/20231213/vadd/072ae7f281a657a5691b52c7b7c90d9a.png) no-repeat
  background-size 100% 100%
</style>