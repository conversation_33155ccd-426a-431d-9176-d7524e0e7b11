<template>
  <div
    v-show="isShow"
    class="operation-sus-mi"
    :class="[
      // route_page === 'search' && 'operation-sus-search', 
      // route_page === 'singer' && 'operation-sus-singer'
      route_page !== 'home' && 'operation-sus-hide',
    ]"
    @click="handleShowVip"
  >
    <div @click.stop="$emit('close')" class="operation-sus-mi-close"></div>

  </div>
</template>
<script setup>
import { computed, ref, watch, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import useVip from '@/composables/useVip'
import { sendLog } from '@/directives/v-log/log'

const store = useStore()
const route = useRoute()
const { showVipQrcode } = useVip()

const frObj = {
  search: 1765,
  singer: 1767,
}

const route_page = computed(() => route.name)
const userInfo = computed(() => store.state.userInfo)
const isLogin = computed(() => !!userInfo.value.unionid)
const isVip = computed(() => store.state.vipInfo.end_time)
const mvIsHide = computed(() => store.state.mvIsHide)
const isYSTipAccept = computed(() => store.state.storageYSTipAccept)
// const currentSize = computed(() => store.state.currentSize);

const isShowOperationSus = ref(true)

const isShow = computed(() => {
  return isShowOperationSus.value && isYSTipAccept.value && !isVip.value && mvIsHide.value
})

const handleShowVip = () => {
  sendLog({
    event_type: 'click',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: '底部运营位',
      str3: '点击',
      str4: 'click',
      str5: isLogin.value ? '2' : '1',
    }
  })
  showVipQrcode({
    log: '通用-底部运营位',
    logData: {
      str1: '首页',
      str2: '底部运营位',
      str3: '点击',
      str4: 'click',
    },
    isLogin: isLogin.value,
    fr:  isLogin.value ? 1859 : 1858,
  })

  // sendLog({
  //   event_type: 'click',
  //   event_name: 1021,
  //   event_data: {
  //     str1: '首页',
  //     str2: '底部运营位',
  //     str3: '点击',
  //     str4: 'click',
  //     str5: isLogin.value ? '已登录' : '未登录',
  //     str10: isLogin.value ? '1859' : '1858',
  //   }
  // })

}

// watch(isShow, (val) => {
//   if (val) {
//     sendLog({
//       event_type: 'click',
//       event_name: 6001,
//       event_data: {
//         str1: '首页',
//         str2: '底部运营位',
//         str3: '展示',
//         str4: 'show',
//         str5: isLogin.value ? '2' : '1',
//       }
//     })
//   }
// })

// watch(mvIsHide, (val) => {
//   isShowOperationSus.value = val
// })
onMounted(() => {
  if (isShow.value) {
    sendLog({
      event_type: 'click',
      event_name: 6001,
      event_data: {
        str1: '首页',
        str2: '底部运营位',
        str3: '展示',
        str4: 'show',
        // str5: isLogin.value ? '2' : '1',
      }
    })
  }
})

</script>

<style lang="stylus" scoped>
.operation-sus-mi
  width 852px
  height 118px
  display flex
  align-items center
  justify-content flex-end
  position fixed
  bottom 50px
  left calc(50vw - 426px)
  background url(https://qncweb.ktvsky.com/20250516/other/0c5ea53b258e367a20214b17095f7231.png) no-repeat
  // background url(https://qncweb.ktvsky.com/20250425/vadd/c435b33e4cfee6fb703f2d7d794e623b.png) no-repeat
  background-size 100% auto
  background-position center
  z-index 0
  // zoom 1.5

  // 三分之一屏
  @media (max-width: 700px)
    display none

  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    zoom 1.5
    left calc(50vw - 38.3vw) !important

  &-close
    width: 36px;
    height: 36px;
    top: 53px;
    right: 30px;
    // width 20px
    // height 20px
    position absolute
    // top 15px
    // right 20px
.operation-sus-search
  // width 740px
  // height 116px
  // background url(https://qncweb.ktvsky.com/20231213/vadd/037524b0c62adf8b6782e420936c1492.png) no-repeat
  // background-size 100% 100%
  // z-index 10
.operation-sus-singer
  // width 766px
  // height 141px
  // background url(https://qncweb.ktvsky.com/20231213/vadd/072ae7f281a657a5691b52c7b7c90d9a.png) no-repeat
  // background-size 100% 100%
.operation-sus-hide
  display none
</style>