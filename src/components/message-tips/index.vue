<template>
  <div class="tips-message" :class="[positionClass]" @click="handleClick">
    <img class="tips-icon" :src="iconSrc" />
    <div class="tips-content" ref="contentRef">
      <div 
        class="marquee-container" 
        :class="{ 'marquee-active': shouldScroll && enableScroll }"
        :style="{ '--scroll-distance': scrollDistance + 'px', '--animation-duration': animationDuration + 's' }"
      >
        <span class="tips-content-inner" ref="contentInnerRef">
          <span class="tips-text">{{ messageText }}</span>
          <span v-if="actionText" class="tips-action">{{ actionText }}</span>
        </span>
        <!-- 复制一份内容用于无缝滚动 -->
        <span v-if="shouldScroll && enableScroll" class="tips-content-inner duplicate">
          <span class="tips-text">{{ messageText }}</span>
          <span v-if="actionText" class="tips-action">{{ actionText }}</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps, defineEmits, ref, onMounted, nextTick, watch } from 'vue'
import { useStore } from 'vuex'
import { sendLog } from '@/directives/v-log/log'

const props = defineProps({
  userStatus: {
    type: String,
    default: 'notLoggedIn', // notLoggedIn, nonVIP, VIP, expiredVIP
    validator: (value) => ['notLoggedIn', 'nonVIP', 'VIP', 'expiredVIP'].includes(value)
  },
  screenSize: {
    type: String,
    default: 'full', // full, medium, small
    validator: (value) => ['full', 'medium', 'small'].includes(value)
  },
  enableScroll: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['show-login-modal', 'show-payment-modal', 'show-toast'])
const store = useStore()
const contentRef = ref(null)
const shouldScroll = ref(false)

// 根据用户状态显示不同的消息内容
const messageText = computed(() => {
  switch (props.userStatus) {
    case 'notLoggedIn':
      return '开小米汽车，唱雷石音乐，享巅峰人生！'
    case 'nonVIP':
      return '您的起步已经让 ta 望尘莫及如若伴随音乐，岂不快哉！点我有惊喜！'
    case 'VIP':
      return '以数为律，唱响心声！'
    case 'expiredVIP':
      return '全新版本权益重磅升级，邀您回归！'
    default:
      return ''
  }
})

// 只有未登录状态显示"去登录"按钮
const actionText = computed(() => {
  return props.userStatus === 'notLoggedIn' ? '去登录' : ''
})

// 根据屏幕尺寸确定组件位置样式
const positionClass = computed(() => {
  if (props.screenSize === 'full') {
    return 'position-right'
  }
  return 'position-bottom'
})

// 图标资源
const iconSrc = computed(() => {
  return require('@/assets/tips-icon.png')
})

// 检查文本是否需要滚动
const checkTextOverflow = async () => {
  await nextTick()
  if (contentRef.value) {
    const container = contentRef.value
    const content = container.querySelector('.tips-content-inner')
    if (content && container) {
      shouldScroll.value = content.offsetWidth > container.offsetWidth
    }
  }
}

// 监听消息文本变化，重新检查是否需要滚动
watch(() => messageText.value, checkTextOverflow)
watch(() => props.screenSize, checkTextOverflow)

onMounted(() => {
  checkTextOverflow()
  // 窗口大小变化时重新检查
  window.addEventListener('resize', checkTextOverflow)
})

// 点击处理
const handleClick = () => {
  // 发送点击日志
  sendLog({
    event_type: 'click',
    event_name: '6001',
    event_data: {
      str1: '首页',
      str2: '顶部运营位',
      str3: '点击',
      str4: 'click',
      str5: props.userStatus === 'notLoggedIn' ? '未登录' : '已登录',
    },
  })

  // 根据用户状态执行不同操作
  switch (props.userStatus) {
    case 'notLoggedIn':
      emit('show-login-modal')
      break
    case 'nonVIP':
    case 'expiredVIP':
      emit('show-payment-modal')
      break
    case 'VIP':
      emit('show-toast', '展示您的歌喉，尽情欢唱吧！')
      break
  }
}

const contentInnerRef = ref(null)
const scrollDistance = ref(0)
const animationDuration = ref(15) // 默认动画持续时间

// 计算滚动距离和动画时间
const calculateScrollParams = async () => {
  await nextTick()
  if (contentInnerRef.value && contentRef.value) {
    const contentWidth = contentInnerRef.value.offsetWidth
    const containerWidth = contentRef.value.offsetWidth
    
    // 只有当内容宽度大于容器宽度时才需要滚动
    if (contentWidth > containerWidth) {
      // 根据文案长度动态调整滚动距离
      // 对于较长的文案，增加额外的滚动距离以确保完整显示
      const ratio = contentWidth / containerWidth
      
      // 根据比例调整滚动距离
      // console.log('ratio', ratio)
      if (ratio > 1.3) {
        // 非常长的文案
        scrollDistance.value = contentWidth * 1.5
      } else if (ratio > 0.9) {
        // 较长的文案
        scrollDistance.value = contentWidth * 1.2
      } else {
        // 一般长度的文案
        scrollDistance.value = contentWidth + 40 // 增加间距确保完整显示
      }
      
      // 根据内容长度和滚动距离调整动画时间
      // 保持滚动速度相对一致
      const scrollSpeed = 50 // 像素/秒
      animationDuration.value = Math.max(Math.min(scrollDistance.value / scrollSpeed, 30), 10)
    }
  }
}

// 监听消息文本变化，重新计算滚动参数
watch(() => messageText.value, () => {
  checkTextOverflow()
  calculateScrollParams()
})

watch(() => props.screenSize, () => {
  checkTextOverflow()
  calculateScrollParams()
})

onMounted(() => {
  checkTextOverflow()
  calculateScrollParams()
  // 窗口大小变化时重新检查
  window.addEventListener('resize', () => {
    checkTextOverflow()
    calculateScrollParams()
  })
})
</script>

<style lang="stylus" scoped>
.dark-theme
    .tips-message
        background rgba(255, 255, 255, 0.15)
    .tips-text
        color rgba(214, 214, 214, 1)
   .tips-action
        color rgba(219, 174, 106, 1)
.tips-message
  background rgba(255, 255, 255, 0.8)
  border-radius 12px
  padding 13px 10px
  display flex
  flex-direction row
  gap 5px
  align-items center
  justify-content flex-start
  position relative
  cursor pointer

.tips-icon
  flex-shrink 0
  width 27px
  height 27px
  position relative
  object-fit cover
  aspect-ratio 1

.tips-content
  text-align left
  font-family "HarmonyOsSansSc-Regular", sans-serif
  font-weight 400
  position relative
  overflow hidden
  flex 1
  padding 0 5px  // 添加左右内边距

.marquee-container
  display flex
  white-space nowrap
  position relative
  padding-right 10px  // 右侧添加内边距，避免文字紧贴边缘

.marquee-active
  animation marquee var(--animation-duration, 15s) linear infinite
  &:hover
    animation-play-state paused

@keyframes marquee
  0%
    transform translateX(0)
  100%
    transform translateX(calc(-1 * var(--scroll-distance, 100%)))

.tips-content-inner
  display inline-flex
  flex-direction row
  align-items center
  justify-content flex-start
  padding-left 5px  // 左侧添加内边距
  &.duplicate
    margin-left 24px  // 增加两份内容之间的间距，确保有明显分隔

.tips-text
  color #151515
  font-family "HarmonyOsSansSc-Regular", sans-serif
  font-weight 400
  white-space nowrap

.tips-action
  color #dbae6a
  font-family "HarmonyOsSansSc-Bold", sans-serif
  font-weight 700
  margin-left 5px
  white-space nowrap

// 响应式布局样式
.position-right
  // 适用于 1280px * 664px 屏幕
  margin-left 10px
  max-width 500px

.position-bottom
  // 适用于 818px * 651px 和 399px * 651px 屏幕
  margin-top 10px
  width 100%

@media (max-width: 1000px)
  .tips-message
    padding 30px 16px
    border-radius 1.5vw !important
  
  .tips-icon
    width 27px
    height 27px
    transform scale(1.8) !important

@media (max-width: 410px)
  .tips-message
    padding 30px 6px
    border-radius 32px !important
  
  .tips-icon
    width 27px
    height 27px
    transform scale(1.8) !important
</style>