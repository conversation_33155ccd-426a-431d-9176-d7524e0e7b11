<template>
  <div class="suspense-status">
    <div class="suspense-status-tip">
      {{ tip }}
    </div>
  </div>
</template>

<script>
import { onMounted } from "vue";

export default {
  name: "Suspense",
  props: {
    tip: String,
  },
  setup(props, { emit }) {
    onMounted(() => {});

    return {};
  },
};
</script>

<style lang="stylus" scoped>
.suspense-status {
  padding-top: 184px;
  &-tip {
    color rgba(255, 255, 255, 0.5)
    font-size 24px
    text-align: center
  }
}
</style>
