<template>
  <div class="download-status">
    <div v-if="state === DownloadStatus.PROCESSING">
      <Circle
        :current-rate="progress"
        :layer-color="themeClass === 'themeDark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'"
        color="rgba(219, 174, 106, 1)"
        size="32"
        :stroke-width="80"
      />
    </div>
    <template v-if="state === DownloadStatus.SUCCESS">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12ZM23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12ZM11.5949 15.519L17.5949 9.71899L16.2049 8.28101L10.8913 13.4175L8.08592 10.7724L6.71389 12.2276L10.2139 15.5276L10.9085 16.1825L11.5949 15.519Z" fill="#DBAE6A" style="fill:#DBAE6A;fill:color(display-p3 0.8583 0.6809 0.4149);fill-opacity:1;"/>
      </svg>
    </template>
    <template v-if="state === DownloadStatus.FAILED && themeClass === 'themeDark'">
      <svg @click.stop="handleClickFailed" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="white" stroke-opacity="0.2" style="stroke:white;stroke-opacity:0.2;" stroke-width="2"/>
        <circle cx="1" cy="1" r="1" transform="matrix(-1 0 0 1 13 15)" fill="white" fill-opacity="0.2" style="fill:white;fill-opacity:0.2;"/>
        <path d="M12.9165 7.99651C12.9615 7.46015 12.5382 7 12 7C11.4618 7 11.0385 7.46015 11.0835 7.99651L11.5518 13.5878C11.5713 13.8208 11.7662 14 12 14C12.2338 14 12.4286 13.8208 12.4481 13.5878L12.9165 7.99651Z" fill="white" fill-opacity="0.2" style="fill:white;fill-opacity:0.2;"/>
      </svg>
    </template>
    <template v-if="state === DownloadStatus.FAILED && themeClass === 'themeLight'">
      <svg @click.stop="handleClickFailed" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="#1D1D1F" stroke-opacity="0.2" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:0.2;" stroke-width="2"/>
        <circle cx="1" cy="1" r="1" transform="matrix(-1 0 0 1 13 15)" fill="#1D1D1F" fill-opacity="0.2" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.2;"/>
        <path d="M12.9165 7.99651C12.9615 7.46015 12.5382 7 12 7C11.4618 7 11.0385 7.46015 11.0835 7.99651L11.5518 13.5878C11.5713 13.8208 11.7662 14 12 14C12.2338 14 12.4286 13.8208 12.4481 13.5878L12.9165 7.99651Z" fill="#1D1D1F" fill-opacity="0.2" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:0.2;"/>
      </svg>
    </template>
  </div>
</template>

<script setup>
import { toRefs, computed, defineProps, defineEmits } from 'vue'
import { DownloadStatus } from '@/utils/download';
import { Circle } from 'vant';
import { useStore } from 'vuex';
import { debounce } from 'lodash'

const props = defineProps({
  state: Number,
  progress: Number,
})

const emit = defineEmits(['onClickDownloadFaild'])

const { state } = toRefs(props)
const store = useStore();
const themeClass = computed(() => store.state.themeClass)

const status = computed(() => {
  let res = ''
  console.log('DownloadStatus', state.value)
  switch(state.value) {
    case DownloadStatus.WAIT:
      res = '等待中';
      break;
    case DownloadStatus.PROCESSING:
      res = '下载中';
      break;
    case DownloadStatus.SUCCESS:
      res = '本地';
      break;
    case DownloadStatus.FAILED:
      res = '下载失败';
      break;
    case DownloadStatus.PAUSED:
      res = '下载暂停';
      break;
    case DownloadStatus.UNAUTH:
      res = '请登录';
      break;
    case DownloadStatus.UNSTORAGE:
      res = '空间不足，暂无法下载';
      break;
    default:
      res = '等待中';
      break;
  }
  return res
})

const handleClickFailed = debounce(() => {
  emit('onClickDownloadFaild')
}, 2000, { leading: true, trailing: false })

</script>

<style lang="stylus" scoped>
.download-status
  display flex
  align-items center
  position relative
  margin 0px !important
  & > div
    width 28px
    height 28px
    margin-right 8px
    
    // 三分之一屏
    @media (max-width: 700px)
      width calc(16px * var(--scale))
      height calc(16px * var(--scale))
  :deep(.van-circle)
    width 100% !important
    height 100% !important
  svg
    margin-right 8px
    width 28px
    height 28px
    
    // 三分之一屏
    @media (max-width: 700px)
      width calc(16px * var(--scale))
      height calc(16px * var(--scale))
  &-progress
    width 28px
    height 28px
</style>
