<script lang="ts">
export default {
  name: '<PERSON><PERSON><PERSON>ty<PERSON>',
}
</script>

<style lang="stylus">
:root
  --van-dialog-has-title-message-padding-top: 29px !important

  --van-overlay-background-color rgba(0, 0, 0, 0.2)

  --van-background-color-light: rgba(30, 31, 33, 1)
  --van-dialog-font-size: 32px!important
  --van-dialog-has-title-message-text-color: rgba(255, 255, 255, .8) !important
  --van-dialog-width: calc(546px * 1.5)!important
  --van-dialog-header-font-weight: 400!important
  --van-dialog-message-font-size: var(--font-size-medium) !important
  --van-dialog-message-line-height: 1.5!important
  --van-dialog-button-height: 60px!important
  --van-button-default-background-color: #D2D3D3!important
  --van-button-default-font-size: 32px!important
  --van-dialog-confirm-button-text-color: #1E1F21!important
  --van-dialog-confirm-button-background: rgba(204, 66, 57, 1)!important
  --van-dialog-header-line-height: 1!important

  --van-toast-background-color: rgba(255, 255, 255, 0.8) !important
  --van-toast-text-color: #FFF!important
  --van-toast-text-padding: 60.5px 72px !important
  --van-toast-max-width: 90%
  --van-toast-border-radius 10px !important
  --van-toast-position-top-distance 150px !important

.dark-theme {
  --van-toast-background-color: rgba(42, 44, 46, 1) !important
}

.van-toast
  box-shadow: 0px 8px 50px 0px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(30px)
  white-space nowrap
  padding 15px 35px
  @media (max-width: 838px)
    padding 25px 45px!important
  // 三分之一屏
  @media (max-width: 700px)
    padding 60.5px 72px!important
    white-space normal
    min-width 76vw
  &__text
    min-height 33px
    line-height 50px
    font-size var(--font-size-medium) !important

.van-dialog
  width: calc(546px * 1.5)
  border-radius calc(16px * var(--scale))
  padding 0 calc(60px * var(--scale))!important
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width calc(546px * 1.47) !important
    //height calc(275px * 1.48) !important
  &__header
    padding calc(40px * var(--scale)) 0  calc(30px * var(--scale)) !important
    font-size calc(20px * var(--scale))
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      font-size calc(20px * 1.48) !important
      margin-top calc(20px * 1.48)
  &__footer
    margin-bottom: 60px
    justify-content: center
    flex-direction: row-reverse
  &__message
    color var(--text-secondary-color)
    padding 0 0 calc(32px * var(--scale)) !important
    text-align center 
    font-size var(--font-size-medium) !important
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      margin-top calc(20px * 1.48)
  &__cancel,
  &__confirm
    flex inherit!important
    width calc(200px * var(--scale))
    height calc(54px * var(--scale))
    border-radius: 12px!important
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(200px * 1.48) !important
      height calc(54px * 1.48) !important
      margin-top calc(15px * 1.48)
      margin-bottom calc(10px * 1.48)
  &__confirm
    background: var(--van-dialog-confirm-button-background) !important;
    border none!important
    backdrop-filter: blur(200px);
    *
      color #FFF !important
    &:not(:first-child)
      margin-right 40px!important
  &__cancel
    background transparent !important
    border 2px solid var(--border-color) !important
    *
      opacity 0.9
  // 三分之一屏
  @media (max-width: 700px)
    width: calc(352px * 3) !important
    padding 0 calc(40px * 3) !important

    &__header
      font-size var(--font-size-large)

    &__footer
      margin-bottom calc(40px * 3)

    &__cancel,
    &__confirm
      width: calc(128px * 3)
      height: calc(50px * 3)
      border-radius : calc(8px * 3)

.van-hairline--top::after
  border-top-width: 0px!important
.van-hairline--left::after
  border-left-width: 0px!important
// .global-force-login
//   color rgba(255, 255, 255, 0.8)
//   border-radius: 8px!important
//   // background: #1E1F21!important
//   font-size: 36px;
</style>
