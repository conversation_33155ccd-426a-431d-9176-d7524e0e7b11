<template>
  <svg class="svg-icon" aria-hidden="true">
    <use :xlink:href="iconName"></use>
  </svg>
</template>

<script setup>
import { computed, onMounted, defineProps } from 'vue';
import store from '@/store'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'currentColor'
  },
  useTheme: {
    type: Boolean,
    default: false
  }
});

const iconName = computed(() => {
  if (props.useTheme && themeClass.value === 'themeDark') {
    return `#icon-${props.name}-dark`;
  }
  return `#icon-${props.name}`;
});
const themeClass = computed(() => store.state.themeClass)

onMounted(() => {
  if (props.useTheme) {
    import(`@/assets/icons/${props.name}-dark.svg`);
  }
  import(`@/assets/icons/${props.name}.svg`);
});
</script>

<style lang="stylus" scoped>
.svg-icon {
  width 100%
  height 100%
}
</style>