<template>
  <div class="volume-item" :class="{'disabled': disabled}">
    <div class="v-slider flex-between">
      <p class="name">
        <slot>名称</slot>
      </p>
      <Slider
        v-model="value"
        :disabled="disabled"
        @change="onChange"
        @update:model-value="onUpdate"
        @drag-start="onDragStart"
        @drag-end="onDragEnd"
        :step="step"
        :min="min"
        :max="max"
      >
        <template #button>
          <div class="custom-button flex-center">
            <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="1" height="16" fill="white" fill-opacity="0.4"/>
              <rect x="7" width="1" height="16" fill="white" fill-opacity="0.4"/>
              <rect x="14" width="1" height="16" fill="white" fill-opacity="0.4"/>
            </svg>
          </div>
        </template>
      </Slider>
      <div class="v-slider-ruler" :style="{ opacity: disabled ? 0.2 : 1}">
        <div v-for="(item, index) in rulers" :key="item" :class="(disabled && index === 0) && 'disabled'">{{item}}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, toRefs, watch, defineProps, defineEmits } from 'vue'
import _ from 'lodash'
import { Slider } from 'vant'

const props = defineProps({
  defaultValue: {
    type: Number,
  },
  disabled: {
    type: Boolean,
  },
  resetValue: {
    type: Number,
  },
  step: {
    type: Number,
    default: 5,
  },
  max: {
    type: Number,
    default: 100,
  },
  min: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['change', 'update'])

const { disabled, defaultValue, resetValue } = toRefs(props)
// 默认50%
let value = ref(50)
const rulers = [0, 25, 50, 75, 100]

const onChange = (val) => {
  console.log(val, 'onChange')
  emit('change', val)
}
const onUpdate = _.debounce((val) => {
  console.log(val, 'onUpdate')
  emit('update', val)
}, 200)

const onDragStart = (e) => {
  console.log('onDragStart', e)
}

const onDragEnd = (e) => {
  console.log('onDragEnd', e.changedTouches[0].clientX)
  const clientX = e.changedTouches[0].clientX
  if (clientX < 0) {
    value.value = 0
  }
}

onBeforeMount(() => {
  if (typeof defaultValue.value === 'number') {
    value.value = defaultValue.value
  }
  if (typeof resetValue.value === 'number' && resetValue.value > 5) {
    value.value = resetValue.value
  }
})

watch(resetValue, (val) => {
  console.log('resetValue', val)
  if (val !== value.value) {
    console.log('value.value', val)
    value.value = val
  }
}, {
  immediate: true
})
</script>

<style lang="stylus" scoped>
.volume-item
  --custom-button-color #363636;

  margin 0 calc(32px * var(--scale))!important
  display flex
  align-items center
  justify-content center
  height 165px
  position relative
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    height calc(119px * 1.48) !important
  &:after
    content ""
    width 100%
    height 2px
    position absolute
    bottom 0
    left 0
    background var(--border-color)
  &:nth-child(3)
    margin 10px 0
  .v-slider
    width 100%
    height 112px
    display flex
    align-items center
    position relative
    --van-slider-bar-height 12px !important
    --van-slider-button-width 40px!important
    --van-slider-button-height 40px!important
    --van-slider-button-background-color #6052FF !important
    --van-slider-button-border-radius 40px !important
    --van-slider-inactive-background-color #000 !important
    --van-slider-button-box-shadow none !important
    --van-slider-active-background-color #E3AB5D !important
    :deep(.van-slider)
      border-radius 15px
    :deep(.van-slider--disabled)
      --van-slider-button-background-color #2E2F32 !important
      --van-slider-active-background-color #2E2F32 !important
      --van-slider-inactive-background-color rgba(56, 58, 62, 0.60) !important
    .custom-button
      width 83px
      height 50px
      background var(--custom-button-color)
      background-size 100%
      z-index 10
      position relative
      left -6px
      touch-action: none;
      pointer-events: none;
      border-radius 38px
      svg
        width 16px
        height 16px
    // @media screen and (max-width 1200px) and (min-height 1200px)
    //   width 75vw
    .name
      :deep(p)
        opacity 0.6
      // font-size 39px
      // color rgba(255, 255, 255, 0.6)
    &.disabled
      .name
        opacity 0.2
        // color rgba(255, 255, 255, 0.2)
    .van-slider
      width calc(580 / 800 * 100%)
      border-radius: 38px;
      margin-bottom 44px
      position relative
      z-index 10
      margin-right calc(40px * var(--scale))
      // @media screen and (max-width 1200px) and (min-height 1200px)
      //   width 802px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        height calc(10px * 1.48) !important
    :deep(.van-slider__bar)
      height 4px !important
      min-height 4px !important
      top 4px
      padding 0 !important
      margin 0 6px
      transition width 1.5s ease
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        top calc(4px * 1.48) !important
    &-ruler
      width calc(606 / 800 * 100%)
      display flex
      justify-content space-between
      position absolute
      left unset 
      right calc(30px * var(--scale))
      top 60px
      // color rgba(255,255,255,0.2)
      font-size 24px
      // @media screen and (max-width 1200px) and (min-height 1200px)
      //   width 802px
      //   justify-content space-between
      div
        width 42px
        text-align center
        position relative
        opacity 0.6
        &::after
          content ""
          position absolute
          top -10px
          left 50%
          width 1px
          height 12px
          background rgba(255,255,255,0.2)
          z-index 1
      .disabled
        &::after
          content ""
          display none
.light-theme
  .volume-item
    --custom-button-color rgba(160, 74, 240, 1)
    .v-slider
      --van-slider-inactive-background-color #1D1D1F14!important
      --van-slider-active-background-color rgba(160, 74, 240, 1)!important
      --van-slider-disabled-opacity 0.2!important
    // .custom-button
    //   // background-image url('../../../../assets/slider-light.png')!important
    :deep(.van-slider)
      background #1D1D1F14!important
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        height calc(10px * 1.48) !important
      &__bar
        background rgba(160, 74, 240, 1)!important
    .v-slider-ruler
      div
        color #1D1D1F80!important
        &::after
          content ""
          position absolute
          top -10px
          left 50%
          width 1px
          height 12px
          background #1D1D1F80
          z-index 1
</style>
