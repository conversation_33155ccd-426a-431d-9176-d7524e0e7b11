<template>
  <div class="adj-volume">
    <div class="mv-side-back">
      <div class="left">调音</div>
      <div
        class="close"
        @click="$emit('close')"
      >
        <DynamicIcon name="close" />
      </div>
    </div>
    <!-- <div class="adj-volume-title">
      <div class="left">调音</div>
      <div class="close" @click="$emit('close')">
        <svg v-show="themeClass === 'themeLight'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.4">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="#1D1D1F" style="fill:#1D1D1F;fill:color(display-p3 0.1137 0.1137 0.1216);fill-opacity:1;"/>
          </g>
        </svg>
        <svg v-show="themeClass === 'themeDark'" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.4">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3.74219 26.3708L5.62781 28.2564L15.9987 17.8855L26.3698 28.2566L28.2554 26.371L17.8843 15.9999L28.2552 5.62903L26.3696 3.74341L15.9987 14.1143L5.628 3.7436L3.74238 5.62922L14.1131 15.9999L3.74219 26.3708Z" fill="white" style="fill:white;fill-opacity:1;"/>
          </g>
        </svg>
      </div>
    </div> -->
    <div class="adj-volume-main">
      <div class="effect-container" v-show="hasDongle && enableMoreEffect">
        <EffectItem 
          :effect="effect"
          v-for="(effect,index) in effectList"
          :key="index"
          :isSelected="useEffect == effect.sound_type"
          @select="handleSelect(effect)"/>
      </div>
      
      <EffectSlider
        :disabled="!(hasDongle && !systemVolumeMuted)"
        :defaultValue="micVolume"
        :reset-value="micVolume"
        @update="onMicChange"
        :max="store2('maxMicVolume') || 15"
        :step="1"
      >
        <div class="adj-volume-main-item">
          <svg v-show="themeClass === 'themeDark'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.6">
            <circle cx="25.7232" cy="14.2403" r="9.27787" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
            <path d="M17.1562 16.9525L5.44418 31.8946L8.67421 35.1246L23.6163 23.4126" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
            </g>
          </svg>
          <svg v-show="themeClass === 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.6">
            <circle cx="25.7232" cy="14.2403" r="9.27787" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
            <path d="M17.1562 16.9525L5.44418 31.8946L8.67421 35.1246L23.6163 23.4126" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
            </g>
          </svg>
          <p>麦克风</p>
        </div>
      </EffectSlider>
      <EffectSlider
        :disabled="systemVolumeMuted"
        :defaultValue="videoVolume"
        @update="onVolumeChange"
      >
        <div class="adj-volume-main-item">
          <svg v-show="themeClass === 'themeDark'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.6">
            <circle cx="10" cy="31" r="5" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
            <circle cx="27" cy="26" r="5" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
            <path d="M15 30V10L32 5V25.5" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
            </g>
          </svg>
          <svg v-show="themeClass === 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.6">
            <circle cx="10" cy="31" r="5" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
            <circle cx="27" cy="26" r="5" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
            <path d="M15 30V10L32 5V25.5" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
            </g>
          </svg>
          <p>伴奏</p>
        </div>
      </EffectSlider>
      <EffectSlider
        v-if="enableMedia"
        :defaultValue="mediaVolume"
        :reset-value="mediaVolume"
        @update="onMediaChange"
        :max="store2('maxMediaVolume') || 15"
        :step="1"
      >
        <div class="adj-volume-main-item media">
          <svg v-show="themeClass === 'themeDark'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.6" clip-path="url(#clip0_735_52383)">
            <path d="M12 25.5001H4V15.5001H12H12.2868L12.53 15.3481L19 11.3045V29.6958L12.53 25.6521L12.2868 25.5001H12Z" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
            <path d="M25.001 14.499V14.499C28.0391 17.5371 28.0391 22.4629 25.001 25.501V25.501" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
            <path d="M30.501 8.99903V8.99903C36.5766 15.0747 36.5766 24.9253 30.501 31.001V31.001" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2"/>
            </g>
            <defs>
            <clipPath id="clip0_735_52383">
            <rect width="40" height="40" fill="white" style="fill:white;fill-opacity:1;"/>
            </clipPath>
            </defs>
          </svg>
          <svg v-show="themeClass === 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.6" clip-path="url(#clip0_5014_94763)">
            <path d="M12 25.5H4V15.5H12H12.2868L12.53 15.348L19 11.3044V29.6958L12.53 25.652L12.2868 25.5H12Z" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
            <path d="M25.001 14.499V14.499C28.0391 17.5371 28.0391 22.4629 25.001 25.501V25.501" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
            <path d="M30.501 8.99903V8.99903C36.5766 15.0747 36.5766 24.9253 30.501 31.001V31.001" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2"/>
            </g>
            <defs>
            <clipPath id="clip0_5014_94763">
            <rect width="40" height="40" fill="white" style="fill:white;fill-opacity:1;"/>
            </clipPath>
            </defs>
          </svg>
          <p>多媒体</p>
        </div>
      </EffectSlider>
      <!-- <EffectSlider
        :disabled="!(hasDongle && !systemVolumeMuted)"
        :defaultValue="reverbVolume"
        :reset-value="reverbVolume"
        @update="onReverbChange"
        :max="store2('maxEchoVolume') || 15"
        :step="1"
      >
        <div class="adj-volume-main-item media">
          <svg v-show="themeClass === 'themeDark'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4">
              <path d="M16 12C11.5817 12 8 15.5817 8 20C8 24.4183 11.5817 28 16 28" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
              <path d="M24 12C28.4183 12 32 15.5817 32 20C32 24.4183 28.4183 28 24 28" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
              <path d="M16 6C8.26801 6 2 12.268 2 20C2 27.732 8.26801 34 16 34" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
              <path d="M24 6C31.732 6 38 12.268 38 20C38 27.732 31.732 34 24 34" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
            </g>
          </svg>
          <svg v-show="themeClass === 'themeLight'" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g opacity="0.4">
              <path d="M16 12C11.5817 12 8 15.5817 8 20C8 24.4183 11.5817 28 16 28" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
              <path d="M24 12C28.4183 12 32 15.5817 32 20C32 24.4183 28.4183 28 24 28" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
              <path d="M16 6C8.26801 6 2 12.268 2 20C2 27.732 8.26801 34 16 34" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
              <path d="M24 6C31.732 6 38 12.268 38 20C38 27.732 31.732 34 24 34" stroke="#1D1D1F" style="stroke:#1D1D1F;stroke:color(display-p3 0.1137 0.1137 0.1216);stroke-opacity:1;" stroke-width="2" stroke-linecap="square"/>
            </g>
          </svg>
          <p>混响</p>
        </div>
      </EffectSlider> -->
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import store from '@/store'
import EffectSlider from './slider.vue'
import eventBus from '@/utils/event-bus'
import { sendLog } from '@/directives/v-log/log'
import store2 from 'store2'
import config from '@/config'
import EffectItem from '@/components/mv/components/adj-effect/index.vue'
import {TSMicrophoneInstance} from "@/packages/TSJsbridge";

const enableMedia = config.enableMedia

const hasDongle = computed(() => store.state.hasDongle)
const micVolume = computed(() => store.state.micVolume)
const videoVolume = computed(() => store.state.videoVolume)
const reverbVolume = computed(() => store.state.reverbVolume)
const mediaVolume = computed(() => store.state.mediaVolume)
const systemVolumeMuted = computed(() => store.state.systemVolumeMuted)
const themeClass = computed(() => store.state.themeClass)
const effectList = computed(() => store.state.effectList)
// const effectList = [
//   { sound_name: '原生' },
//   { sound_name: '原生' },
//   { sound_name: '原生' },
//   { sound_name: '原生' },
//   { sound_name: '原生' },
//   { sound_name: '原生' },
// ]
const useEffect = computed(() => store.state.useEffect)
const enableMoreEffect = computed(() => store.state.enableMoreEffect)

const onMicChange = (val) => {
  store.commit('UPDATE_VIDEO_MIC_VOLUME', val)
  eventBus.emit('effect-adjust-tuner-change', 'mic', val)
  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: 30108,
  //   event_data: {
  //     str1: '欢唱页',
  //     str2: '调音',
  //     str3: '麦克风音量',
  //     str4: 'click',
  //   },
  // })
  sendLog({
    event_type: 'click',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '调音',
      str3: '麦克风音量',
      str4: 'click',
    },
  });
}

const onMediaChange = (val) => {
  store.commit('UPDATE_MV_VIDEO_MEDIA', val);
  eventBus.emit('effect-adjust-tuner-change', 'media', val)
  sendLog({
    event_type: 'click',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '调音',
      str3: '多媒体音量',
      str4: 'click',
    },
  });
}

const onVolumeChange = (val) => {
  store.commit('UPDATE_MV_VIDEO_VOLUME', val)
  eventBus.emit('effect-adjust-tuner-change', 'stream', val)
  sendLog({
    event_type: 'click',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '调音',
      str3: '伴奏音量',
      str4: 'click',
    },
  });
  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: 10093,
  //   event_data: {
  //     str1: '调音',
  //     str2: '伴奏',
  //     str3: '调节伴奏',
  //     str4: 'click',
  //   },
  // })
  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: 30109,
  //   event_data: {
  //     str1: '欢唱页',
  //     str2: '调音',
  //     str3: '伴奏音量',
  //     str4: 'click',
  //   },
  // })
}

// const onReverbChange = (val) => {
//   store.commit('UPDATE_VIDEO_REVERB_VOLUME', val);
//   eventBus.emit('effect-adjust-tuner-change', 'echo', val)
//   sendLog({
//     event_type: '10000~50000',
//     event_name: 30111,
//     event_data: {
//       str1: '欢唱页',
//       str2: '调音',
//       str3: '混响',
//       str4: 'click',
//     },
//   })
// }

 const handleSelect = (effect) => {
   TSMicrophoneInstance.onAudioEffectParams(effect.sound_type)
 }
 
</script>

<style lang="stylus" scoped>
.adj-volume
  width 100%
  height 100vh
  position relative
  &-main
    width 100%
    height auto
    
    &-hint
      font-size 28px
      color rgba(255, 255, 255, .4)
      margin-bottom 8px
    &-item
      display flex
      flex-direction column
      justify-content center
      align-items center
      padding 0px 0 0
      text-align center
      width calc(90px * var(--scale)) !important
      svg
        width 40px
        height 40px
        margin-bottom 6px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(26px * 1.48) !important
          height calc(26px * 1.48) !important
    .effect-container
      padding-left 48px
      display: flex;
      scrollbar-width: none;
      overflow-x auto;
      -webkit-overflow-scrolling: touch;
  .explosion-sound
    border-bottom 2px solid rgba(255, 255, 255, 0.1)
    padding-left 48px
    margin-bottom 24px
    h3
      color: rgba(255, 255, 255, 0.60);
      font-size: 28px;
      margin-bottom 40px;
    ul
      display flex
      overflow-x scroll
      padding-bottom 50px
      li
        margin-right 32px
        &.active
          div
            border-color #E3AB5D
          svg
            fill #E3AB5D
          p
            color #E3AB5D
      div
        width: 116px;
        height: 116px;
        border 1px solid rgba(255, 255, 255, 0.20)
        border-radius 50%
        display flex
        justify-content center
        align-items center
        svg
          fill #fff
      p
        color: rgba(255, 255, 255, 0.40);
        font-size: 26px;
        text-align center
        margin-top 24px
</style>