<template>
  <div class="mv-quality-side">
    <div class="mv-side-back">
      <div class="left">画质选择</div>
      <div
        class="close"
        @click="$emit('close')"
      >
        <DynamicIcon name="close" />
      </div>
    </div>
    <div class="mv-quality-side-main">
      <div
        class="mv-quality-side-main-item"
        :class="playingMvQuality == quality  && 'mv-quality-side-main-item-active'"
        v-for="(quality, index) in qualityList"
        :key="index"
        @click="handleChoose(quality)"
      >
        <p class="title">{{ quality }}</p>
        <p class="des">
          {{ qualityLog[quality][1] }}
        </p>
        <img v-if="quality === '1080'" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png">
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import useVip from '@/composables/useVip'
import useQuality from '@/composables/useQuality'
import get from 'lodash/get'
import { sendLog } from '@/directives/v-log/log'
import eventBus from '@/utils/event-bus'

const store = useStore()
const { showVipQrcode } = useVip()
const { useMvQuality } = useQuality()

const isLogin = computed(() => !!store.state.userInfo.unionid)
const isVip = computed(() => !!store.state.vipInfo.end_time)
const playingMvQuality = computed(() => store.state.playingMvQuality)
const qualityList = computed(() => store.state.availableQualities)
const currentSong = computed(() => get(store.state.orderedList, '[0]', {}));

// const qualityList = [
//   { name: '480P', label: '标清', value: '480' },
//   { name: '720P', label: '高清', value: '720' },
//   { name: '1080P', label: '蓝光', value: '1080' }
// ]

const qualityLog = {
  480: [10090, '标清'],
  720: [10091, '高清'],
  1080: [10092, '1080p']
}

// const themeClass = computed(() => store.state.themeClass)

const handleChoose = (quality) => {
  if (playingMvQuality.value === quality) return

  if (quality === '1080' && !isVip.value) {
    showVipQrcode({
      fr: isLogin.value ? 1841 : 1840,
      logData: {
        str1: 'MV页面',
        str2: '画质-1080P',
        str3: '触发 VIP 弹窗',
        str4: 'click',
        str7: currentSong.value.songid,
      },
      isLogin: isLogin.value
    })
    sendLog({
      event_type: 'show',
      event_name: 6012,
      event_data: {
        str1: '通用',
        str2: '画质弹窗',
        str3: 'VIP画质弹窗展示',
        str4: 'show',
      },
    })
    return
  }
  eventBus.emit('video-quality-change')

  sendLog({
    event_type: 'click',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '画质选择',
      str3: quality + 'p',
      str4: 'click',
    },
  });

  useMvQuality(quality)
}
</script>

<style lang="stylus" scoped>
.dark-theme
  .mv-quality-side
    --card-background linear-gradient(93.55deg, rgba(56, 58, 62, 0.9) 0.37%, rgba(56, 58, 62, 0.5) 98.8%);
    --card-active-background rgba(219, 174, 106, 0.1)

.mv-quality-side
  --card-background linear-gradient(93.55deg, rgba(255, 255, 255, 0.9) 0.37%, #F3F3F3 98.8%);
  --card-active-background rgba(160, 74, 240, 0.1)

  width 100%
  height 100vh
  padding 0 0px
  position relative
  &-main
    width 100%
    height auto
    display flex
    flex-direction column
    align-items center
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      margin-top calc(30px * 1.48) !important
    &-item
      width 704px
      height 180px
      margin-bottom 36px
      display flex
      flex-direction column
      align-items center
      justify-content center
      border-radius 12px
      background: var(--card-background)
      position relative
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(362px * 1.48) !important
        height calc(92px * 1.48) !important
      .title
        // color rgba(255, 255, 255, 0.8)
        font-size 48px
        font-weight 500
        height 66px
        margin-bottom 12px
        opacity 0.8
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          font-size calc(24px * 1.48) !important
      .des
        // color rgba(255, 255, 255, 0.5)
        // font-size 28px
        display flex
        align-items center
        justify-content center
        font-weight 400
        opacity 0.5
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          font-size calc(14px * 1.48) !important
      img
        width 56px
        height 28px
        margin-left 12px
        position absolute
        right 20px
        top 20px
      &-active
        border: 4px solid var(--highlight-color)
        background: var(--card-active-background)
      &-unactive
        display none
</style>