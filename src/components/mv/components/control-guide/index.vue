<template>
  <div class="mv-control-guide">
    <div class="menu-item" @click.stop="handleShowSideBar('快速点歌', 'left', 10070)">
      <!-- <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20231102/vadd/fa015ce36c8013015815d1a708a5075b.png"/> -->
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M28.5012 16.4817C31.544 19.5245 31.544 24.4579 28.5012 27.5008C28.4085 27.5935 28.3141 27.6833 28.2181 27.7703C28.1224 27.7972 28.0322 27.8483 27.9569 27.9236C27.9061 27.9744 27.8662 28.0321 27.8375 28.0937C24.7787 30.5288 20.3125 30.3312 17.4821 27.5008C14.4393 24.4579 14.4393 19.5245 17.4821 16.4817C20.525 13.4388 25.4584 13.4388 28.5012 16.4817ZM28.3776 29.1928C24.8557 31.8338 19.8368 31.5526 16.6336 28.3493C13.1221 24.8378 13.1221 19.1446 16.6336 15.6331C20.1451 12.1216 25.8383 12.1216 29.3497 15.6331C32.8612 19.1446 32.8612 24.8378 29.3497 28.3493C29.3299 28.3691 29.3101 28.3888 29.2902 28.4083L34.315 33.4331C34.5493 33.6674 34.5493 34.0473 34.315 34.2817C34.0806 34.516 33.7008 34.516 33.4664 34.2817L28.3776 29.1928Z" fill="white"/>
      </svg>

      <div class="menu-item-text">快速点歌</div>
    </div>
    <div class="mv-control-guide-center">
      <p>点击两侧都可以快速点歌喔~</p>
      <div @click="$emit('close-control-guide')">知道啦</div>
    </div>
    <div class="menu-item right" @click.stop="handleShowSideBar('快速点歌', 'right', 10070)">
      <!-- <img class="menu-item-icon" src="https://qncweb.ktvsky.com/20231102/vadd/fa015ce36c8013015815d1a708a5075b.png"/> -->
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M28.5012 16.4817C31.544 19.5245 31.544 24.4579 28.5012 27.5008C28.4085 27.5935 28.3141 27.6833 28.2181 27.7703C28.1224 27.7972 28.0322 27.8483 27.9569 27.9236C27.9061 27.9744 27.8662 28.0321 27.8375 28.0937C24.7787 30.5288 20.3125 30.3312 17.4821 27.5008C14.4393 24.4579 14.4393 19.5245 17.4821 16.4817C20.525 13.4388 25.4584 13.4388 28.5012 16.4817ZM28.3776 29.1928C24.8557 31.8338 19.8368 31.5526 16.6336 28.3493C13.1221 24.8378 13.1221 19.1446 16.6336 15.6331C20.1451 12.1216 25.8383 12.1216 29.3497 15.6331C32.8612 19.1446 32.8612 24.8378 29.3497 28.3493C29.3299 28.3691 29.3101 28.3888 29.2902 28.4083L34.315 33.4331C34.5493 33.6674 34.5493 34.0473 34.315 34.2817C34.0806 34.516 33.7008 34.516 33.4664 34.2817L28.3776 29.1928Z" fill="white"/>
      </svg>
      <div class="menu-item-text">快速点歌</div>
    </div>
  </div>
</template>

<script setup>
import { TSBaseInfoInstance } from '@/packages/TSJsbridge'
import { ref, defineEmits, computed, watch } from 'vue'
import { useStore } from 'vuex'

// 定义 props 和 emit
const emit = defineEmits(['show-side-bar', 'close-control-guide'])

// 使用 Vuex store
const store = useStore()
const mvIsHide = computed(() => store.state.mvIsHide)

// 定义响应式变量
const isVisible = ref(true)

// 处理显示侧边栏的逻辑
const handleShowSideBar = (name, pos, logname) => {
  emit('show-side-bar', {
    name,
    pos: pos
  })
  emit('close-control-guide')

  TSBaseInfoInstance.controlMVScaling(pos === 'left' ? 1 : 0)
}

// 监听 mvIsHide 的变化
watch(mvIsHide, (newValue) => {
  if (!newValue) {
    setTimeout(() => {
      isVisible.value = false
      emit('close-control-guide')
    }, 5000)
  }
})
</script>

<style lang="stylus" scoped>
.mv-control-guide
  width 100vw
  height 100vh
  position fixed
  left 0
  top 0
  z-index 20
  background: rgba(0, 0, 0, 0.90);
  display flex
  justify-content center
  align-items center
  .menu-item
    position fixed
    top 50%;
    left 40px
    margin-top -65px
    width: 130px;
    height: 130px;
    border-radius: 12px;
    background: #282427
    display flex
    flex-direction column
    justify-content center
    align-items center
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(72px * 1.48) !important
      height calc(60px * 1.48) !important
    svg
      width 72px
      height 72px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(72px * 1.48) !important
        height calc(60px * 1.48) !important
    *
      font-size: var(--font-size-extra-small)
      color #FFFFFF80
    &-icon
      width 80px
      height auto
    &.right
      left unset
      right 40px
  &-center
    background url(https://qncweb.ktvsky.com/20231215/other/48e937945a6c6cc7f241e7873fb8b10c.png) no-repeat top center
    background-size 132px auto
    text-align center
    padding-top 60px
    p
      color: rgba(255, 255, 255, 0.70);
      font-size: calc(18px * 1.5)
      padding 0px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        font-size: calc(18px * 1.48) !important
    div
      width: 300px;
      height: 100px;
      border-radius: 200px;
      background: rgba(255, 255, 255, 0.10);
      font-size: calc(18px * 1.5)
      color: rgba(255, 255, 255, 0.80);
      display flex
      align-items center
      justify-content center
      margin 50px auto 0
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        font-size: calc(18px * 1.48) !important
</style>
