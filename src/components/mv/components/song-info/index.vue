<template>
  <div
    v-if="showMarquee"
    :class="['song-info flex-center', { full: isFullMode }]" 
  >
    <Vue3Marquee
      :duration="10"
      :loop="3"
      @onLoopComplete="handleMarqueeComplete"
    >
      <div class="song-info-marquee flex">
        <div class="song-info-content flex">
          <span class="flex">
            正在演唱：{{ currentSong.music_name }}
            <img
              v-show="currentSong.is_vip"
              src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png"
              alt=""
            >
          </span>
          <span>下一首：{{ nextSong.music_name || '没有歌曲啦，快去点歌吧' }}</span>
        </div>
      </div>
    </Vue3Marquee>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import get from 'lodash/get';
import { Vue3Marquee } from 'vue3-marquee';
import eventBus from '@/utils/event-bus'

const store = useStore();
const currentSong = computed(() => get(store.state.orderedList, '[0]', {}));
const nextSong = computed(() => get(store.state.orderedList, '[1]', {}));
const isFullMode = computed(() => store.state.mvMode.mode !== 'mv' && store.state.currentSize === 'full');
const showMarquee = ref(true); // 控制跑马灯的显示与隐藏

let timer = null; // 定时器

// 跑马灯展示完成后触发
const handleMarqueeComplete = () => {
  showMarquee.value = false; // 隐藏跑马灯
  timer = setTimeout(() => {
    showMarquee.value = true; // 30 秒后再次展示跑马灯
  }, 30000); // 30 秒
};

const onChangePlaySong = () => {
  clearTimeout(timer);
  showMarquee.value = true;
}

// 组件挂载时启动定时器
onMounted(() => {
  showMarquee.value = true; // 每 30 秒展示一次跑马灯
  eventBus.on('chang-playsong', onChangePlaySong)
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearTimeout(timer);
  }
});
</script>

<style lang="stylus" scoped>
.song-info
  position fixed
  width 800px
  height 100px
  top 40px
  left 50%
  margin-left -400px
  background: #1E1F21B2;
  backdrop-filter: blur(30px) 
  border-radius 14px
  color #FFFFFF
  padding 0 40px
  &.full
    top 70px
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width calc(480px * 1.48) !important
    height calc(72px * 1.48) !important
    left 41.1%
    // 三分之一屏
  @media (max-width: 700px)
    width calc(600px * 1.48) !important
    left 41.1%

  &-content
    width auto !important
    margin-right 50px
  img
    width 50px
    margin-left 12px
  span:last-child
    color #FFFFFF80
    margin-left 80px
    white-space nowrap
  *
    color #FFFFFF
</style>