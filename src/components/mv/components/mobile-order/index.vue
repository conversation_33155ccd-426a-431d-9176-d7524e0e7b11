<template>
  <div
    v-if="isShowQRcode"
    class="mobile-order"
    :class="{ full: isFullMode }"
    >
    <div class="mobile-order-modal">
      <div class="qrcode">
        <img :src="qrCodeURL" />
      </div>
      <div class="title" @click="handleClose">
        微信扫码点歌
        <img
          src="https://qncweb.ktvsky.com/20231027/vadd/34a27f5bf5e20eb04915dd081edf29b0.png"
        />
      </div>
    </div>
  </div>
  <div
    v-else 
    class="mv-mobile-order"
    :class="{ full: isFullMode }"
    @mousedown.prevent
  >
    <!-- <img
      @click="handleOpen"
      :src=" currentSize !== 'medium' ? 'https://qncweb.ktvsky.com/20231027/vadd/bd2b5666419511daafaae33bc88518b8.png' : 'https://qncweb.ktvsky.com/20250114/vadd/9b9bba246c9b92e8f91f05a8822bfece.png' "
    /> -->
    <div class="order-icon" @click="handleOpen">
      <img src="https://qncweb.ktvsky.com/20250214/vadd/16b051c2a83ccffcc669dab373bfd14e.png"/>
      <p>手机点歌</p>
    </div>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from 'vue'
import useQRCode from '@/composables/useQRCode'
import { sendLog } from '@/directives/v-log/log'
import store from '@/store';

const { getQRCodeURL } = useQRCode();
const qrCodeURL = ref('');
const isShowQRcode = ref(false);
const phone_control_qr = computed(() => store.state.base.phone_control_qr);
const currentSize = computed(() => store.state.currentSize);
const isFullMode = computed(() => store.state.mvMode.mode !== 'mv' && store.state.currentSize === 'full');

const fetchQRCode = async () => {
  const qrCodeData = await getQRCodeURL(phone_control_qr.value);
  if (qrCodeData) {
    qrCodeURL.value = qrCodeData;
  }
};

const handleClose = () => {
  isShowQRcode.value = false;
  sendLog({
    event_type: 'click',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '手机点歌',
      str3: '收起',
      str4: 'click',
    },
  });
};

const handleOpen = async () => {
  await fetchQRCode();
  isShowQRcode.value = true;
  sendLog({
    event_type: 'click',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '手机点歌',
      str3: '打开',
      str4: 'click',
    },
  });
};

onBeforeMount(fetchQRCode);
</script>

<style lang="stylus" scoped>
.mobile-order
  position absolute
  top 30px
  right 30px
  &.full
    top 70px 
  z-index 11
  &-modal
    width 226px
    height 276px
    border-radius 14px
    background rgba(30, 31, 33, 0.8)
    display flex
    flex-direction column
    align-items center
    justify-content center
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(140px * 1.48) !important
      height calc(174px * 1.48) !important
    .qrcode
      width 186px
      height 186px
      display flex
      justify-content center
      align-items center
      background #ffffff
      border-radius 10px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(114px * 1.48) !important
        height calc(114px * 1.48) !important
      img
        width 166px
        height 166px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(110px * 1.48) !important
          height calc(110px * 1.48) !important
    .title
      width 186px
      height 30px
      display flex
      align-items center
      justify-content space-between
      color rgba(255, 255, 255, 0.80)
      font-size 24px
      margin-top 21px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        zoom 1.5
      img
        width 30px
        height 30px
  @media screen and (max-width 1200px) and (min-height 1200px)
    top 196px
    right 24px
.mv-mobile-order
  width 156px
  height 130px
  position absolute
  top 40px
  right 0
  z-index 11
  &.full
    top 70px 
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width calc(90px * 1.48) !important
    height calc(72px * 1.48) !important
  img
    width 100%
    height 100%
  .order-icon
    display flex
    height 100%
    justify-content center
    align-items center
    flex-direction column
    background rgba(30, 31, 33, 0.7)
    border-radius 90px 0 0 90px
    backdrop-filter: blur(30px)
    padding-left 22px
    p
      color rgba(255, 255, 255, 0.8)
      font-size 24px
      @media (max-width: 900px) and (min-width: 701px)  
        font-size 20px!important
    img
      width 52px
      height 52px
      margin-top -5px
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(32px * 1.48) !important
        height calc(32px * 1.48) !important
  @media screen and (max-width 1200px) and (min-height 1200px)
    top 196px
</style>
