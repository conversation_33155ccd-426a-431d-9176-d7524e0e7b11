<template>
  <div class="fast-order-page" :class="currPageTab" v-show="pageViewName === 'searchTabPage'">
    <div v-if="isSearch" class="mv-side-back">
      <div class="left flex" @click="handleBackFastSearch">
        <DynamicIcon name="back" />
        搜索
      </div>
      <div
        class="close"
        @click="$emit('close')"
      >
        <DynamicIcon name="close" />
      </div>
    </div>
    
    <div v-else class="mv-side-tab">
      <div class="tab">
        <div
          class="tab-item" 
          :class="{ 'actived': currPageTab === 'search' }" 
          @click="handleSwitchPageTab('search')"
        >
          快速点歌
        </div>
        <div
          class="tab-item" 
          :class="{ 'actived': currPageTab === 'singer' }" 
          @click="handleSwitchPageTab('singer')"
          v-log="{
            event_type: 'click',
            event_name: '6007',
            event_data: {
              str1: '欢唱页',
              str2: '快速点歌',
              str3: '歌手点歌',
              str4: 'click',
            }
          }"
        >
          歌手点歌
        </div>
      </div>
      <div
        class="close"
        @click="$emit('close')"
      >
        <DynamicIcon name="close" />
      </div>
    </div>
    
    <div class="fast-order-page-main" v-show="currPageTab === 'search'" :class="showSearchGuess && 'search-guess'">
      <SearchBar
        ref="searchInputRef"
        :isSearch="isSearch"
        @go-search="handleGoSearch"
        @search="handleSearch"
      />
      
      <div class="search-list flex-column" v-if="keyword">
        <div class="global-tab flex-center">
          <div 
            class="item" 
            @click="handleSwitchSearchTab('song')"
          >
            <div class="item-txt" 
            :class="{ 'active': curSearchTab === 'song' }">歌曲</div>
            <span></span>
          </div>
          <div 
            class="item" 
            @click="handleSwitchSearchTab('singer')"
          >
            <div class="item-txt" 
            :class="{ 'active': curSearchTab === 'singer' }">歌手</div>
          </div>
        </div>
        
        <div v-if="curSearchTab === 'song'" class="song-list">
          <LoadMore
            v-if="resultData.song.length"
            @load-more="getSearchResult"
          >
            <SongItem
              v-for="(songItem, index) in resultData.song"
              :key="index"
              :songItem="songItem"
              :order-log="{
                str1: '欢唱页',
                str2: '快速点歌-搜索结果',
              }"
              :logData="logData"
              :isStickButton="false"
              :index="index"
              @singer-click="handleClickSinger"
            />
          </LoadMore>
          <p class="hint" 
             v-if="isEmpty && resultData.song.length">
            已加载全部
          </p>
        </div>
        
        <div v-else class="song-list">
          <LoadMore
            class="singer-list singer-list-padding"
            ref="loadMoreRef"
            v-if="resultData.singer.length"
            @load-more="getSearchResult"
          >
            <SingerItem
              v-for="(item, index) in resultData.singer"
              :key="index"
              :singer="item"
              @click="handleClickSinger(item, 'search')"
            />
          </LoadMore>
        </div>
      </div>
      <div 
        v-show="!isSearch || showSearchGuess" 
        style="flex: 1; overflow-y: scroll;overflow-x: hidden;"
      >
        <SearchHistory
          v-if="isSearch && !keyword"
          @clickWord="handleChangeSearchInput"
          @delete-word="handleDeleteSearchWord"
          from="mv"
        />
        
        <div
          class="empty"
          v-if="isEmptyResult"
        >
          <DynamicIcon name="empty" useTheme="true" />
          <p>抱歉，暂无"{{ keyword }}"的结果</p>
        </div>
        <GuessSongList
          :isFastList="true"
          class="guess-song-list"
          showAllData
          pageRoute="3"
          :song_list_source="11"
          :ponitActionLog="{ }"
          renderType="list"
          :isStickButton="false"
          @singer-click="handleClickSinger"
          :noPadding="true"
          from="mv"
        />
      </div>
    </div>
    
    <SingerOrder 
      v-show="currPageTab === 'singer'"
      :currPageTab="currPageTab" 
      @singer-click="handleClickSinger"
    />
  </div>
  
  <SingerDetail 
    v-if="pageViewName === 'singerDetail'" 
    :singerData="singerData" 
    @close="handleClose" 
    @back="handlechangePageViewName" 
  />
</template>

<script setup>
import { ref, watch, computed, defineEmits } from 'vue'
import { useStore } from 'vuex'
import SearchBar from './search.vue'
import SongItem from '@/components/song-item/index.vue'
import SingerItem from '@/components/singer-item/index.vue'
import GuessSongList from '@/components/guess-song/songlist.vue'
import SearchHistory from '@/components/search-history/index.vue'
import SingerOrder from './../singer-order/index.vue'
import SingerDetail from './../singer-detail/index.vue'
import { search } from '@/service/search'
import { sendLog } from '@/directives/v-log/log'
import { setSearchCache } from '@/utils/historyCache'
import get from 'lodash/get'

const store = useStore()
const emit = defineEmits(['click-retry', 'close'])

// Reactive state
const pageViewName = ref('searchTabPage')
const singerData = ref({ singerid: '', name: '', image: '' })
const currPageTab = ref('search')
const searchInputRef = ref(null)
const isSearch = ref(false)
const keyword = ref('')
const curSearchTab = ref('song')
const isEmpty = ref(false)
const resultData = ref({ singer: [], song: [] })
const pagination = ref({ singer: 1, song: 1 })
const isRequest = ref(false)
const isInit = ref(true)
const logData = {
  vipLog: {
    str1: 'MV 页面',
    str2: '快速点歌-搜索结果-歌曲',
    str3: '点击 VIP 歌曲',
  },
  fr: [1836, 1837],
}

// Computed properties
const searchCacheList = computed(() => store.state.search.searchCache)
const showSearchGuess = computed(() => {
  if (!isSearch.value) {
    return false
  }
  if (!keyword.value) {
    return true
  }
  if (curSearchTab.value == 'song') {
    return !resultData.value.song.length && !isInit.value
  }
  if (curSearchTab.value == 'singer') {
    return !resultData.value.singer.length && !isInit.value
  }
  return false
})

// 计算属性
const isEmptyResult = computed(() => {
  return keyword.value && !isRequest.value && !isInit.value && 
         ((curSearchTab.value === 'song' && resultData.value.song.length <= 0) || 
          (curSearchTab.value === 'singer' && resultData.value.singer.length <= 0));
});

// Methods
const handleGoSearch = () => {
  isSearch.value = true
  sendLog({
    event_type: '10000~50000',
    event_name: 10080,
    event_data: {
      str1: '快速点歌',
      str2: '搜索',
      str3: '点击搜索栏',
      str4: 'click',
    },
  })
}

const handleBackFastSearch = () => {
  isSearch.value = false
  curSearchTab.value = 'song'
  keyword.value = ''
  resultData.value = { singer: [], song: [] }
  if (searchInputRef.value) searchInputRef.value.changeInput('', true)
}

const handleSearch = async (k) => {
  keyword.value = k
}

const handleChangeSearchInput = (v) => {
  if (searchInputRef.value) searchInputRef.value.changeInput(v)
  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: 10084,
  //   event_data: {
  //     str1: '搜索',
  //     str2: '搜索历史',
  //     str3: '点击歌曲',
  //     str4: 'click',
  //   },
  // })
}

const handleDeleteSearchWord = (v) => {
  const eventName = v !== -1 ? 10085 : 10086 // 10085: 单首删除, 10086: 全部删除
  sendLog({
    event_type: '10000~50000',
    event_name: eventName,
    event_data: {
      str1: '搜索',
      str2: '搜索历史',
      str3: v !== -1 ? '单首删除' : '全部删除',
      str4: 'click',
    },
  })
}

const getSearchReportStatus = (res) => {
  if (res.errcode !== 200) return 12
  return res.singer.length || res.song.length ? 10 : 11
}

const setSearchSongList = (list) => {
  if (keyword.value && !resultData.value.song.length) {
    list.some(v => {
      const musicName = get(v, 'music_name', '').split('(HD)')[0]
      if (musicName === keyword.value) {
        store.dispatch('search/addSearchSong', { ...v, searchname: keyword.value })
        return true
      }
      return false
    })
  }
}

const setSearchCacheList = (k) => {
  const trimmedKeyword = k.trim()
  let newSearchCacheList = [trimmedKeyword, ...searchCacheList.value.filter(item => item !== trimmedKeyword)]
  newSearchCacheList = newSearchCacheList.slice(0, 10)
  store.dispatch('search/updateSearchCache', newSearchCacheList)
  setSearchCache(newSearchCacheList)
}

const toSongvipSort = (arr) => {
  const vipSongs = arr.filter(song => song.is_vip)
  const nonVipSongs = arr.filter(song => !song.is_vip)
  return [...vipSongs, ...nonVipSongs]
}

const searchHandler = {
  singer: async () => {
    const res = await search(keyword.value, pagination.value.singer, 'singer')
    sendLog({
      event_type: 'click',
      event_name: 122,
      event_data: {
        key_words: keyword.value,
        status: getSearchReportStatus(res),
      },
    })
    if (res.singer.length) {
      resultData.value.singer = resultData.value.singer.concat(res.singer)
      pagination.value.singer++
    }
    isRequest.value = false
    isInit.value = false
  },
  song: async () => {
    const res = await search(keyword.value, pagination.value.song, 'song')
    sendLog({
      event_type: 'click',
      event_name: 122,
      event_data: {
        key_words: keyword.value,
        status: getSearchReportStatus(res),
      },
    })
    if (res.song.length) {
      const sortedSongs = toSongvipSort(res.song)
      setSearchSongList(sortedSongs)
      resultData.value.song = resultData.value.song.concat(sortedSongs)
      pagination.value.song++
    }
    isRequest.value = false
    isInit.value = false
  },
}

const getSearchResult = async () => {
  if (isRequest.value) return
  isRequest.value = true
  await searchHandler[curSearchTab.value]()
}

const handleSwitchSearchTab = (tab) => {
  curSearchTab.value = tab
  isInit.value = true
  if (!resultData.value[tab].length) {
    searchHandler[tab]()
  }
}

const handleSwitchPageTab = (tab) => {
  currPageTab.value = tab
}

const handleClickSinger = ({ singer, singerhead, singerid, from }, type) => {
  if (type !== 'search') {
    sendLog({
      event_type: 'click',
      event_name: '6007',
      event_data: {
        str1: '欢唱页',
        str2: '快速点歌',
        str3: '歌手区域',
        str4: 'click',
        str5: singerid,
      }
    })
  }
  singerData.value = {
    singerid,
    name: singer,
    image: singerhead,
    isSearch: type === 'search',
    from,
  }
  pageViewName.value = 'singerDetail'
}

const handleClose = () => {
  emit('close')
}

const handlechangePageViewName = () => {
  singerData.value = {
    singerid: '',
    name: '',
    image: '',
  }
  pageViewName.value = 'searchTabPage'
}

// Watchers
watch(keyword, (k) => {
  if (k) {
    resultData.value = { singer: [], song: [] }
    pagination.value = { singer: 1, song: 1 }
    getSearchResult()
    setSearchCacheList(k)
  }
})
</script>

<style lang="stylus" scoped>
.dark-theme
  .fast-order-page
    --tab-background #00000033

.fast-order-page
  --tab-background #1D1D1F14

  backdrop-filter: blur(20px)
  width 100%
  height 100vh
  position relative
  display flex
  flex-direction column

  :deep(.section-container)
    .sec-gusse-sing-list
      padding 0px
      @media (max-width: 900px) and (min-width: 701px)  
        grid-template-columns: repeat(1, 1fr) !important;
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      margin-top calc(24px * var(--scale)) !important
      .section-container-header
        padding-bottom 0px
        padding-bottom 0px

  :deep(.common-singer-item)
    margin-bottom 0px
  :deep(.section-container)
    margin 46px 0px 0px
    padding 0px 48px
    flex 1
    overflow hidden
    .section-container-list
      height 97%
      overflow-y scroll
    .sec-gusse-sing-list
      grid-template-columns repeat(1, 100%)
      padding-bottom 50px
    .section-container-header-title
      font-size var(--font-size-large)
      color var(--text-secondary-color)
      margin-bottom 0px
  .search-guess
    .empty
      padding 100px 0 50px
    .song-list
      display none
    .search-list
      flex none
    :deep(.section-container)
      flex none
      overflow scroll
      height fit-content !important
      .section-container-list
        height fit-content !important
  .mv-side-tab
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      margin-top calc(20px * 1.48) !important
    .tab
      background var(--tab-background)
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(290px * 1.48) !important
        height calc(58px * 1.48) !important
        &-item
          width calc(136px * 1.48) !important
          height calc(50px * 1.48) !important
  &-title
    color rgba(255, 255, 255, 0.8)
    font-size 40px
    margin 71px 0 53px
    width 200px
    height 48px
    line-height 48px
    font-weight 600
  &-back
    color rgba(255, 255, 255, 0.8)
    font-size 32px
    display flex
    align-items center
    margin 64px 0 84px 48px
    width 200px
    height 48px
    svg
      width 36px
      height 36px
      margin-right 30px
  .singer-order
    width 202px
    height 90px
    color #fff
    opacity 0.8
    font-size 28px
    display flex
    align-items center
    justify-content center
    position absolute
    top 43px
    right 158px
    background #383A3E
    border-radius 14px
    img
      width 29.6px
      height 28.8px
      margin-right 9px
  &-main
    width 100%
    flex 1
    padding 0
    display flex
    flex-direction column
    overflow hidden
    :deep(.search-history)
      margin 32px 48px 0
      &-content
        max-height 300px
        overflow-y scroll
    .search-list
      flex 1
      overflow hidden
      .global-tab
        margin-top 20px
        .item-txt
          padding 21px 15px
        span
          opacity 0
          margin 0 75px
      .song-list
        flex 1
        overflow hidden
        margin-top 0px
        height 100% !important
      .empty
        display flex
        flex-direction column
        justify-content center
        align-items center
        margin-top 30px
        font-size 28px
        color rgba(255, 255, 255, 0.40)
        text-align center
        svg
          width 80px
          height 80px
          margin-bottom 40px
        p
          height 32px
          line-height 32px
      .hint
        text-align center
        color #555555
      .singer-list
        margin 0 auto
        display grid
        grid-template-columns repeat(3, 206px)
        row-gap 42px
        justify-content space-between
        padding-top 12px
        padding-left 48px
        padding-right 48px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          row-gap 0 !important
          grid-template-columns repeat(3, calc(105px * 1.48)) !important
        :deep(.common-singer-item)
          width 206px
          margin-bottom 0px !important
          font-size 24px
          margin-right 45px
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)   
            margin-right calc(4px * var(--scale)) !important
            width calc(104px * var(--scale)) !important
            height calc(160px * var(--scale)) !important
          &:nth-child(4n)
            margin-right 0
          .singer-item-cover
            width 206px
            height 206px
            margin-bottom 20px
            // 三分之二屏
            @media (max-width: 900px) and (min-width: 701px)   
              width calc(104px * var(--scale)) !important
              height calc(104px * var(--scale)) !important
          p
            width 240px
            text-align center
      .singer-list-padding
        padding-left 48px !important
        padding-right 48px !important
        padding-bottom 150px !important
  .guess-song-list
    :deep(.song-item)
      .left
        &:active
          &:after
            width calc(660px * 1.48) !important
            height calc(122px * 1.48) !important
            margin-left -10px !important
</style>
