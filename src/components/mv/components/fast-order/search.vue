<template>
  <div class="fast-search" :class="[isSearch && 'pos-search']">
    <div class="fast-search-main" @click="handleToSearch">
      <DynamicIcon name="search" />
      <input
        type="text"
        placeholder="搜索歌曲、歌手"
        v-model="keyword"
        class="fast-search-input"
        :disabled="!isSearch"
        ref="searchInput"
        maxlength="20"
        autocapitalize="off"
        @keydown="handleSearchKeydown($event)"
        @mousedown="disableDoubleClickSelection"
      >
      <div
        v-show="keyword"
        class="clear"
        @click.stop="keyword = ''"
      >
        <DynamicIcon name="close" />
        <!-- <img v-show="themeClass === 'themeLight'" src="@/assets/close.png" />
        <img v-show="themeClass !== 'themeLight'" src="@/assets/close_dark.png" /> -->
      </div>
      <div v-show="!isSearch" class="fast-search-mask"></div>
    </div>
    <button v-if="isSearch" class="fast-search-btn flex-center" @click="handleSearch">
      搜索
    </button>
  </div>
</template>

<script setup>
import { ref, computed, defineEmits, defineExpose } from 'vue'
import { useStore } from 'vuex'
import Toast from '@/utils/toast'
import { useAttrs } from 'vue'
import { sendLog } from '@/directives/v-log/log';

const store = useStore()
const attrs = useAttrs()
let keyword = ref('')
const searchInput = ref(null)
const isSearch = computed(() => attrs.isSearch)

const imgs = {
  themeDark: {
    icon: require('@/assets/fast-search-dark.png'),
  },
  themeLight: {
    icon: require('@/assets/fast-search-light.png'),
  },
  themeSystem: {
    icon: require('@/assets/fast-search-dark.png'),
  },
}

const themeClass = computed(() => store.state.themeClass)

const emit = defineEmits(['go-search', 'search'])

const handleToSearch = () => {
  if (isSearch.value) return

  emit('go-search')
  
  setTimeout(() => {
    searchInput.value.focus()
  }, 0)
  sendLog({
    event_type: 'click',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '快速点歌',
      str3: '搜索栏',
      str4: 'click',
    },
  })
}

const handleSearch = () => {
  if (!keyword.value || !keyword.value.trim()) {
    Toast('请输入搜索内容')
    emit('search', '')
    return
  }
  emit('search', keyword.value)
  searchInput.value.blur()
  sendLog({
    event_type: 'click',
    event_name: 6007,
    event_data: {
      str1: '欢唱页',
      str2: '搜索结果',
      str3: '点击搜索',
      str4: 'click',
    },
  })
}

const handleSearchKeydown = (e) => {
  if (e.keyCode == 13) {
    handleSearch()
  }
}

const changeInput = (e, isForceSearch) => {
  keyword.value = e
  if (isForceSearch) return
  handleSearch()
}

const disableDoubleClickSelection = (event) => {
  if (event.detail > 1) {
    event.preventDefault();
  }
}

defineExpose({
  changeInput 
})
</script>

<style lang="stylus" scoped>
.fast-search
  display flex
  align-items center
  justify-content space-between
  margin 20px 48px 0
  .svg-icon
    width 42px
    aspect-ratio: 1 / 1; /* 1:1 的宽高比，高度等于宽度 */
    // height 42px
    margin 0 12px 0 30px

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(28px * 1.48) !important
  &-main
    display flex
    align-items center
    position relative
    width 100%
    height 75px
    border 2px solid var(--border-color2)
    border-radius 20px
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      height calc(50px * 1.48) !important
  .clear
    width auto
    position absolute
    right 20px
    top 50%
    margin-top -18px
    .svg-icon
      width 36px
      height 36px
      margin 0px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(36px * 1.48) !important
        aspect-ratio: 1 / 1; /* 1:1 的宽高比，高度等于宽度 */
  &-input
    flex 1
    height 75px
    display flex
    align-items center
    overflow hidden
    margin-right 55px
  &-mask
    position absolute
    top 0
    right 0
    left 0
    bottom 0
    width 100%
    height 100%
  &-btn
    width 180px
    height 75px
    border-radius 18px
    // backdrop-filter blur(100px)
    background: var(--highlight-color)
    color: var(--popup-background-color)
    // text-align center
    font-size var(--font-size-medium)
    // font-weight 400
    margin-left 30px
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(180px * 1.48) !important
      height calc(50px * 1.48) !important
  // .width_480
  //   width 480px
.pos-search
  margin-top 0px
  .fast-search-main
    width 495px
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(500px * 1.48) !important
</style>