<template>
    <div class="list-item" :class="{selected: isSelected}" @click="selectEffect">
        <div class="item-name" :class="{item_name_selected: isSelected}">{{ effect.sound_name }}</div>
    </div>
</template>

<script>
export default {
    name: 'EffectItem',
    props: {
        effect: {
            type: Object,
        },
        isSelected: {
            type: Boolean,
            default: false,
        }
    },
    methods: {
        selectEffect() {
            this.$emit('select', this.effect);
        }
    }
}
</script>

<style lang="stylus" scoped>
.list-item
  overflow-x: auto;
  white-space: nowrap;
  flex-shrink: 0;
  margin-bottom: 32px;
  margin-right: 20px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid var(--text-tertiary-color) ;
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    margin-right: 32px!important;
    width calc(60px * 1.48) !important
    height calc(60px * 1.48) !important
    border: calc(2px * 1.48) solid var(--text-tertiary-color)!important;

.selected
  border: 2px solid rgba(219, 174, 106, 1);
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    border: calc(2px * 1.48) solid rgba(219, 174, 106, 1)!important;

.item-name
  font-size: 20px;
  text-align: center;
  color: var(--text-tertiary-color) ;

.item_name_selected
  color: rgba(219, 174, 106, 1);

.theme-themeLight
  .list-item 
    border-color: rgba(29, 29, 31, 0.2);
  
  .selected 
    border: 2px solid rgba(160, 74, 240, 1);
  
  .item-name 
    font-size: 20px;
    text-align: center;
    color: var(--text-tertiary-color);
  
  .item_name_selected 
    color: rgba(160, 74, 240, 1);
  
</style>
