<template>
  <div class="side-order-list" v-if="pageViewName === 'orderTabPage'">
    <div class="mv-side-tab">
      <div class="tab">
        <div
          class="tab-item"
          v-for="(tab, index) in tabList"
          :key="index"
          :class="{ actived: curTab.name === tab.name }"
          @click="handleChangeTab(tab)"
          v-log="{
            event_type: 'click',
            event_name: '6007',
            event_data: {
              str1: '欢唱页',
              str2: '已点',
              str3: `${tab.keyword}tab`,
              str4: 'click',
            }
          }"
        >
          {{ tab.text }}
        </div>
      </div>
      <div
        class="close"
        @click="$emit('close')"
      >
        <DynamicIcon name="close" />
      </div>
    </div>
    <component
      :is="currentComponent"
      @singer-click="handleClickSinger"
      from="mvOrder"
    ></component>
  </div>
  <SingerDetail
    v-if="pageViewName === 'singerDetail'"
    :singerData="singerData"
    @close="$emit('close')"
    @back="handlechangePageViewName"
  />
</template>

<script setup>
import { ref, defineEmits, computed } from 'vue'
import { useStore } from 'vuex'
import AlreadySongList from '@/components/song-list/already/index.vue'
import OrderSongList from '@/components/song-list/order/index.vue'
import SingerDetail from './../singer-detail/index.vue'
import { sendLog } from '@/directives/v-log/log'

const emit = defineEmits(['close'])

const currentComponent = computed(() => {
  return curTab.value.name === 'ordered' ? OrderSongList : AlreadySongList;
});

const store = useStore()
const orderedListNum = computed(() => store.state.orderedList.length)
const alreadyListNum = computed(() => store.state.alreadyList.length)
const tabList = computed(() => {
  return [{
    name: 'ordered',
    text: `已点(${orderedListNum.value > 99 ? '99' : orderedListNum.value})`,
    keyword: '已点',
  }, {
    name: 'already',
    text: `已唱(${alreadyListNum.value > 99 ? '99' : alreadyListNum.value})`,
    keyword: '已唱',
  }]
})
let curTab = ref(tabList.value[0])

let pageViewName = ref('orderTabPage')
let singerData = ref({
  singerid: '',
  name: '',
  image: '',
}) // 侧边栏 - 歌手详情 - 歌手数据

const handleChangeTab = (tab) => {
  if (curTab.value.name === tab.name) return
  curTab.value = tab
  
  if (tab.text === '已唱') {
    sendLog({
      event_type: '10000~50000',
      event_name: 10061,
      event_data: {
        str1: '已点',
        str2: '已唱',
        str3: '进入已唱',
        str4: 'click',
      },
    })
  }
}

const handleClickSinger = ({singer, singerhead, singerid}) => {
  singerData.value = {
    singerid,
    name: singer,
    image: singerhead,
  }
  pageViewName.value = 'singerDetail'
}

const handlechangePageViewName = () => {
  singerData.value = {
    singerid: '',
    name: '',
    image: '',
  }
  pageViewName.value = 'orderTabPage'
}

</script>
<style lang="stylus" scoped>
.dark-theme
  .side-order-list
    --background #23202C
    --tab-background #00000033

.side-order-list
  --tab-background #1D1D1F14

  width 100%
  height 100vh
  padding 0
  position relative
  .tab
    background var(--tab-background) !important
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(267px * 1.48) !important
      height calc(58px * 1.48) !important
      &-item
        width calc(136px * 1.48) !important
        height calc(50px * 1.48) !important
  :deep(.song-list)
    .tip
      bottom -50px
  .empty
    margin-top 375px !important
    padding-top 0 !important
  :deep(.song-list)
    height calc(100% - 220px)
  .close-side
    position absolute
    top 53px
    right 48px
    width 32px
    height 32px
    img
      width 100%
      height 100%
</style>
