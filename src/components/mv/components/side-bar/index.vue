<template>
  <transition ref="root" name="slide-transition">
    <div class="mv-side" :style="posStyle" :data-pos="pos">
      <slot name="default"></slot>
    </div>
  </transition>
</template>

<script setup>
import { defineProps, toRefs, computed } from 'vue'
// import { useShareBrowserSize } from '@/composables/sharedComposable'

const props = defineProps({
  pos: {
    type: Number,
    default: 0, // 0 不显示 1 右侧显示 2 左侧显示
  }
})

// const { browserType } = useShareBrowserSize()
const { pos } = toRefs(props)
// let posMaps = {
//   0: { 'width': 0 },
//   1: { 'width': '533px', 'right': 0 },
//   2: { 'width': '533px', 'left': 0 },
//   3: { 'height': '56vh' }
// }
const posStyle = computed(() => {
  if (pos.value === 0) {
    return {
      right: '-99999px'
    }
  }
  return {
    'right': pos.value === 1 ? 0 : 'unset',
    'left': pos.value === 1 ? 'unset' : 0
  }
})

</script>

<style lang="stylus" scoped>
.dark-theme
  .mv-side
    --background rgba(35, 32, 44, 1)
.mv-side
  width 800px
  --background rgba(232, 234, 238, 1)
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width calc(384px * 1.48) !important
  :deep(.song-item)
    .left
      max-width 68%

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        max-width 60%

  height 100vh
  background var(--background)
  display flex
  flex-direction column
  align-items center
  overflow hidden
  position absolute
  top 0
  z-index 12
  :deep(.mv-side-back)
    display flex
    justify-content space-between
    align-items center
    height 147px
    padding 0 48px
    position relative
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      margin-top calc(12px * 1.48) !important
      padding 0 calc(32px * 1.48) !important
    .close
      position absolute
      right 48px
      top 50%
      transform translateY(-50%)
    .left
      font-size 30px
      opacity 0.8
      @media (max-width: 900px) and (min-width: 701px)  
        font-size  calc(20px * 1.48) !important
      .svg-icon
        margin-right 30px
    .svg-icon
      width 33px
      height 33px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(24px * 1.48) !important
        height calc(24px * 1.48) !important
  :deep(.mv-side-tab)
    width 100%
    margin 32px auto 16px
    position relative
    .close
      position absolute
      top 36px
      right 26px
      z-index 10
      width 33px
      height 33px

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        top calc(18px * 1.48) !important
        width calc(24px * 1.48) !important
        height calc(24px * 1.48) !important
    .tab
      width: 506px;
      height: 100px;
      border-radius: 28px;
      background var(--tab-button-background-color)
      display flex
      align-items center
      justify-content space-between
      padding 0 8px
      position relative
      margin 0 auto
      
      &-item
        width 240px
        height 84px
        display flex
        align-items center
        justify-content center
        font-size var(--font-size-large)
        &.actived
          background var(--highlight-color)
          color var(--popup-background-color)
          border-radius 20px

</style>