<template>
  <div class="singer-detail-side">
    <div class="mv-side-back">
      <div class="left flex" @click="$emit('back')">
        <DynamicIcon name="back" />
        {{ singerData.name }}
      </div>
      <div
        class="close"
        @click="$emit('close')"
      >
        <DynamicIcon name="close" />
      </div>
    </div>
    <div class="singer-detail-side-main">
      <div v-if="singerHeaderIsfixed" class="avatar-default-wrapper fixed-header">
        <div :style="{ backgroundImage: `url(${singerData.image})` }"></div>
      </div>
      <div class="list">
        <LoadMore
          class="song-list"
          ref="loadMoreRef"
          v-if="dataList.length"
          @load-more="fetchData"
          :safeAreaHeight="browserType === 'B07' ? '0vw' : '11.6991vw'"
        >
          <div class="header flex-column">
            <div class="avatar-default-wrapper">
              <div :style="{ backgroundImage: `url(${singerData.image})` }"></div>
            </div>
            <p class="ellipsis">{{ singerData.name }}</p>
          </div>
          <SongItem
            v-for="(songItem, index) in dataList"
            :key="index"
            :songItem="songItem"
            :index="index"
            :order-log="{
              str1: '欢唱页',
              str2: '快速点歌-歌手点歌',
            }"
            :logData="logData"
            :singerEnable="false"
            :isStickButton="false"
          />
        </LoadMore>
        <div v-else-if="!isRequest" class="empty">
          暂无歌曲
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, computed, defineProps, nextTick } from 'vue'
import SongItem from '@/components/song-item/index.vue'
import { searchSinger } from '@/service/search'
import {useShareBrowserSize} from '@/composables/sharedComposable';

const props = defineProps({
  singerData: {
    default: () => ({
      singerid: '',
      name: '',
      image: '',
    }),
    type: Object,
  }
})

const logData = computed(() => {
  if (singerData.value.from === 'changchang') {
    return {
      vipLog: {
        str1: 'MV 页面',
        str2: '常唱歌手',
        str3: '点击 VIP 歌曲',
      },
      fr: [1855, 1854],
    }
  }
  return {
    vipLog: {
      str1: 'MV 页面',
      str2: '快速点歌-搜索结果-歌手',
      str3: '点击 VIP 歌曲',
    },
    fr: [1838, 1839],
  }
})

const singerData = ref(props.singerData)

const { browserType } = useShareBrowserSize()
let p = ref(1)
let version = ref({
  current: '',
  latest: ''
})
let isRequest = ref(false)
let dataList = ref([])
let loadMoreRef = ref(null)
let singerHeaderIsfixed = ref(false)
let observer

const requestBussinessData = async () => {
  let responseData = []
  const { singerid } = singerData.value
  if (singerid) {
    const { data } = await searchSinger(singerid, p.value);
    responseData = {
      data: data.song
    }
  }
  return responseData
}

const fetchData = async () => {
  if (isRequest.value) {
    return
  }
  isRequest.value = true

  const res = await requestBussinessData()
  if (res.data.length !== 0) {
    if (p.value === 1 && res.version) {
      version.value = res.version
    }
    dataList.value = dataList.value.concat(res.data)
    p.value++
  }
  isRequest.value = false
}

onMounted(async () => {
  await fetchData()
  await nextTick()
  const headerElement = document.querySelector('.avatar-default-wrapper.header')
  console.log(1225, headerElement)
  if (headerElement) {
    console.log(1225, headerElement)
    observer = new IntersectionObserver((entries) => {
      singerHeaderIsfixed.value = !entries[0].isIntersecting
    }, {
      root: null,
      threshold: 0.1
    })
    observer.observe(headerElement)
  }
})

onBeforeUnmount(() => {
  if (observer) {
    observer.disconnect()
  }
})
</script>

<style lang="stylus" scoped>
.singer-detail-side
  width 100%
  height 100vh
  position relative
  &-title
    color rgba(255, 255, 255, 0.8)
    font-size 40px
    margin 64px 0 48px
    width 500px
    height 48px
    line-height 48px
    font-weight 600
  &-back
    color rgba(255, 255, 255, 0.8)
    font-size 32px
    font-weight 400
    display flex
    align-items center
    margin 63px 0 63px
    width 320px
    height 38px
    line-height 38px
    svg
      width 32px
      height 32px
      margin-right 30px
    p
      flex 1
      text-overflow ellipsis
      white-space nowrap
      overflow hidden
    img
      width 36px
      height 36px
      margin-right 30px
  .close-side
    position absolute
    top 61px
    right 48px
    width 32px
    height 32px
    img
      width 100%
      height 100%
  &-main
    width 100%
    height 88vh
    display flex
    flex-direction column
    align-items center
    overflow-y scroll
    @keyframes fadeIn {
      from {
        opacity 0
      }
      to {
        opacity 1
      }
    }
    .visi-hidden
      visibility hidden
    .fixed-header
      position absolute
      top 46px
      left 50%
      margin-left -30px
      width 60px
      height 60px
      border-radius 50%
      animation fadeIn 1s ease-in-out

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        top 45px !important
        :deep(.song-item)
          background #000
          height calc(78px * 1.48) !important
          .right
            div
              margin-left: calc(12px * var(--scale)) !important;
  
    .empty
      text-align center
      //color rgba(255, 255, 255, 0.4)
      color #1D1D1F80
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        margin-top calc(200px * 1.48) !important
    .list
      width 100%
      margin-right 0!important
      ::-webkit-scrollbar
        display none
      .song-list
        width 100%
        padding 0 !important
        .header
          width auto !important
          height auto !important
          margin 0 auto
          margin-bottom calc(20px * var(--scale))
          padding 0 !important
          border-radius 0
          align-items center
          div
            width 200px
            height 200px
            border-radius 50%
          p
            text-align center
            margin-top calc(12px * var(--scale))
            white-space nowrap
            max-width 90%
        :deep(.song-item)
          height 141px
          @media (max-width: 900px) and (min-width: 701px)   
            height calc(78px * var(--scale)) !important
          margin-top 0
          .right
            margin-right 0
            div
              // 三分之二屏
              @media (max-width: 900px) and (min-width: 701px)   
                width calc(44px * var(--scale)) !important
                height calc(44px * var(--scale)) !important
                margin-left calc(12px * var(--scale)) !important
  </style>