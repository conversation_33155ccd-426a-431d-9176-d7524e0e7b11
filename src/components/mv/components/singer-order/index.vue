<template>
  <div class="singer-order-side">
    <div class="singer-order-side-main">
      <template v-if="oftenSingSingerList.length">
        <SecContainer
          title="常唱歌手"
          class="often-sing-singer"
        >
          <div class="often-sing-singer-list">
            <SingerItem
              className="often-sing-singer-list-item"
              v-for="(item, index) in oftenSingSingerList"
              :singer="item"
              :key="index"
              :singerType="oftenSingSingerList.length"
              @click="handleClickSinger(item, '常唱歌手')"
            />
          </div>
        </SecContainer>
      </template>
      <div
        v-if="singerUnionList.length"
        ref="singerTabRefs"
        class="singer-tabs"
        :class="[
          oftenSingSingerList.length ? 'has-often-sing-singer' : ''
        ]"
      >
        <div
          class="tab"
          v-for="item in tabList"
          :key="item"
          :class="{'active': curTab == item}"
          :id="'mv-' + item"
          @click="handleChangeTab(item)"
        >
          {{ item }}
        </div>
      </div>
      <LoadMore
        class="singer-list"
        ref="loadMoreRef"
        @load-more="fetchSingerList"
        safeAreaHeight="9.6991vw"
      >
        <SingerItem
          className="singer-list-item"
          v-for="(item, index) in singerUnionList"
          :singer="item"
          :key="index"
          @click="handleClickSinger(item, '列表歌手')"
        />
      </LoadMore>
      <p class="hint" v-if="isEmpty && singerUnionList.length > 20">已加载全部</p>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, onBeforeUnmount, ref, watch, nextTick, toRefs } from 'vue'
import { useStore } from 'vuex'
import SecContainer from '@/components/section-container/index.vue'
import SingerItem from '@/components/singer-item/index.vue'
import { getSingsingerList } from '@/service/singing'
import { getSingerClassList, getSingerList } from '@/service/singer'
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'SingerOrder',
  props: {
    currPageTab: String
  },
  components: {
    SecContainer,
    SingerItem,
  },
  setup(props, { emit }) {
    const store = useStore()
    const unionid = computed(() => store.state.userInfo.unionid)
    const isLogin = computed(() => !!unionid.value)

    const { currPageTab } = toRefs(props)

    let loadMoreRef = ref(null)
    let singerTabRefs = ref(null)
    let watcherRef = ref(null)
    let oftenSingSingerList = ref([])
    let tabList = ref([])
    let singerList = ref([])
    let curTab = ref('')
    let p = 1
    let version = {
      current: '',
      latest: ''
    }
    let isEmpty = ref(false)
    let isRequest = false
    let isLoaded = ref(false)

    // let singerHeaderIsfixed = ref(false)

    const isSticky = ref(false);
    let observer = null;

    const handleClickSinger = ({singername, singerheader, singerid}, type) => {
      console.log('handleClickSinger', type)
      sendLog({
        event_type: 'show',
        event_name: 6006,
        event_data: {
          str1: '歌手详情页',
          str2: '歌手详情页',
          str3: '进入歌手详情页',
          str4: 'show',
          str5: singerid,
          str6: type === '常唱歌手' ? 4 : 5,
        },
      });
      emit('singer-click', {
        singer: singername,
        singerhead: singerheader,
        singerid,
        from: type === '常唱歌手' ? 'changchang' : ''
      })
    }

    const initOftenSingSinger = async () => {
      if (unionid.value) {
        try {
          oftenSingSingerList.value = await getSingsingerList({
            unionid: unionid.value
          })
          isLoaded.value = true
        } catch (error) {
          console.error('获取常唱歌手列表失败:', error)
          isLoaded.value = true
        } finally {
          await nextTick()
          observer = new IntersectionObserver(handleIntersection);
          if (watcherRef.value) {
            observer.observe(watcherRef.value);
          }
        }
      } else {
        await nextTick()
        observer = new IntersectionObserver(handleIntersection);
        if (watcherRef.value) {
          observer.observe(watcherRef.value);
        }
      }
    }

    // 歌手列表去重
    const singerUnionList = computed(() => {
      const idsMap = new Map();
      singerList.value && singerList.value.forEach((singer) => {
        if (singer && !idsMap.has(singer.singerid)) {
          idsMap.set(singer.singerid, singer);
        }
      });
      return Array.from(idsMap.values());
    });

    const fetchSingerClassList = async () => {
      tabList.value = await getSingerClassList()
      handleChangeTab(tabList.value[0])
    }

    const fetchSingerList = async (callBack) => {
      if (isRequest) {
        return
      }
      isRequest = true

      let bussinessResponseData = {}
        bussinessResponseData = await getSingerList({
          p,
          k: curTab.value.replace('歌星', '歌手'),
          version: version.latest
        })

      if (bussinessResponseData.data?.length !== 0) {
        if (p === 1 && bussinessResponseData.version) {
          version = bussinessResponseData.version
        }
        singerList.value = p === 1 ? bussinessResponseData.data : singerList.value.concat(bussinessResponseData.data);
        p++
      }
      isRequest = false
      if (callBack) callBack()
    }

    const handleChangeTab = (tab) => {
      if (curTab.value === tab) return
      curTab.value = tab
      const element = document.getElementById('mv-' + tab);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
      }
      console.log('1225', loadMoreRef.value)
      loadMoreRef.value.root.scrollTop = -30
    }

    const handleIntersection = (entries) => {
      if (currPageTab.value !== 'singer') return

      const entry = entries[0]
      if (entry.isIntersecting) {
        isSticky.value = false;
      } else {
        isSticky.value = true;
        console.log('1203')
      }
    };

    onMounted(async () => {
      initOftenSingSinger()
      fetchSingerClassList()
    })

    onBeforeUnmount(() => {
      if (observer) {
        observer.disconnect();
      }
    })

    watch(isLogin, (val) => {
      if (val) initOftenSingSinger()
    })

    watch(curTab, (tab) => {
      if (tab) {
        p = 1
        fetchSingerList()
      }
    })

    return {
      loadMoreRef,
      singerTabRefs,
      oftenSingSingerList,
      singerUnionList,
      tabList,
      curTab,
      isEmpty,
      fetchSingerList,
      handleClickSinger,
      handleChangeTab,
      isSticky,
      watcherRef,
      isLoaded,
    }
  }
}
</script>

<style lang="stylus" scoped>
.singer-order-side
  width 100%
  height calc(100% - 164px)
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)   
    width calc(400px * var(--scale)) !important
  &-main
    width 100%
    height 100%
    padding-left 0px
    padding-right 0px
    overflow-y scroll
    // display flex
    // flex-direction column
    .singer-tabs
      width 100%
      height auto !important
      margin-bottom 0px
      display flex
      overflow-x scroll
      grid-column span 4
      position sticky
      background var(--background)
      top 0px
      padding 48px 48px 0
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        padding calc(40px * 1.48) calc(20px * 1.48) 0 !important
      &.has-often-sing-singer
        top -30px
      .tab
        width fit-content
        height 100%
        min-width 160px
        padding 21.5px 24.5px
        border-radius 20px
        font-size 28px
        color var(--text-secondary-color)
        float left
        margin 0 20px 0 0
        background var(--tab-background)
        flex-shrink 0
        text-align center
        border none
        white-space nowrap
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)   
          height calc(50px * 1.48) !important
          line-height calc(33px * 1.48) !important
          font-size calc(18px * 1.48) !important
          padding calc(9px * 1.48) calc(20px * 1.48) !important
          border-radius calc(12px * 1.48) !important
      .active
        background var(--highlight-color)
        color var(--popup-background-color)
        opacity 1
    .singer-list
      text-align center
      display flex
      flex-wrap wrap
      box-sizing border-box
      display grid
      grid-template-columns repeat(3, 1fr);
      padding 36px 0 0 48px
      justify-content: space-between;
      height calc(100vh - 164px) !important
      row-gap: calc(40px * var(--scale))  /* 只设置垂直间距 */
      :deep(.common-singer-item)
        width calc(144px * var(--scale))

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(108px * var(--scale)) !important
          column-gap: calc(48px * var(--scale)) !important
    .no-data
      font-size 28px
      color rgba(255, 255, 255, 0.5)
      text-align center
      width 100%
      height 32px
      clear both
    .hint
      text-align center
      color #555555
    .often-sing-singer
      width 100%
      padding 0px !important
      margin 48px 0 0px
      :deep(.section-container-list)
        height auto !important
      :deep(.section-container-header)
        padding-bottom 30px
        padding-left 48px
      &-list
        width 100%
        height auto
        display flex
        width fit-content
        overflow-y hidden
        overflow-x scroll
        margin-top 0px
        padding-left 48px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          margin-top 20px !important
        :deep(.common-singer-item)
          margin-right calc(30px * var(--scale))
          width calc(88px * var(--scale)) !important
    :deep(.empty)
      display flex
      flex-direction column
      justify-content center
      align-items center
      margin-top 30px
      font-size 28px
      color rgba(255, 255, 255, 0.40)
      text-align center
      img
        width 80px
        height 80px
        margin-bottom 40px
      p
        height 32px
        line-height 32px
</style>
