<template>
  <div class="mini" id="mvMini">
    <div class="controls flex-between">
      <div class="left flex">
        <button @click="handleVideoNext" class="margin flex-center">
          <DynamicIcon name="mini-next" />
        </button>
        <button v-show="paused" class="flex-center" @click="handleVideoPlay">
          <DynamicIcon name="mini-play" />
        </button>
        <button v-show="!paused" class="flex-center" @click="handleVideoPause">
          <DynamicIcon name="mini-pause" />
        </button>
      </div>
      <button class="flex-center" @click="handleOpenMv">
        <DynamicIcon name="mini-fullscreen" />
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import eventBus from '@/utils/event-bus'
import { debounce } from 'lodash'
import { sendLog } from '@/directives/v-log/log'

const store = useStore()
const paused = computed(() => store.state.videoPaused)

const handleVideoNext = debounce(() => {
  eventBus.emit('handle-video-next', 'bottom-bar')
}, 1000, { leading: true, trailing: false })

const handleOpenMv = () => {
  store.commit('UPDATE_MV_ISHIDE', false)
}

const handleVideoPlay = async () => {
  eventBus.emit('video-control-resume')
  sendLog({
    event_type: '10000~50000',
    event_name: 10096,
    event_data: {
      str1: '任意页',
      str2: '歌词条',
      str3: '播放',
      str4: 'click',
    },
  })
}

const handleVideoPause = () => {
  eventBus.emit('handle-video-pause')
  sendLog({
    event_type: '10000~50000',
    event_name: 10096,
    event_data: {
      str1: '任意页',
      str2: '歌词条',
      str3: '暂停',
      str4: 'click',
    },
  })
}
</script>

<style lang="stylus" scoped>
.mini
  width 715px
  height 420px
  border: 1px solid
  background transparent
  border-radius 28px
  clip-path: url(#roundedClip)
  position fixed
  z-index 10000
  right 80px
  top 150px
  .controls
    padding 24px
  button
    width 88px
    height 64px
    border-radius 20px
    background rgba(31, 31, 32, 0.2)
    backdrop-filter: blur(30px)
    &.margin
      margin-right 12px
    .svg-icon
      width 36px
      height 36px
</style>