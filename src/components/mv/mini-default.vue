<template>
  <div
    class="mv-mini-default"
    ref="mvMiniRef"
  >
    <div class="default">
      <!-- <div class="default-rudio" v-if="!TSBaseInfoInstance.requestAudioFocus()" @click.stop>
        <img src="https://qncweb.ktvsky.com/20250112/vadd/925aeb37b97100caf765cf2870ffa372.png" alt="" srcset="">
      </div> -->
      <div
        class="default-drawer"
        :class="{ show: showDrawer }"
        @click.stop="toggleDrawer"
      >
        <div @click.stop="toggleDrawer" class="icon">
          <svg width="45" height="16" viewBox="0 0 45 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_b_1476_51399)">
            <rect width="45" height="16" rx="8" fill="white" fill-opacity="0.2"/>
            <rect x="0.223333" y="0.223333" width="44.5533" height="15.5533" rx="7.77667" stroke="url(#paint0_linear_1476_51399)" stroke-opacity="0.1" stroke-width="0.446667"/>
            <mask id="mask0_1476_51399" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="16" y="1" width="14" height="14">
            <rect x="16" y="1.33301" width="13.3333" height="13.3333" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_1476_51399)">
            <path opacity="0.7" d="M17.7227 6.99902L22.1595 9.55348C22.3311 9.65227 22.5423 9.65227 22.7139 9.55348L27.1507 6.99902" stroke="white" stroke-width="1.33333" stroke-linecap="round"/>
            </g>
            </g>
            <defs>
            <filter id="filter0_b_1476_51399" x="-30" y="-30" width="105" height="76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="15"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1476_51399"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1476_51399" result="shape"/>
            </filter>
            <linearGradient id="paint0_linear_1476_51399" x1="22.5" y1="0" x2="22.5" y2="16" gradientUnits="userSpaceOnUse">
            <stop stop-color="white"/>
            <stop offset="1" stop-color="#999999" stop-opacity="0"/>
            </linearGradient>
            </defs>
          </svg>
        </div>
        <div
          class="default-drawer-content"
        >
          <Carousel
            :itemsToShow="itemsToShow"
            :wrapAround="true"
            :transition="300"
            @slide-end="onSlideEnd"
            ref="myCarousel"
            @click.stop
            :breakpoints="carouselBreakpoints"
          >
            <Slide v-for="(slide, index) in defaultList" :key="slide">
              <div class="carousel__item" @click="handleItemClick(index)">
                <img
                  :src="slide.cover"
                  class="cover"
                />
                <img v-if="slide.song.is_vip" class="song-block-vip" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" alt="">
                <img v-else class="free-icon" src="https://qncweb.ktvsky.com/20241231/other/b66b6a18dd20b2425d2643dff4f3db5e.png" alt="">
              </div>
            </Slide>
          </Carousel>
        </div>


        <div v-if="!showDrawer" class="default-drawer-paination">
          <span
            v-for="(i, index) in defaultList"
            :key="i"
            :class="{
              active: index === activeIndex
            }"
          ></span>
        </div>
      </div>

      <div
        v-if="showMvMiniGuide && !showDrawer"
        class="default-guide"
        @click="handleClickGuide"
      >
        <img src="https://qncweb.ktvsky.com/20240710/other/c2e4820b1a75524f58aa37af3b4abbaa.png" alt="">
      </div>
      <video-player
        v-if="orderedListNum == 0 && defaultSong.video_url"
        from="indexDefault"
        ref="videoPlayerRef"
        :autoplay="true"
        :startPosition="0"
        :src="defaultSong.video_url"
        :token="''"
        :isSizeInherit="roundToNearestMinutes"
        :poster="require('./poster.png')"
        @ended="playNext"
        @timeupdate="onTimeupdate"
      />
    </div>
  </div>

  
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, defineExpose } from 'vue'
import { useStore } from 'vuex';
import store2 from 'store2'
import 'vue3-carousel/dist/carousel.css'
import { Carousel, Slide } from 'vue3-carousel'
import VideoPlayer from '@/components/video-player/mp4.vue';
import roundToNearestMinutes from 'date-fns/roundToNearestMinutes/index.js';
import { sendLog } from '@/directives/v-log/log'
import useTimeUpdate from '@/composables/useTimeUpdate'

const store = useStore();
const { handleTimeupdate }  = useTimeUpdate()

const showMvMiniGuide = ref(!store2.get('showMvMiniGuide'))
const showDrawer = ref(true)
const myCarousel = ref(null)
const activeIndex = ref(0)
const videoPlayerRef = ref(null);

const defaultSong = computed(() => store.state.miniMv.defaultSong);
const defaultSongIndex = computed(() => store.state.miniMv.defaultSongIndex);
const defaultList = computed(() => store.state.miniMv.defaultList);
const currentSize = computed(() => store.state.currentSize);
const orderedList = computed(() => store.state.orderedList)
const orderedListNum = computed(() => orderedList.value.length)
const mvMiniRef = ref(null)
const itemsToShow = ref(4.2)
const carouselBreakpoints = {
  900: {
    itemsToShow: 4.2
  }
}

let resizeObserver = null

const handleResize = (entries) => {
  for (let entry of entries) {
    const { width } = entry.contentRect
    if (width <= 900) {
      itemsToShow.value = 3.8
    } else {
      itemsToShow.value = 4.2
    }
    if (myCarousel.value && myCarousel.value.updateSlideWidth) {
      myCarousel.value.updateSlideWidth()
    }
  }
}

const onTimeupdate = (payload) => {
  // console.log('onTimeupdate', payload)
}

onMounted(() => {
  store.commit('miniMv/SET_DEFAULT_SONG', {
    song: defaultList.value[0],
    index: 0,
  });
  store.commit('UPDATE_SONG_LRC_EMPTY', false);
  if (typeof ResizeObserver !== 'undefined') {
    resizeObserver = new ResizeObserver(handleResize)
    if (mvMiniRef.value) {
      resizeObserver.observe(mvMiniRef.value)
    }
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 监听 defaultSongIndex 的变化
watch(defaultSongIndex, (newIndex) => {
  if (myCarousel.value && newIndex !== undefined) {
    myCarousel.value.slideTo(newIndex); // 更新轮播图位置
    activeIndex.value = newIndex; // 更新 activeIndex
  }
});

const handleClickGuide = () => {
  store2.set('showMvMiniGuide', true)
  showMvMiniGuide.value = false
}

const toggleDrawer = () => {
  console.log('toggleDrawer', showDrawer.value)
  sendLog({
    event_type: '10000~50000',
    event_name: 30243,
    event_data: {
      str1: '首页',
      str2: `${showDrawer.value ? '下拉-隐藏' : '展开-显示'}定制歌单`,
      str3: '点击',
      str4: 'click',
    },
  })

  showDrawer.value = !showDrawer.value
}

const handleItemClick = (index) => {
  const totalSlides = defaultList.value.length;
  const currentSlide = myCarousel.value.data.currentSlide;

  if (index === currentSlide) return;

  if(index === 0 && currentSlide === totalSlides - 1) {
    myCarousel.value.next()
  } else if(index === totalSlides - 1 && currentSlide === 0) {
    myCarousel.value.prev()
  } else {
    myCarousel.value.slideTo(index);
  }

  activeIndex.value = index;
  store.commit('miniMv/SET_DEFAULT_SONG', {
    song: defaultList.value[activeIndex.value],
    index: activeIndex.value,
  });
};

const playNext = async () => {
  activeIndex.value = (activeIndex.value + 1) % defaultList.value.length;
  handleItemClick(activeIndex.value)
};

const playPrev = async () => {
  activeIndex.value = (activeIndex.value - 1 + defaultList.value.length) % defaultList.value.length;
  handleItemClick(activeIndex.value)
};

const onSlideEnd = ({currentSlideIndex}) => {
  console.log(currentSlideIndex)
  handleItemClick(currentSlideIndex)
}

const handleSwipe = (payload) => {
  console.log(payload)
  if(payload === 'left') {
    playNext()
  } else {
    playPrev()
  }
}

defineExpose({
  handleSwipe
})
</script>

<style lang="stylus" scoped>
.mv-mini-default
  width 100%
  height 100%
  border-radius 24px
  margin-bottom 0px
  & > div
    width 100%
    height 100%
    position absolute
    top 0
    left 0

  .video-player
    width 100%
    height 100%

    #video-player, video
      width 100% !important
      height 100% !important

  .default
    width 100%
    height 100%
    overflow hidden
    position relative
    
    &-rudio
      position absolute
      top 0
      left 0
      right 0
      bottom 0
      background rgba(0, 0, 0, 0.5) // 半透明黑色背景
      z-index 999
      display flex
      justify-content center
      align-items center
    button
      width 177px
      height 63px
      background: #1F1F2033;
      backdrop-filter: blur(120px);
      color #FFFFFF
      position absolute
      right 24px
      top 24px
      display flex
      align-items center
      justify-content center
      border-radius 18px
      overflow hidden
      z-index 10
      font-size var(--font-size-medium) !important

      svg
        width 36px
        height 36px
        margin-right 3px

    &-guide
      position absolute
      z-index 10
      left 0
      top 0
      width 100%
      height 100%
      background rgba(0, 0, 0, 0.8)
      display flex
      align-items center
      justify-content center
      z-index 9999
      img
        width 388px
        height auto

    &-drawer
      position absolute
      z-index 20
      bottom 0
      left 0
      width 100%
      height 153px
      background-color rgba(27, 27, 27, 0.66)
      transition transform 0.3s ease-in-out
      transform translateY(120px)

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        height calc(104px * var(--scale)) !important
        transform translateY(120px) !important
      .icon
        position absolute
        width 67.5px
        height 24px
        left 50%
        backdrop-filter blur(5px)
        -webkit-backdrop-filter blur(5px)
        transform translateX(-50%) rotate(180deg)
        top 6px

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          top calc(5px * var(--scale)) !important
          width calc(45px * var(--scale)) !important
          height calc(16px * var(--scale)) !important

      &-paination
        display flex
        justify-content center
        align-items center
        margin-top 14px
        height auto
        position absolute
        width 100%
        top 0
        left 0
        margin-left -10px

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          margin-top calc(10px * var(--scale)) !important
        span
          width 9px
          height 9px
          border-radius 50%
          background #898989
          margin 0 5px
          opacity 0.5
          &:nth-child(7) {
            margin-right: calc(90px * var(--scale));
          }
          &.active
            width 9px
            opacity 1
            border-radius 10px

      &.show
        transform translateY(0) !important
        border-top-left-radius 0px
        border-top-right-radius 0px
        .icon
          transform translateX(-50%) rotate(0deg)

        .carousel
          position relative
          margin-top 25px

      &-content
        margin-top 36px

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          margin-top calc(26px * var(--scale)) !important
        :deep(.carousel)
          height 102px
          margin-top 0px !important
          
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            height calc(67px * var(--scale)) !important

          &__viewport
            height 100%

          &__track
            height 100%
            display flex
            align-items center

          .cover 
            width 100% !important
            height auto !important

          &__item
            width 160px
            height 100%
            border-radius 20px
            overflow hidden
            // transform scale(calc(160 / 180)) translateX(20px)
            position relative
            margin 0 9px
            padding 0px

            // 三分之二屏
            @media (max-width: 900px) and (min-width: 701px)  
              width calc(106px * var(--scale)) !important
              margin 0px !important

            p
              width 100%
              height 40px
              line-height 40px
              background #00000033
              font-size 20px
              color #FFFFFF
              text-align center
              position absolute
              bottom 0
              left 0

            .free-icon
              height 24px
              // display flex
              // align-items center
              // justify-content center
              // background: linear-gradient(90deg, #4ADBEF -0.89%, #1CA8C7 99.11%);
              // color #fff
              // font-size 16px
              // border-radius 12px
              position absolute
              right 5px
              top 5px
              // 三分之二屏
              @media (max-width: 900px) and (min-width: 701px)  
                width calc(43px * 1.48) !important
                height calc(16px * 1.48) !important
                right calc(4px * 1.48) !important
                top calc(4px * 1.48) !important
              img
                height 100%
                width auto
            .song-block-vip
              width 48px
              height 24px
              position absolute
              right 5px
              top 5px
              // 三分之二屏
              @media (max-width: 900px) and (min-width: 701px)  
                width calc(28px * 1.48) !important
                height calc(16px * 1.48) !important
                right calc(4px * 1.48) !important
                top calc(4px * 1.48) !important
          &__slide--next ~ .carousel__slide
            .carousel__item
              // transform scale(calc(160 / 180)) translateX(-30px)

          &__slide--prev, &__slide--next
            .carousel__item
              // transform scale(calc(160 / 180))

          &__slide--active
            transform scale(calc(180 / 160)) translateX(-9px)
            transform-origin center
            margin 0 4px 0 18px

            // 三分之二屏
            @media (max-width: 900px) and (min-width: 701px)  
              width scale(calc(120 / 106)) translateX(-9px) !important
</style>
