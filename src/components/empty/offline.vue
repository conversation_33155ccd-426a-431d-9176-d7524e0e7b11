<template>
  <div class="offline">
    <div>
      <svg v-show="themeClass === 'themeDark'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.4">
        <rect x="11" y="11" width="68" height="68" rx="34" stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="4"/>
        <circle cx="44.9994" cy="60" r="4" fill="white" style="fill:white;fill-opacity:1;"/>
        <path d="M41.0525 31.4226C40.7849 29.0652 42.629 27 45.0015 27C47.374 27 49.2181 29.0652 48.9506 31.4226L47.0502 48.1668C46.932 49.2088 46.0503 49.996 45.0016 49.996C43.9529 49.996 43.0713 49.2088 42.953 48.1668L41.0525 31.4226Z" fill="white" style="fill:white;fill-opacity:1;"/>
        </g>
      </svg>
      <svg v-show="themeClass === 'themeLight'" width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.4">
        <rect x="11" y="11" width="68" height="68" rx="34" stroke="black" style="stroke:black;stroke-opacity:1;" stroke-width="4"/>
        <circle cx="44.9995" cy="60" r="4" fill="black" style="fill:black;fill-opacity:1;"/>
        <path d="M41.0525 31.4226C40.7849 29.0652 42.629 27 45.0016 27C47.3741 27 49.2182 29.0652 48.9506 31.4226L47.0503 48.1668C46.932 49.2088 46.0504 49.996 45.0017 49.996C43.953 49.996 43.0713 49.2088 42.9531 48.1668L41.0525 31.4226Z" fill="black" style="fill:black;fill-opacity:1;"/>
        </g>
      </svg>
    </div>
    <p>{{ tip }}</p>
    <button @click="handleClickRetry">重试</button>
  </div>
</template>

<script setup>
import { computed, defineEmits, defineProps } from 'vue';
import { useStore } from 'vuex';

const store = useStore();

const emit = defineEmits(['click-retry'])

defineProps({
  tip: {
    type: String,
    default: '网络异常，请检查网络后点击按钮重新尝试',
  }
})

const themeClass = computed(() => store.state.themeClass)

const handleClickRetry = () => {
  emit('click-retry')
}
</script>


<style lang="stylus" scoped>
.offline
  text-align center
  height 60vh
  min-height 300px
  display flex
  flex-direction column
  align-items center
  color #FFFFFF66
  font-size 28px
  padding-top 2vh
  p
    width  auto
    margin-top 24px
  button
    width 300px
    height 80px
    background #FFFFFF1A
    border-radius 4px
    margin-top 30px

.theme-themeLight
  color #00000066
  p
    color #00000066
  button
    background #0000001A
    color #000000CC
</style>
