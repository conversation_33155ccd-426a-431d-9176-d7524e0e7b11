<template>
  <div
    v-if="currentSize === 'small'"
    class="user-vip-small"
    :class="{
      'vip': isVip
    }"
  >
    <div class="user-vip-card flex-column" @click="handleClickSmallTop">
      <div class="top">
        <div class="user-vip-info">
          <div class="avatar-default-wrapper">
            <div class="avatar" :style="{ backgroundImage: `url(${userInfo.avatar || avatar})` }"></div>
          </div>
          <div class="user-vip-info-user">
            <div class="username ellipsis">
              {{ userInfo.username }}
            </div>
            <div v-if="isLogin" class="desc">{{ vipDesc }}</div>
          </div>
        </div>
        <DynamicIcon name="right-icon" />
      </div>
      <div class="bottom flex-center">
        <svg v-show="themeClass === 'themeLight'" width="304" height="19" viewBox="0 0 304 19" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M1.74359 0.5C0.780632 0.5 0 1.28063 0 2.24359V16.7564C0 17.7194 0.780631 18.5 1.74359 18.5H19.2564C20.2194 18.5 21 17.7194 21 16.7564V2.24359C21 1.28063 20.2194 0.5 19.2564 0.5H1.74359ZM13.6589 4.62482C13.7793 4.62482 13.8769 4.72239 13.8769 4.84275V6.65688C13.8769 6.77724 13.7793 6.87482 13.6589 6.87482H12.5948C12.4744 6.87482 12.3769 6.97239 12.3769 7.09275V12.1246C12.3769 12.125 12.3765 12.1254 12.3761 12.1254C12.3757 12.1254 12.3754 12.1257 12.3754 12.1261C12.3749 13.5755 11.1998 14.7503 9.75039 14.7503C8.30064 14.7503 7.12539 13.575 7.12539 12.1251C7.12539 10.6753 8.30064 9.5 9.75039 9.5C9.7927 9.5 9.83478 9.501 9.87661 9.50298C10.0091 9.50926 10.1269 9.40801 10.1269 9.27532V6.87482V6.125V4.84275C10.1269 4.72239 10.2244 4.62482 10.3448 4.62482H13.6589Z" fill="url(#paint0_linear_1832_101731)"/>
          <path d="M40.8826 9.778V10.73H39.4406C39.4126 11.528 39.3846 12.298 39.3566 12.9H40.6166V13.866H39.3286C39.3146 13.992 39.3146 14.076 39.3146 14.118C39.2446 15.392 38.7686 15.756 37.3826 15.756H35.2966L35.0866 14.776H37.0466C37.9706 14.776 38.2786 14.566 38.3206 13.894V13.866H32.0346L32.3846 10.73H31.2926V9.778H32.4826L32.7906 6.992C32.5806 7.258 32.3706 7.524 32.1466 7.776L31.4326 7.02C32.4686 5.872 33.4486 4.304 34.0226 2.96L34.9606 3.282C34.8066 3.646 34.6246 4.024 34.4146 4.416H40.5466V5.368H33.8826C33.6306 5.788 33.3646 6.208 33.0706 6.6H39.5806C39.5806 6.768 39.5246 8.21 39.4686 9.778H40.8826ZM31.7546 5.2L31.1806 6.026C30.5506 5.508 29.5706 4.738 28.9266 4.248L29.5286 3.45C30.2426 3.982 31.0966 4.668 31.7546 5.2ZM31.3626 8.546L30.7606 9.428C30.1026 8.854 29.1366 8 28.4926 7.524L29.0946 6.712C29.7246 7.216 30.7186 8.028 31.3626 8.546ZM38.6006 7.552H33.7286L33.4766 9.778H38.5026C38.5586 8.686 38.6006 7.748 38.6006 7.552ZM34.9046 8.294L35.4366 7.58C35.9546 7.958 36.7806 8.574 37.2286 8.952L36.6686 9.708C36.1926 9.302 35.4366 8.7 34.9046 8.294ZM33.1126 12.9H38.3626C38.3906 12.298 38.4326 11.514 38.4606 10.73H33.3646L33.1126 12.9ZM37.0466 12.144L36.4586 12.872C36.0246 12.494 35.1146 11.78 34.6526 11.444L35.1846 10.744C35.6326 11.066 36.5846 11.78 37.0466 12.144ZM29.6966 15.882L28.6886 15.574C28.8846 14.902 29.8506 11.444 30.0466 10.758L30.9986 11.052C30.7886 11.738 29.8786 15.196 29.6966 15.882ZM43.9066 3.632H53.3986V6.922H43.9066V3.632ZM52.3766 4.948V4.318H44.9566V4.948H52.3766ZM44.9566 5.592V6.25H52.3766V5.592H44.9566ZM42.4366 8.392V7.65H54.8826V8.392H42.4366ZM53.8186 14.104H49.1706V14.846H54.7426V15.588H42.5766V14.846H48.1626V14.104H43.5006V13.376H48.1626V12.62H43.9066V9.148H53.4126V12.62H49.1706V13.376H53.8186V14.104ZM48.1626 10.534V9.82H44.9426V10.534H48.1626ZM49.1706 10.534H52.3766V9.82H49.1706V10.534ZM44.9426 11.206V11.92H48.1626V11.206H44.9426ZM49.1706 11.206V11.92H52.3766V11.206H49.1706ZM64.7946 3.296V5.354H67.9866V15.966H66.9646V15.154H58.3546V15.966H57.3466V5.354H60.5246V3.296H61.5606V5.354H63.7586V3.296H64.7946ZM60.5246 9.68V6.39H58.3546V9.68H60.5246ZM63.7586 9.68V6.39H61.5606V9.68H63.7586ZM66.9646 9.68V6.39H64.7946V9.68H66.9646ZM60.5246 10.688H58.3546V14.132H60.5246V10.688ZM63.7586 14.132V10.688H61.5606V14.132H63.7586ZM64.7946 10.688V14.132H66.9646V10.688H64.7946ZM78.1086 4.556H82.8406V5.494H72.8306V8.868C72.8306 12.424 72.5506 13.754 71.4306 15.98L70.4366 15.462C71.5706 13.236 71.8226 11.99 71.8226 8.966V4.556H76.8906C76.7506 4.108 76.5966 3.646 76.4706 3.268L77.6046 3.072C77.7446 3.478 77.9546 4.066 78.1086 4.556ZM78.8086 12.858H82.8686V13.838H78.8086V15.966H77.7866V13.838H73.1246V12.858H77.7866V11.346H74.1326V10.394C74.5526 9.75 75.0286 8.952 75.4766 8.154H73.4186V7.202H75.9806C76.2466 6.698 76.4706 6.222 76.6526 5.802L77.6886 5.956C77.5206 6.348 77.3106 6.768 77.1006 7.202H82.3506V8.154H76.5826C76.1486 8.966 75.6726 9.764 75.2666 10.394H77.7866V8.714H78.8086V10.394H82.0566V11.346H78.8086V12.858Z" fill="#614208"/>
          <rect opacity="0.1" x="95.6666" y="1.5" width="1" height="16" fill="#C18B24"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M110.41 0.5C109.447 0.5 108.667 1.28063 108.667 2.24359V16.7564C108.667 17.7194 109.447 18.5 110.41 18.5H130.401C131.364 18.5 132.145 17.7194 132.145 16.7564V2.24359C132.145 1.28063 131.364 0.5 130.401 0.5H110.41ZM117.764 7.19733C117.742 7.21969 117.731 7.2495 117.731 7.28677V10.1712C117.731 10.2085 117.713 10.2271 117.676 10.2271H115.627C115.59 10.2271 115.572 10.2085 115.572 10.1712V7.28677C115.572 7.2495 115.557 7.21969 115.528 7.19733C115.506 7.16751 115.477 7.15261 115.44 7.15261H113.884C113.848 7.15261 113.815 7.16751 113.786 7.19733C113.764 7.21969 113.753 7.2495 113.753 7.28677V14.8445C113.753 14.8818 113.764 14.9153 113.786 14.9452C113.815 14.9675 113.848 14.9787 113.884 14.9787H115.44C115.477 14.9787 115.506 14.9675 115.528 14.9452C115.557 14.9153 115.572 14.8818 115.572 14.8445V11.8706C115.572 11.8334 115.59 11.8147 115.627 11.8147H117.676C117.713 11.8147 117.731 11.8334 117.731 11.8706V14.8445C117.731 14.8818 117.742 14.9153 117.764 14.9452C117.793 14.9675 117.826 14.9787 117.862 14.9787H119.418C119.455 14.9787 119.484 14.9675 119.506 14.9452C119.535 14.9153 119.55 14.8818 119.55 14.8445V7.28677C119.55 7.2495 119.535 7.21969 119.506 7.19733C119.484 7.16751 119.455 7.15261 119.418 7.15261H117.862C117.826 7.15261 117.793 7.16751 117.764 7.19733ZM121.271 14.9452C121.3 14.9675 121.333 14.9787 121.37 14.9787H124.164C124.734 14.9787 125.238 14.8781 125.676 14.6768C126.115 14.4756 126.454 14.1886 126.695 13.816C126.937 13.4433 127.057 13.0147 127.057 12.5302V9.60105C127.057 9.11658 126.937 8.68801 126.695 8.31534C126.454 7.94267 126.115 7.65571 125.676 7.45447C125.238 7.25323 124.734 7.15261 124.164 7.15261H121.37C121.333 7.15261 121.3 7.16751 121.271 7.19733C121.249 7.21969 121.238 7.2495 121.238 7.28677V14.8445C121.238 14.8818 121.249 14.9153 121.271 14.9452ZM123.112 13.3911C123.075 13.3911 123.057 13.3725 123.057 13.3352V8.79609C123.057 8.75882 123.075 8.74018 123.112 8.74018H124.23C124.529 8.74018 124.77 8.84826 124.953 9.06441C125.143 9.2731 125.238 9.55633 125.238 9.9141V12.2172C125.231 12.575 125.136 12.8619 124.953 13.0781C124.77 13.2868 124.533 13.3911 124.241 13.3911H123.112ZM110.622 2.89309C110.622 2.65235 110.817 2.45719 111.058 2.45719H113.317C113.557 2.45719 113.753 2.65235 113.753 2.89309V3.58651C113.753 3.82725 113.557 4.02241 113.317 4.02241H111.058C110.817 4.02241 110.622 3.82725 110.622 3.58651V2.89309ZM116.537 2.45719C116.297 2.45719 116.101 2.65235 116.101 2.89309V3.58651C116.101 3.82725 116.297 4.02241 116.537 4.02241H118.796C119.037 4.02241 119.232 3.82725 119.232 3.58651V2.89309C119.232 2.65235 119.037 2.45719 118.796 2.45719H116.537ZM121.579 2.89309C121.579 2.65235 121.774 2.45719 122.015 2.45719H124.274C124.514 2.45719 124.709 2.65235 124.709 2.89309V3.58651C124.709 3.82725 124.514 4.02241 124.274 4.02241H122.015C121.774 4.02241 121.579 3.82725 121.579 3.58651V2.89309ZM127.494 2.45719C127.253 2.45719 127.058 2.65235 127.058 2.89309V3.58651C127.058 3.82725 127.253 4.02241 127.494 4.02241H129.753C129.994 4.02241 130.189 3.82725 130.189 3.58651V2.89309C130.189 2.65235 129.994 2.45719 129.753 2.45719H127.494Z" fill="#DAB487"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M110.41 0.5C109.447 0.5 108.667 1.28063 108.667 2.24359V16.7564C108.667 17.7194 109.447 18.5 110.41 18.5H130.401C131.364 18.5 132.145 17.7194 132.145 16.7564V2.24359C132.145 1.28063 131.364 0.5 130.401 0.5H110.41ZM117.764 7.19733C117.742 7.21969 117.731 7.2495 117.731 7.28677V10.1712C117.731 10.2085 117.713 10.2271 117.676 10.2271H115.627C115.59 10.2271 115.572 10.2085 115.572 10.1712V7.28677C115.572 7.2495 115.557 7.21969 115.528 7.19733C115.506 7.16751 115.477 7.15261 115.44 7.15261H113.884C113.848 7.15261 113.815 7.16751 113.786 7.19733C113.764 7.21969 113.753 7.2495 113.753 7.28677V14.8445C113.753 14.8818 113.764 14.9153 113.786 14.9452C113.815 14.9675 113.848 14.9787 113.884 14.9787H115.44C115.477 14.9787 115.506 14.9675 115.528 14.9452C115.557 14.9153 115.572 14.8818 115.572 14.8445V11.8706C115.572 11.8334 115.59 11.8147 115.627 11.8147H117.676C117.713 11.8147 117.731 11.8334 117.731 11.8706V14.8445C117.731 14.8818 117.742 14.9153 117.764 14.9452C117.793 14.9675 117.826 14.9787 117.862 14.9787H119.418C119.455 14.9787 119.484 14.9675 119.506 14.9452C119.535 14.9153 119.55 14.8818 119.55 14.8445V7.28677C119.55 7.2495 119.535 7.21969 119.506 7.19733C119.484 7.16751 119.455 7.15261 119.418 7.15261H117.862C117.826 7.15261 117.793 7.16751 117.764 7.19733ZM121.271 14.9452C121.3 14.9675 121.333 14.9787 121.37 14.9787H124.164C124.734 14.9787 125.238 14.8781 125.676 14.6768C126.115 14.4756 126.454 14.1886 126.695 13.816C126.937 13.4433 127.057 13.0147 127.057 12.5302V9.60105C127.057 9.11658 126.937 8.68801 126.695 8.31534C126.454 7.94267 126.115 7.65571 125.676 7.45447C125.238 7.25323 124.734 7.15261 124.164 7.15261H121.37C121.333 7.15261 121.3 7.16751 121.271 7.19733C121.249 7.21969 121.238 7.2495 121.238 7.28677V14.8445C121.238 14.8818 121.249 14.9153 121.271 14.9452ZM123.112 13.3911C123.075 13.3911 123.057 13.3725 123.057 13.3352V8.79609C123.057 8.75882 123.075 8.74018 123.112 8.74018H124.23C124.529 8.74018 124.77 8.84826 124.953 9.06441C125.143 9.2731 125.238 9.55633 125.238 9.9141V12.2172C125.231 12.575 125.136 12.8619 124.953 13.0781C124.77 13.2868 124.533 13.3911 124.241 13.3911H123.112ZM110.622 2.89309C110.622 2.65235 110.817 2.45719 111.058 2.45719H113.317C113.557 2.45719 113.753 2.65235 113.753 2.89309V3.58651C113.753 3.82725 113.557 4.02241 113.317 4.02241H111.058C110.817 4.02241 110.622 3.82725 110.622 3.58651V2.89309ZM116.537 2.45719C116.297 2.45719 116.101 2.65235 116.101 2.89309V3.58651C116.101 3.82725 116.297 4.02241 116.537 4.02241H118.796C119.037 4.02241 119.232 3.82725 119.232 3.58651V2.89309C119.232 2.65235 119.037 2.45719 118.796 2.45719H116.537ZM121.579 2.89309C121.579 2.65235 121.774 2.45719 122.015 2.45719H124.274C124.514 2.45719 124.709 2.65235 124.709 2.89309V3.58651C124.709 3.82725 124.514 4.02241 124.274 4.02241H122.015C121.774 4.02241 121.579 3.82725 121.579 3.58651V2.89309ZM127.494 2.45719C127.253 2.45719 127.058 2.65235 127.058 2.89309V3.58651C127.058 3.82725 127.253 4.02241 127.494 4.02241H129.753C129.994 4.02241 130.189 3.82725 130.189 3.58651V2.89309C130.189 2.65235 129.994 2.45719 129.753 2.45719H127.494Z" fill="url(#paint1_linear_1832_101731)"/>
          <path d="M142.634 15H141.318V5.886L139.26 6.964V5.578L141.654 4.36H142.634V15ZM148.76 15.196C146.478 15.196 145.148 13.222 145.148 9.68C145.148 6.138 146.478 4.164 148.76 4.164C151.042 4.164 152.372 6.138 152.372 9.68C152.372 13.222 151.042 15.196 148.76 15.196ZM148.76 13.992C150.174 13.992 151.028 12.452 151.028 9.68C151.028 6.908 150.174 5.368 148.76 5.368C147.346 5.368 146.492 6.908 146.492 9.68C146.492 12.452 147.346 13.992 148.76 13.992ZM157.687 15.196C155.629 15.196 154.103 13.922 154.103 12.116C154.103 10.884 154.999 9.722 156.077 9.33V9.288C155.237 8.98 154.495 8.014 154.495 6.992C154.495 5.326 155.853 4.164 157.687 4.164C159.521 4.164 160.879 5.326 160.879 6.992C160.879 8.056 160.137 9.022 159.283 9.288V9.33C160.375 9.75 161.271 10.898 161.271 12.116C161.271 13.922 159.745 15.196 157.687 15.196ZM157.687 8.742C158.751 8.742 159.563 8.042 159.563 7.076C159.563 6.054 158.751 5.368 157.687 5.368C156.623 5.368 155.811 6.054 155.811 7.076C155.811 8.042 156.623 8.742 157.687 8.742ZM157.687 13.992C158.961 13.992 159.927 13.166 159.927 11.976C159.927 10.786 158.961 9.946 157.687 9.946C156.413 9.946 155.447 10.786 155.447 11.976C155.447 13.166 156.413 13.992 157.687 13.992ZM166.616 15.196C164.334 15.196 163.004 13.222 163.004 9.68C163.004 6.138 164.334 4.164 166.616 4.164C168.898 4.164 170.228 6.138 170.228 9.68C170.228 13.222 168.898 15.196 166.616 15.196ZM166.616 13.992C168.03 13.992 168.884 12.452 168.884 9.68C168.884 6.908 168.03 5.368 166.616 5.368C165.202 5.368 164.348 6.908 164.348 9.68C164.348 12.452 165.202 13.992 166.616 13.992ZM172.183 4.36H175.403C177.979 4.36 179.407 5.536 179.407 7.636C179.407 9.736 178.021 10.912 175.529 10.912H173.499V15H172.183V4.36ZM173.499 9.68H175.291C177.069 9.68 178.063 8.952 178.063 7.636C178.063 6.32 177.069 5.592 175.291 5.592H173.499V9.68ZM187.335 15H186.019V4.36H187.657L191.073 11.542H191.157L194.573 4.36H196.211V15H194.895V6.922H194.811L191.745 13.292H190.485L187.419 6.922H187.335V15ZM201.276 15L197.468 4.36H198.882L201.962 13.25H202.046L205.126 4.36H206.54L202.732 15H201.276Z" fill="#614208"/>
          <rect x="218.812" y="1.5" width="1" height="16" fill="#C18B24" fill-opacity="0.1"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M233.555 0.5C232.592 0.5 231.812 1.28063 231.812 2.24359V16.7564C231.812 17.7194 232.592 18.5 233.555 18.5H253.546C254.509 18.5 255.29 17.7194 255.29 16.7564V2.24359C255.29 1.28063 254.509 0.5 253.546 0.5H233.555ZM242.041 13.8049C241.958 13.8049 241.905 13.7671 241.882 13.6914L241.656 12.9158L243.356 11.963L243.891 13.646C243.898 13.6612 243.902 13.6801 243.902 13.7028C243.902 13.7709 243.861 13.8049 243.777 13.8049H242.041ZM236.594 13.7709C236.611 13.7879 236.635 13.7986 236.665 13.8028L236.599 13.6926L241.076 11.1836H239.386C239.348 11.1836 239.333 11.1609 239.34 11.1155L240.192 8.19908C240.199 8.17639 240.21 8.16504 240.226 8.16504C240.241 8.15747 240.252 8.16882 240.26 8.19908L241.122 11.1155C241.127 11.1314 241.129 11.1445 241.127 11.1548L242.8 10.2168L241.451 5.97491C241.428 5.89925 241.379 5.86143 241.304 5.86143H239.159C239.083 5.86143 239.034 5.89925 239.011 5.97491L236.583 13.646C236.568 13.699 236.572 13.7406 236.594 13.7709ZM244.734 13.6687V11.1902L246.618 10.1343V12.1368C246.618 12.1746 246.637 12.1935 246.675 12.1935H247.844C248.146 12.1935 248.392 12.0876 248.581 11.8758C248.77 11.6564 248.869 11.3651 248.876 11.002V8.86853L250.712 7.83963C250.744 8.00133 250.76 8.17032 250.76 8.3466V11.3197C250.76 11.8115 250.635 12.2465 250.386 12.6247C250.136 13.003 249.784 13.2943 249.33 13.4985C248.876 13.7028 248.354 13.8049 247.764 13.8049H244.871C244.833 13.8049 244.799 13.7936 244.768 13.7709C244.746 13.7406 244.734 13.7066 244.734 13.6687ZM244.734 9.13265L246.618 8.07678V7.52956C246.618 7.49173 246.637 7.47282 246.675 7.47282H247.696L249.685 6.35798C249.574 6.28789 249.456 6.22451 249.33 6.16782C248.876 5.96356 248.354 5.86143 247.764 5.86143H244.871C244.833 5.86143 244.799 5.87656 244.768 5.90682C244.746 5.92952 244.734 5.95978 244.734 5.9976V9.13265Z" fill="url(#paint2_linear_1832_101731)"/>
          <path d="M268.985 7.958H274.963V9.022H268.831C268.243 12.018 266.661 14.006 263.567 15.966L262.853 14.986C265.667 13.32 267.081 11.57 267.641 9.022H262.951V7.958H267.823C267.879 7.538 267.921 7.118 267.935 6.656C267.949 6.432 267.977 5.634 267.991 5.046H263.763V4.01H274.137V5.046H269.125C269.111 5.648 269.097 6.502 269.083 6.74C269.069 7.16 269.027 7.566 268.985 7.958ZM273.703 13.446C273.717 13.124 273.745 11.864 273.745 11.542L274.879 11.766L274.795 13.894C274.753 15.084 274.291 15.518 272.821 15.518H270.819C269.615 15.518 269.097 15.042 269.097 13.726V9.89H270.203V13.432C270.203 14.188 270.483 14.44 271.183 14.44H272.583C273.437 14.44 273.675 14.23 273.703 13.446ZM283.895 2.988L284.763 4.92H289.103V5.956H279.499V8.336C279.499 11.934 279.205 13.488 277.847 15.966L276.783 15.392C278.113 13.068 278.365 11.682 278.365 8.462V4.92H283.433L282.677 3.226L283.895 2.988ZM297.713 6.18V8.266H303.089V9.302H290.867V8.266H296.635V6.18H293.373C293.093 6.726 292.771 7.244 292.421 7.734L291.497 7.16C292.421 5.886 292.967 4.808 293.401 3.492L294.381 3.828C294.241 4.262 294.073 4.696 293.877 5.144H296.635V3.198H297.713V5.144H302.263V6.18H297.713ZM293.513 15.952H292.449V10.772H301.507V15.952H300.401V15.266H293.513V15.952ZM300.401 14.244V11.794H293.513V14.244H300.401Z" fill="#614208"/>
          <defs>
          <linearGradient id="paint0_linear_1832_101731" x1="10.5" y1="0.5" x2="10.5" y2="18.5" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E3A92F"/>
          <stop offset="1" stop-color="#C28C0C"/>
          </linearGradient>
          <linearGradient id="paint1_linear_1832_101731" x1="120.406" y1="0.5" x2="120.406" y2="18.5" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E3A92F"/>
          <stop offset="1" stop-color="#C28C0C"/>
          </linearGradient>
          <linearGradient id="paint2_linear_1832_101731" x1="243.551" y1="0.5" x2="243.551" y2="18.5" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E3A92F"/>
          <stop offset="1" stop-color="#C28C0C"/>
          </linearGradient>
          </defs>
        </svg>
        <svg v-show="themeClass === 'themeDark'" width="304" height="19" viewBox="0 0 304 19" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M1.74359 0.5C0.780632 0.5 0 1.28063 0 2.24359V16.7564C0 17.7194 0.780631 18.5 1.74359 18.5H19.2564C20.2194 18.5 21 17.7194 21 16.7564V2.24359C21 1.28063 20.2194 0.5 19.2564 0.5H1.74359ZM13.6589 4.62482C13.7793 4.62482 13.8769 4.72239 13.8769 4.84275V6.65688C13.8769 6.77724 13.7793 6.87482 13.6589 6.87482H12.5948C12.4744 6.87482 12.3769 6.97239 12.3769 7.09275V12.1246C12.3769 12.125 12.3765 12.1254 12.3761 12.1254C12.3757 12.1254 12.3754 12.1257 12.3754 12.1261C12.3749 13.5755 11.1998 14.7503 9.75039 14.7503C8.30064 14.7503 7.12539 13.575 7.12539 12.1251C7.12539 10.6753 8.30064 9.5 9.75039 9.5C9.7927 9.5 9.83478 9.501 9.87661 9.50298C10.0091 9.50926 10.1269 9.40801 10.1269 9.27532V6.87482V6.125V4.84275C10.1269 4.72239 10.2244 4.62482 10.3448 4.62482H13.6589Z" fill="url(#paint0_linear_2289_120149)"/>
          <path d="M40.8826 9.778V10.73H39.4406C39.4126 11.528 39.3846 12.298 39.3566 12.9H40.6166V13.866H39.3286C39.3146 13.992 39.3146 14.076 39.3146 14.118C39.2446 15.392 38.7686 15.756 37.3826 15.756H35.2966L35.0866 14.776H37.0466C37.9706 14.776 38.2786 14.566 38.3206 13.894V13.866H32.0346L32.3846 10.73H31.2926V9.778H32.4826L32.7906 6.992C32.5806 7.258 32.3706 7.524 32.1466 7.776L31.4326 7.02C32.4686 5.872 33.4486 4.304 34.0226 2.96L34.9606 3.282C34.8066 3.646 34.6246 4.024 34.4146 4.416H40.5466V5.368H33.8826C33.6306 5.788 33.3646 6.208 33.0706 6.6H39.5806C39.5806 6.768 39.5246 8.21 39.4686 9.778H40.8826ZM31.7546 5.2L31.1806 6.026C30.5506 5.508 29.5706 4.738 28.9266 4.248L29.5286 3.45C30.2426 3.982 31.0966 4.668 31.7546 5.2ZM31.3626 8.546L30.7606 9.428C30.1026 8.854 29.1366 8 28.4926 7.524L29.0946 6.712C29.7246 7.216 30.7186 8.028 31.3626 8.546ZM38.6006 7.552H33.7286L33.4766 9.778H38.5026C38.5586 8.686 38.6006 7.748 38.6006 7.552ZM34.9046 8.294L35.4366 7.58C35.9546 7.958 36.7806 8.574 37.2286 8.952L36.6686 9.708C36.1926 9.302 35.4366 8.7 34.9046 8.294ZM33.1126 12.9H38.3626C38.3906 12.298 38.4326 11.514 38.4606 10.73H33.3646L33.1126 12.9ZM37.0466 12.144L36.4586 12.872C36.0246 12.494 35.1146 11.78 34.6526 11.444L35.1846 10.744C35.6326 11.066 36.5846 11.78 37.0466 12.144ZM29.6966 15.882L28.6886 15.574C28.8846 14.902 29.8506 11.444 30.0466 10.758L30.9986 11.052C30.7886 11.738 29.8786 15.196 29.6966 15.882ZM43.9066 3.632H53.3986V6.922H43.9066V3.632ZM52.3766 4.948V4.318H44.9566V4.948H52.3766ZM44.9566 5.592V6.25H52.3766V5.592H44.9566ZM42.4366 8.392V7.65H54.8826V8.392H42.4366ZM53.8186 14.104H49.1706V14.846H54.7426V15.588H42.5766V14.846H48.1626V14.104H43.5006V13.376H48.1626V12.62H43.9066V9.148H53.4126V12.62H49.1706V13.376H53.8186V14.104ZM48.1626 10.534V9.82H44.9426V10.534H48.1626ZM49.1706 10.534H52.3766V9.82H49.1706V10.534ZM44.9426 11.206V11.92H48.1626V11.206H44.9426ZM49.1706 11.206V11.92H52.3766V11.206H49.1706ZM64.7946 3.296V5.354H67.9866V15.966H66.9646V15.154H58.3546V15.966H57.3466V5.354H60.5246V3.296H61.5606V5.354H63.7586V3.296H64.7946ZM60.5246 9.68V6.39H58.3546V9.68H60.5246ZM63.7586 9.68V6.39H61.5606V9.68H63.7586ZM66.9646 9.68V6.39H64.7946V9.68H66.9646ZM60.5246 10.688H58.3546V14.132H60.5246V10.688ZM63.7586 14.132V10.688H61.5606V14.132H63.7586ZM64.7946 10.688V14.132H66.9646V10.688H64.7946ZM78.1086 4.556H82.8406V5.494H72.8306V8.868C72.8306 12.424 72.5506 13.754 71.4306 15.98L70.4366 15.462C71.5706 13.236 71.8226 11.99 71.8226 8.966V4.556H76.8906C76.7506 4.108 76.5966 3.646 76.4706 3.268L77.6046 3.072C77.7446 3.478 77.9546 4.066 78.1086 4.556ZM78.8086 12.858H82.8686V13.838H78.8086V15.966H77.7866V13.838H73.1246V12.858H77.7866V11.346H74.1326V10.394C74.5526 9.75 75.0286 8.952 75.4766 8.154H73.4186V7.202H75.9806C76.2466 6.698 76.4706 6.222 76.6526 5.802L77.6886 5.956C77.5206 6.348 77.3106 6.768 77.1006 7.202H82.3506V8.154H76.5826C76.1486 8.966 75.6726 9.764 75.2666 10.394H77.7866V8.714H78.8086V10.394H82.0566V11.346H78.8086V12.858Z" fill="#C9AF7D"/>
          <rect opacity="0.1" x="95.6666" y="1.5" width="1" height="16" fill="#C18B24"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M110.41 0.5C109.447 0.5 108.667 1.28063 108.667 2.24359V16.7564C108.667 17.7194 109.447 18.5 110.41 18.5H130.401C131.364 18.5 132.145 17.7194 132.145 16.7564V2.24359C132.145 1.28063 131.364 0.5 130.401 0.5H110.41ZM117.764 7.19733C117.742 7.21969 117.731 7.2495 117.731 7.28677V10.1712C117.731 10.2085 117.713 10.2271 117.676 10.2271H115.627C115.59 10.2271 115.572 10.2085 115.572 10.1712V7.28677C115.572 7.2495 115.557 7.21969 115.528 7.19733C115.506 7.16751 115.477 7.15261 115.44 7.15261H113.884C113.848 7.15261 113.815 7.16751 113.786 7.19733C113.764 7.21969 113.753 7.2495 113.753 7.28677V14.8445C113.753 14.8818 113.764 14.9153 113.786 14.9452C113.815 14.9675 113.848 14.9787 113.884 14.9787H115.44C115.477 14.9787 115.506 14.9675 115.528 14.9452C115.557 14.9153 115.572 14.8818 115.572 14.8445V11.8706C115.572 11.8334 115.59 11.8147 115.627 11.8147H117.676C117.713 11.8147 117.731 11.8334 117.731 11.8706V14.8445C117.731 14.8818 117.742 14.9153 117.764 14.9452C117.793 14.9675 117.826 14.9787 117.862 14.9787H119.418C119.455 14.9787 119.484 14.9675 119.506 14.9452C119.535 14.9153 119.55 14.8818 119.55 14.8445V7.28677C119.55 7.2495 119.535 7.21969 119.506 7.19733C119.484 7.16751 119.455 7.15261 119.418 7.15261H117.862C117.826 7.15261 117.793 7.16751 117.764 7.19733ZM121.271 14.9452C121.3 14.9675 121.333 14.9787 121.37 14.9787H124.164C124.734 14.9787 125.238 14.8781 125.676 14.6768C126.115 14.4756 126.454 14.1886 126.695 13.816C126.937 13.4433 127.057 13.0147 127.057 12.5302V9.60105C127.057 9.11658 126.937 8.68801 126.695 8.31534C126.454 7.94267 126.115 7.65571 125.676 7.45447C125.238 7.25323 124.734 7.15261 124.164 7.15261H121.37C121.333 7.15261 121.3 7.16751 121.271 7.19733C121.249 7.21969 121.238 7.2495 121.238 7.28677V14.8445C121.238 14.8818 121.249 14.9153 121.271 14.9452ZM123.112 13.3911C123.075 13.3911 123.057 13.3725 123.057 13.3352V8.79609C123.057 8.75882 123.075 8.74018 123.112 8.74018H124.23C124.529 8.74018 124.77 8.84826 124.953 9.06441C125.143 9.2731 125.238 9.55633 125.238 9.9141V12.2172C125.231 12.575 125.136 12.8619 124.953 13.0781C124.77 13.2868 124.533 13.3911 124.241 13.3911H123.112ZM110.622 2.89309C110.622 2.65235 110.817 2.45719 111.058 2.45719H113.317C113.557 2.45719 113.753 2.65235 113.753 2.89309V3.58651C113.753 3.82725 113.557 4.02241 113.317 4.02241H111.058C110.817 4.02241 110.622 3.82725 110.622 3.58651V2.89309ZM116.537 2.45719C116.297 2.45719 116.101 2.65235 116.101 2.89309V3.58651C116.101 3.82725 116.297 4.02241 116.537 4.02241H118.796C119.037 4.02241 119.232 3.82725 119.232 3.58651V2.89309C119.232 2.65235 119.037 2.45719 118.796 2.45719H116.537ZM121.579 2.89309C121.579 2.65235 121.774 2.45719 122.015 2.45719H124.274C124.514 2.45719 124.709 2.65235 124.709 2.89309V3.58651C124.709 3.82725 124.514 4.02241 124.274 4.02241H122.015C121.774 4.02241 121.579 3.82725 121.579 3.58651V2.89309ZM127.494 2.45719C127.253 2.45719 127.058 2.65235 127.058 2.89309V3.58651C127.058 3.82725 127.253 4.02241 127.494 4.02241H129.753C129.994 4.02241 130.189 3.82725 130.189 3.58651V2.89309C130.189 2.65235 129.994 2.45719 129.753 2.45719H127.494Z" fill="#DAB487"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M110.41 0.5C109.447 0.5 108.667 1.28063 108.667 2.24359V16.7564C108.667 17.7194 109.447 18.5 110.41 18.5H130.401C131.364 18.5 132.145 17.7194 132.145 16.7564V2.24359C132.145 1.28063 131.364 0.5 130.401 0.5H110.41ZM117.764 7.19733C117.742 7.21969 117.731 7.2495 117.731 7.28677V10.1712C117.731 10.2085 117.713 10.2271 117.676 10.2271H115.627C115.59 10.2271 115.572 10.2085 115.572 10.1712V7.28677C115.572 7.2495 115.557 7.21969 115.528 7.19733C115.506 7.16751 115.477 7.15261 115.44 7.15261H113.884C113.848 7.15261 113.815 7.16751 113.786 7.19733C113.764 7.21969 113.753 7.2495 113.753 7.28677V14.8445C113.753 14.8818 113.764 14.9153 113.786 14.9452C113.815 14.9675 113.848 14.9787 113.884 14.9787H115.44C115.477 14.9787 115.506 14.9675 115.528 14.9452C115.557 14.9153 115.572 14.8818 115.572 14.8445V11.8706C115.572 11.8334 115.59 11.8147 115.627 11.8147H117.676C117.713 11.8147 117.731 11.8334 117.731 11.8706V14.8445C117.731 14.8818 117.742 14.9153 117.764 14.9452C117.793 14.9675 117.826 14.9787 117.862 14.9787H119.418C119.455 14.9787 119.484 14.9675 119.506 14.9452C119.535 14.9153 119.55 14.8818 119.55 14.8445V7.28677C119.55 7.2495 119.535 7.21969 119.506 7.19733C119.484 7.16751 119.455 7.15261 119.418 7.15261H117.862C117.826 7.15261 117.793 7.16751 117.764 7.19733ZM121.271 14.9452C121.3 14.9675 121.333 14.9787 121.37 14.9787H124.164C124.734 14.9787 125.238 14.8781 125.676 14.6768C126.115 14.4756 126.454 14.1886 126.695 13.816C126.937 13.4433 127.057 13.0147 127.057 12.5302V9.60105C127.057 9.11658 126.937 8.68801 126.695 8.31534C126.454 7.94267 126.115 7.65571 125.676 7.45447C125.238 7.25323 124.734 7.15261 124.164 7.15261H121.37C121.333 7.15261 121.3 7.16751 121.271 7.19733C121.249 7.21969 121.238 7.2495 121.238 7.28677V14.8445C121.238 14.8818 121.249 14.9153 121.271 14.9452ZM123.112 13.3911C123.075 13.3911 123.057 13.3725 123.057 13.3352V8.79609C123.057 8.75882 123.075 8.74018 123.112 8.74018H124.23C124.529 8.74018 124.77 8.84826 124.953 9.06441C125.143 9.2731 125.238 9.55633 125.238 9.9141V12.2172C125.231 12.575 125.136 12.8619 124.953 13.0781C124.77 13.2868 124.533 13.3911 124.241 13.3911H123.112ZM110.622 2.89309C110.622 2.65235 110.817 2.45719 111.058 2.45719H113.317C113.557 2.45719 113.753 2.65235 113.753 2.89309V3.58651C113.753 3.82725 113.557 4.02241 113.317 4.02241H111.058C110.817 4.02241 110.622 3.82725 110.622 3.58651V2.89309ZM116.537 2.45719C116.297 2.45719 116.101 2.65235 116.101 2.89309V3.58651C116.101 3.82725 116.297 4.02241 116.537 4.02241H118.796C119.037 4.02241 119.232 3.82725 119.232 3.58651V2.89309C119.232 2.65235 119.037 2.45719 118.796 2.45719H116.537ZM121.579 2.89309C121.579 2.65235 121.774 2.45719 122.015 2.45719H124.274C124.514 2.45719 124.709 2.65235 124.709 2.89309V3.58651C124.709 3.82725 124.514 4.02241 124.274 4.02241H122.015C121.774 4.02241 121.579 3.82725 121.579 3.58651V2.89309ZM127.494 2.45719C127.253 2.45719 127.058 2.65235 127.058 2.89309V3.58651C127.058 3.82725 127.253 4.02241 127.494 4.02241H129.753C129.994 4.02241 130.189 3.82725 130.189 3.58651V2.89309C130.189 2.65235 129.994 2.45719 129.753 2.45719H127.494Z" fill="url(#paint1_linear_2289_120149)"/>
          <path d="M142.634 15H141.318V5.886L139.26 6.964V5.578L141.654 4.36H142.634V15ZM148.76 15.196C146.478 15.196 145.148 13.222 145.148 9.68C145.148 6.138 146.478 4.164 148.76 4.164C151.042 4.164 152.372 6.138 152.372 9.68C152.372 13.222 151.042 15.196 148.76 15.196ZM148.76 13.992C150.174 13.992 151.028 12.452 151.028 9.68C151.028 6.908 150.174 5.368 148.76 5.368C147.346 5.368 146.492 6.908 146.492 9.68C146.492 12.452 147.346 13.992 148.76 13.992ZM157.687 15.196C155.629 15.196 154.103 13.922 154.103 12.116C154.103 10.884 154.999 9.722 156.077 9.33V9.288C155.237 8.98 154.495 8.014 154.495 6.992C154.495 5.326 155.853 4.164 157.687 4.164C159.521 4.164 160.879 5.326 160.879 6.992C160.879 8.056 160.137 9.022 159.283 9.288V9.33C160.375 9.75 161.271 10.898 161.271 12.116C161.271 13.922 159.745 15.196 157.687 15.196ZM157.687 8.742C158.751 8.742 159.563 8.042 159.563 7.076C159.563 6.054 158.751 5.368 157.687 5.368C156.623 5.368 155.811 6.054 155.811 7.076C155.811 8.042 156.623 8.742 157.687 8.742ZM157.687 13.992C158.961 13.992 159.927 13.166 159.927 11.976C159.927 10.786 158.961 9.946 157.687 9.946C156.413 9.946 155.447 10.786 155.447 11.976C155.447 13.166 156.413 13.992 157.687 13.992ZM166.616 15.196C164.334 15.196 163.004 13.222 163.004 9.68C163.004 6.138 164.334 4.164 166.616 4.164C168.898 4.164 170.228 6.138 170.228 9.68C170.228 13.222 168.898 15.196 166.616 15.196ZM166.616 13.992C168.03 13.992 168.884 12.452 168.884 9.68C168.884 6.908 168.03 5.368 166.616 5.368C165.202 5.368 164.348 6.908 164.348 9.68C164.348 12.452 165.202 13.992 166.616 13.992ZM172.183 4.36H175.403C177.979 4.36 179.407 5.536 179.407 7.636C179.407 9.736 178.021 10.912 175.529 10.912H173.499V15H172.183V4.36ZM173.499 9.68H175.291C177.069 9.68 178.063 8.952 178.063 7.636C178.063 6.32 177.069 5.592 175.291 5.592H173.499V9.68ZM187.335 15H186.019V4.36H187.657L191.073 11.542H191.157L194.573 4.36H196.211V15H194.895V6.922H194.811L191.745 13.292H190.485L187.419 6.922H187.335V15ZM201.276 15L197.468 4.36H198.882L201.962 13.25H202.046L205.126 4.36H206.54L202.732 15H201.276Z" fill="#C9AF7D"/>
          <rect x="218.812" y="1.5" width="1" height="16" fill="#C18B24" fill-opacity="0.1"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M233.555 0.5C232.592 0.5 231.812 1.28063 231.812 2.24359V16.7564C231.812 17.7194 232.592 18.5 233.555 18.5H253.546C254.509 18.5 255.29 17.7194 255.29 16.7564V2.24359C255.29 1.28063 254.509 0.5 253.546 0.5H233.555ZM242.041 13.8049C241.958 13.8049 241.905 13.7671 241.882 13.6914L241.656 12.9158L243.356 11.963L243.891 13.646C243.898 13.6612 243.902 13.6801 243.902 13.7028C243.902 13.7709 243.861 13.8049 243.777 13.8049H242.041ZM236.594 13.7709C236.611 13.7879 236.635 13.7986 236.665 13.8028L236.599 13.6926L241.075 11.1836H239.386C239.348 11.1836 239.333 11.1609 239.34 11.1155L240.191 8.19908C240.199 8.17639 240.21 8.16504 240.225 8.16504C240.241 8.15747 240.252 8.16882 240.26 8.19908L241.122 11.1155C241.127 11.1314 241.129 11.1445 241.127 11.1548L242.8 10.2168L241.451 5.97491C241.428 5.89925 241.379 5.86143 241.304 5.86143H239.159C239.083 5.86143 239.034 5.89925 239.011 5.97491L236.583 13.646C236.568 13.699 236.571 13.7406 236.594 13.7709ZM244.734 13.6687V11.1902L246.618 10.1343V12.1368C246.618 12.1746 246.637 12.1935 246.675 12.1935H247.844C248.146 12.1935 248.392 12.0876 248.581 11.8758C248.77 11.6564 248.869 11.3651 248.876 11.002V8.86853L250.712 7.83963C250.744 8.00133 250.76 8.17032 250.76 8.3466V11.3197C250.76 11.8115 250.635 12.2465 250.386 12.6247C250.136 13.003 249.784 13.2943 249.33 13.4985C248.876 13.7028 248.354 13.8049 247.764 13.8049H244.871C244.833 13.8049 244.799 13.7936 244.768 13.7709C244.746 13.7406 244.734 13.7066 244.734 13.6687ZM244.734 9.13265L246.618 8.07678V7.52956C246.618 7.49173 246.637 7.47282 246.675 7.47282H247.696L249.685 6.35798C249.574 6.28789 249.456 6.22451 249.33 6.16782C248.876 5.96356 248.354 5.86143 247.764 5.86143H244.871C244.833 5.86143 244.799 5.87656 244.768 5.90682C244.746 5.92952 244.734 5.95978 244.734 5.9976V9.13265Z" fill="url(#paint2_linear_2289_120149)"/>
          <path d="M268.984 7.958H274.962V9.022H268.83C268.242 12.018 266.66 14.006 263.566 15.966L262.852 14.986C265.666 13.32 267.08 11.57 267.64 9.022H262.95V7.958H267.822C267.878 7.538 267.92 7.118 267.934 6.656C267.948 6.432 267.976 5.634 267.99 5.046H263.762V4.01H274.136V5.046H269.124C269.11 5.648 269.096 6.502 269.082 6.74C269.068 7.16 269.026 7.566 268.984 7.958ZM273.702 13.446C273.716 13.124 273.744 11.864 273.744 11.542L274.878 11.766L274.794 13.894C274.752 15.084 274.29 15.518 272.82 15.518H270.818C269.614 15.518 269.096 15.042 269.096 13.726V9.89H270.202V13.432C270.202 14.188 270.482 14.44 271.182 14.44H272.582C273.436 14.44 273.674 14.23 273.702 13.446ZM283.894 2.988L284.762 4.92H289.102V5.956H279.498V8.336C279.498 11.934 279.204 13.488 277.846 15.966L276.782 15.392C278.112 13.068 278.364 11.682 278.364 8.462V4.92H283.432L282.676 3.226L283.894 2.988ZM297.712 6.18V8.266H303.088V9.302H290.866V8.266H296.634V6.18H293.372C293.092 6.726 292.77 7.244 292.42 7.734L291.496 7.16C292.42 5.886 292.966 4.808 293.4 3.492L294.38 3.828C294.24 4.262 294.072 4.696 293.876 5.144H296.634V3.198H297.712V5.144H302.262V6.18H297.712ZM293.512 15.952H292.448V10.772H301.506V15.952H300.4V15.266H293.512V15.952ZM300.4 14.244V11.794H293.512V14.244H300.4Z" fill="#C9AF7D"/>
          <defs>
          <linearGradient id="paint0_linear_2289_120149" x1="10.5" y1="0.5" x2="10.5" y2="18.5" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E3A92F"/>
          <stop offset="1" stop-color="#C28C0C"/>
          </linearGradient>
          <linearGradient id="paint1_linear_2289_120149" x1="120.406" y1="0.5" x2="120.406" y2="18.5" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E3A92F"/>
          <stop offset="1" stop-color="#C28C0C"/>
          </linearGradient>
          <linearGradient id="paint2_linear_2289_120149" x1="243.551" y1="0.5" x2="243.551" y2="18.5" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E3A92F"/>
          <stop offset="1" stop-color="#C28C0C"/>
          </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
    <div class="vip-packages">
      <div
        class="vip-packages-item"
        v-for="item in packages"
        :key="item.id"
        :class="{ active: item.id === selectItem.id }"
        @click="handleClickItem(item)"
      >
        <div v-if="item.tips" :class="['tips', { orange: item.isOrange }]">{{ item.tips }}</div>
        <div class="days">{{ item.title }}</div>
        <div class="price">
          <span>¥{{ formatValue(item.fee) }}</span>
          <span v-if="item.old_fee !== item.fee" class="origin">¥{{ formatValue(item.old_fee) }}</span>
          <QuestionMarkTip  v-if="item.old_fee !== item.fee" />
        </div>
        <div class="day-price"><span>{{ formatValue(item.day_fee) }}</span>元/天</div>
      </div>
    </div>
    <div class="pay-info flex-between">
      <div class="left flex-column">
        <h3>微信扫码支付</h3>
        <div>
          <div class="price" v-html="formatPayTitle"></div>
          <p>有效期至-{{ expirationDate }}</p>
        </div>
      </div>
      <div class="code flex-center">
        <img :src="qrCodeURL" alt="QR Code">
      </div>
    </div>
  </div>
  <div v-else class="user-vip" :class="[
    isVip && 'vip',
    from === 'mv' && 'mv',
    ['profile', 'mine'].includes(from) && 'mine-user-vip'
  ]">
    <div class="user-vip-top">
      <div class="user-vip-info">
        <div class="avatar-default-wrapper">
          <div class="avatar" :style="{ backgroundImage: `url(${userInfo.avatar || avatar})` }"></div>
        </div>
        <div class="user-vip-info-user">
          <div class="username">
            {{ userInfo.username }}
          </div>
          <div v-if="isLogin" class="desc">{{ vipDesc }}</div>
        </div>
      </div>
      <div v-if="from !== 'mv'" class="user-vip-entry">
        <div
          class="active"
          @click="handleExchange"
          v-log="{
            event_type: 'click',
            event_name: '6008',
            event_data: {
              str1: '我的页',
              str2: '兑换VIP',
              str3: '点击兑换VIP按钮',
              str4: 'click',
            }
          }"
        >兑换VIP</div>
        <div @click="handleClickBuyOrders">购买记录</div>
        <div
          @click="handleSetting"
          v-log="{
            event_type: 'click',
            event_name: '6008',
            event_data: {
              str1: '我的页',
              str2: '设置',
              str3: '设置',
              str4: 'click',
            }
          }"
        >设置</div>
      </div>
      <div v-if="from === 'mv'" class="packages-title">
        <svg width="398" height="21" viewBox="0 0 398 21" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M2.5766 0.718262C1.61364 0.718262 0.833008 1.4989 0.833008 2.46185V18.537C0.833008 19.5 1.61364 20.2806 2.5766 20.2806H21.9122C22.8751 20.2806 23.6557 19.5 23.6557 18.537V2.46185C23.6557 1.49889 22.8751 0.718262 21.9122 0.718262H2.5766ZM15.6964 5.2012C15.8168 5.2012 15.9144 5.29878 15.9144 5.41914V7.42856C15.9144 7.54892 15.8168 7.6465 15.6964 7.6465H14.5021C14.3818 7.6465 14.2842 7.74407 14.2842 7.86443V13.3517C14.2842 13.3522 14.2838 13.3525 14.2834 13.3525C14.2829 13.3525 14.2825 13.3529 14.2825 13.3534C14.282 14.9286 13.0049 16.2053 11.4297 16.2053C9.8541 16.2053 8.57684 14.928 8.57684 13.3523C8.57684 11.7767 9.8541 10.4993 11.4297 10.4993C11.483 10.4993 11.5361 10.5008 11.5887 10.5037C11.7212 10.511 11.8389 10.4097 11.8389 10.277V7.6465V6.83136V5.41914C11.8389 5.29878 11.9365 5.2012 12.0568 5.2012H15.6964Z" :fill="color"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M2.5766 0.718262C1.61364 0.718262 0.833008 1.4989 0.833008 2.46185V18.537C0.833008 19.5 1.61364 20.2806 2.5766 20.2806H21.9122C22.8751 20.2806 23.6557 19.5 23.6557 18.537V2.46185C23.6557 1.49889 22.8751 0.718262 21.9122 0.718262H2.5766ZM15.6964 5.2012C15.8168 5.2012 15.9144 5.29878 15.9144 5.41914V7.42856C15.9144 7.54892 15.8168 7.6465 15.6964 7.6465H14.5021C14.3818 7.6465 14.2842 7.74407 14.2842 7.86443V13.3517C14.2842 13.3522 14.2838 13.3525 14.2834 13.3525C14.2829 13.3525 14.2825 13.3529 14.2825 13.3534C14.282 14.9286 13.0049 16.2053 11.4297 16.2053C9.8541 16.2053 8.57684 14.928 8.57684 13.3523C8.57684 11.7767 9.8541 10.4993 11.4297 10.4993C11.483 10.4993 11.5361 10.5008 11.5887 10.5037C11.7212 10.511 11.8389 10.4097 11.8389 10.277V7.6465V6.83136V5.41914C11.8389 5.29878 11.9365 5.2012 12.0568 5.2012H15.6964Z" fill="#DAB487"/>
          <path d="M45.4263 11.0315V12.1195H43.7783C43.7463 13.0315 43.7143 13.9115 43.6823 14.5995H45.1223V15.7035H43.6503C43.6343 15.8475 43.6343 15.9435 43.6343 15.9915C43.5543 17.4475 43.0103 17.8635 41.4263 17.8635H39.0423L38.8023 16.7435H41.0423C42.0983 16.7435 42.4503 16.5035 42.4983 15.7355V15.7035H35.3143L35.7143 12.1195H34.4663V11.0315H35.8263L36.1783 7.84751C35.9383 8.15151 35.6983 8.45551 35.4423 8.74351L34.6263 7.87951C35.8103 6.56751 36.9303 4.77551 37.5863 3.23951L38.6583 3.60751C38.4823 4.02351 38.2743 4.45551 38.0343 4.90351H45.0423V5.99151H37.4263C37.1383 6.47151 36.8343 6.95151 36.4983 7.39951H43.9383C43.9383 7.59151 43.8743 9.23951 43.8103 11.0315H45.4263ZM34.9943 5.79951L34.3383 6.74351C33.6183 6.15151 32.4983 5.27151 31.7623 4.71151L32.4503 3.79951C33.2663 4.40751 34.2423 5.19151 34.9943 5.79951ZM34.5463 9.62351L33.8583 10.6315C33.1063 9.97551 32.0023 8.99951 31.2663 8.45551L31.9543 7.52751C32.6743 8.10351 33.8103 9.03151 34.5463 9.62351ZM42.8183 8.48751H37.2503L36.9623 11.0315H42.7063C42.7703 9.78351 42.8183 8.71151 42.8183 8.48751ZM38.5943 9.33551L39.2023 8.51951C39.7943 8.95151 40.7383 9.65551 41.2503 10.0875L40.6103 10.9515C40.0663 10.4875 39.2023 9.79951 38.5943 9.33551ZM36.5463 14.5995H42.5463C42.5783 13.9115 42.6263 13.0155 42.6583 12.1195H36.8343L36.5463 14.5995ZM41.0423 13.7355L40.3703 14.5675C39.8743 14.1355 38.8343 13.3195 38.3063 12.9355L38.9143 12.1355C39.4263 12.5035 40.5143 13.3195 41.0423 13.7355ZM32.6423 18.0075L31.4903 17.6555C31.7143 16.8875 32.8183 12.9355 33.0423 12.1515L34.1303 12.4875C33.8903 13.2715 32.8503 17.2235 32.6423 18.0075ZM48.8823 4.00751H59.7303V7.76751H48.8823V4.00751ZM58.5623 5.51151V4.79151H50.0823V5.51151H58.5623ZM50.0823 6.24751V6.99951H58.5623V6.24751H50.0823ZM47.2023 9.44751V8.59951H61.4263V9.44751H47.2023ZM60.2103 15.9755H54.8983V16.8235H61.2663V17.6715H47.3623V16.8235H53.7463V15.9755H48.4183V15.1435H53.7463V14.2795H48.8823V10.3115H59.7463V14.2795H54.8983V15.1435H60.2103V15.9755ZM53.7463 11.8955V11.0795H50.0663V11.8955H53.7463ZM54.8983 11.8955H58.5623V11.0795H54.8983V11.8955ZM50.0663 12.6635V13.4795H53.7463V12.6635H50.0663ZM54.8983 12.6635V13.4795H58.5623V12.6635H54.8983ZM72.7543 3.62351V5.97551H76.4023V18.1035H75.2343V17.1755H65.3943V18.1035H64.2423V5.97551H67.8743V3.62351H69.0583V5.97551H71.5703V3.62351H72.7543ZM67.8743 10.9195V7.15951H65.3943V10.9195H67.8743ZM71.5703 10.9195V7.15951H69.0583V10.9195H71.5703ZM75.2343 10.9195V7.15951H72.7543V10.9195H75.2343ZM67.8743 12.0715H65.3943V16.0075H67.8743V12.0715ZM71.5703 16.0075V12.0715H69.0583V16.0075H71.5703ZM72.7543 12.0715V16.0075H75.2343V12.0715H72.7543ZM87.9703 5.06351H93.3783V6.13551H81.9383V9.99151C81.9383 14.0555 81.6183 15.5755 80.3383 18.1195L79.2023 17.5275C80.4983 14.9835 80.7863 13.5595 80.7863 10.1035V5.06351H86.5783C86.4183 4.55151 86.2423 4.02351 86.0983 3.59151L87.3943 3.36751C87.5543 3.83151 87.7943 4.50351 87.9703 5.06351ZM88.7703 14.5515H93.4103V15.6715H88.7703V18.1035H87.6023V15.6715H82.2743V14.5515H87.6023V12.8235H83.4263V11.7355C83.9063 10.9995 84.4503 10.0875 84.9623 9.17551H82.6103V8.08751H85.5383C85.8423 7.51151 86.0983 6.96751 86.3063 6.48751L87.4903 6.66351C87.2983 7.11151 87.0583 7.59151 86.8183 8.08751H92.8183V9.17551H86.2263C85.7303 10.1035 85.1863 11.0155 84.7223 11.7355H87.6023V9.81551H88.7703V11.7355H92.4823V12.8235H88.7703V14.5515Z" fill="currentColor" fill-opacity="0.8"/>
          <rect x="122.322" y="2.49951" width="1" height="16" fill="currentColor" fill-opacity="0.1"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M153.066 0.945801C152.103 0.945801 151.322 1.72643 151.322 2.68939V18.3099C151.322 19.2729 152.103 20.0535 153.066 20.0535H174.502C175.465 20.0535 176.245 19.2729 176.245 18.3099V2.68939C176.245 1.72643 175.465 0.945801 174.502 0.945801H153.066ZM160.979 8.05512C160.956 8.07886 160.944 8.1105 160.944 8.15007V11.212C160.944 11.2516 160.925 11.2714 160.886 11.2714H158.711C158.672 11.2714 158.653 11.2516 158.653 11.212V8.15007C158.653 8.1105 158.637 8.07886 158.606 8.05512C158.583 8.02347 158.552 8.00765 158.513 8.00765H156.861C156.822 8.00765 156.787 8.02347 156.756 8.05512C156.733 8.07886 156.721 8.1105 156.721 8.15007V16.1729C156.721 16.2125 156.733 16.2481 156.756 16.2798C156.787 16.3035 156.822 16.3154 156.861 16.3154H158.513C158.552 16.3154 158.583 16.3035 158.606 16.2798C158.637 16.2481 158.653 16.2125 158.653 16.1729V13.016C158.653 12.9764 158.672 12.9567 158.711 12.9567H160.886C160.925 12.9567 160.944 12.9764 160.944 13.016V16.1729C160.944 16.2125 160.956 16.2481 160.979 16.2798C161.01 16.3035 161.045 16.3154 161.084 16.3154H162.736C162.775 16.3154 162.806 16.3035 162.829 16.2798C162.86 16.2481 162.875 16.2125 162.875 16.1729V8.15007C162.875 8.1105 162.86 8.07886 162.829 8.05512C162.806 8.02347 162.775 8.00765 162.736 8.00765H161.084C161.045 8.00765 161.01 8.02347 160.979 8.05512ZM164.702 16.2798C164.733 16.3035 164.768 16.3154 164.807 16.3154H167.773C168.378 16.3154 168.913 16.2085 169.379 15.9949C169.844 15.7813 170.205 15.4767 170.461 15.0811C170.717 14.6855 170.845 14.2305 170.845 13.7162V10.6068C170.845 10.0925 170.717 9.63754 170.461 9.24194C170.205 8.84633 169.844 8.54171 169.379 8.32809C168.913 8.11446 168.378 8.00765 167.773 8.00765H164.807C164.768 8.00765 164.733 8.02347 164.702 8.05512C164.679 8.07886 164.667 8.1105 164.667 8.15007V16.1729C164.667 16.2125 164.679 16.2481 164.702 16.2798ZM166.657 14.6301C166.618 14.6301 166.598 14.6103 166.598 14.5707V9.75227C166.598 9.71271 166.618 9.69293 166.657 9.69293H167.843C168.161 9.69293 168.417 9.80765 168.611 10.0371C168.813 10.2586 168.913 10.5593 168.913 10.9391V13.3839C168.906 13.7637 168.805 14.0683 168.611 14.2978C168.417 14.5193 168.165 14.6301 167.855 14.6301H166.657ZM153.398 3.45917C153.398 3.21843 153.593 3.02327 153.834 3.02327H156.285C156.526 3.02327 156.721 3.21843 156.721 3.45917V4.24892C156.721 4.48966 156.526 4.68481 156.285 4.68481H153.834C153.593 4.68481 153.398 4.48966 153.398 4.24892V3.45917ZM159.65 3.02327C159.41 3.02327 159.215 3.21843 159.215 3.45917V4.24892C159.215 4.48966 159.41 4.68481 159.65 4.68481H162.102C162.342 4.68481 162.538 4.48966 162.538 4.24892V3.45917C162.538 3.21843 162.342 3.02327 162.102 3.02327H159.65ZM165.029 3.45917C165.029 3.21843 165.224 3.02327 165.465 3.02327H167.916C168.157 3.02327 168.352 3.21843 168.352 3.45917V4.24892C168.352 4.48966 168.157 4.68481 167.916 4.68481H165.465C165.224 4.68481 165.029 4.48966 165.029 4.24892V3.45917ZM171.282 3.02327C171.041 3.02327 170.846 3.21843 170.846 3.45917V4.24892C170.846 4.48966 171.041 4.68481 171.282 4.68481H173.733C173.974 4.68481 174.169 4.48966 174.169 4.24892V3.45917C174.169 3.21843 173.974 3.02327 173.733 3.02327H171.282Z" fill="#DAB487"/>
          <path d="M187.28 16.9995H185.776V6.58351L183.424 7.81551V6.23151L186.16 4.83951H187.28V16.9995ZM194.282 17.2235C191.674 17.2235 190.154 14.9675 190.154 10.9195C190.154 6.87151 191.674 4.61551 194.282 4.61551C196.89 4.61551 198.41 6.87151 198.41 10.9195C198.41 14.9675 196.89 17.2235 194.282 17.2235ZM194.282 15.8475C195.898 15.8475 196.874 14.0875 196.874 10.9195C196.874 7.75151 195.898 5.99151 194.282 5.99151C192.666 5.99151 191.69 7.75151 191.69 10.9195C191.69 14.0875 192.666 15.8475 194.282 15.8475ZM204.484 17.2235C202.132 17.2235 200.388 15.7675 200.388 13.7035C200.388 12.2955 201.412 10.9675 202.644 10.5195V10.4715C201.684 10.1195 200.836 9.01551 200.836 7.84751C200.836 5.94351 202.388 4.61551 204.484 4.61551C206.58 4.61551 208.132 5.94351 208.132 7.84751C208.132 9.06351 207.284 10.1675 206.308 10.4715V10.5195C207.556 10.9995 208.58 12.3115 208.58 13.7035C208.58 15.7675 206.836 17.2235 204.484 17.2235ZM204.484 9.84751C205.7 9.84751 206.628 9.04751 206.628 7.94351C206.628 6.77551 205.7 5.99151 204.484 5.99151C203.268 5.99151 202.34 6.77551 202.34 7.94351C202.34 9.04751 203.268 9.84751 204.484 9.84751ZM204.484 15.8475C205.94 15.8475 207.044 14.9035 207.044 13.5435C207.044 12.1835 205.94 11.2235 204.484 11.2235C203.028 11.2235 201.924 12.1835 201.924 13.5435C201.924 14.9035 203.028 15.8475 204.484 15.8475ZM214.688 17.2235C212.08 17.2235 210.56 14.9675 210.56 10.9195C210.56 6.87151 212.08 4.61551 214.688 4.61551C217.296 4.61551 218.816 6.87151 218.816 10.9195C218.816 14.9675 217.296 17.2235 214.688 17.2235ZM214.688 15.8475C216.304 15.8475 217.28 14.0875 217.28 10.9195C217.28 7.75151 216.304 5.99151 214.688 5.99151C213.072 5.99151 212.096 7.75151 212.096 10.9195C212.096 14.0875 213.072 15.8475 214.688 15.8475ZM221.051 4.83951H224.731C227.675 4.83951 229.307 6.18351 229.307 8.58351C229.307 10.9835 227.723 12.3275 224.875 12.3275H222.555V16.9995H221.051V4.83951ZM222.555 10.9195H224.603C226.635 10.9195 227.771 10.0875 227.771 8.58351C227.771 7.07951 226.635 6.24751 224.603 6.24751H222.555V10.9195ZM238.367 16.9995H236.863V4.83951H238.735L242.639 13.0475H242.735L246.639 4.83951H248.511V16.9995H247.007V7.76751H246.911L243.407 15.0475H241.967L238.463 7.76751H238.367V16.9995ZM254.3 16.9995L249.948 4.83951H251.564L255.084 14.9995H255.18L258.7 4.83951H260.316L255.964 16.9995H254.3Z" fill="currentColor" fill-opacity="0.8"/>
          <rect x="288.912" y="2.49951" width="1" height="16" fill="currentColor" fill-opacity="0.1"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M319.656 0.945801C318.693 0.945801 317.912 1.72643 317.912 2.68939V18.3101C317.912 19.273 318.693 20.0536 319.656 20.0536H341.092C342.055 20.0536 342.835 19.273 342.835 18.3101V2.68939C342.835 1.72643 342.055 0.945801 341.092 0.945801H319.656ZM328.771 15.0694C328.683 15.0694 328.627 15.0293 328.603 14.949L328.362 14.1257L330.167 13.1143L330.735 14.9008C330.743 14.9169 330.747 14.9369 330.747 14.961C330.747 15.0333 330.703 15.0694 330.614 15.0694H328.771ZM322.989 15.0333C323.007 15.0514 323.032 15.0627 323.064 15.0672L322.994 14.9503L327.746 12.2868H325.953C325.912 12.2868 325.896 12.2627 325.904 12.2145L326.808 9.1186C326.816 9.09451 326.828 9.08246 326.844 9.08246C326.86 9.07443 326.872 9.08648 326.88 9.1186L327.796 12.2145C327.801 12.2314 327.803 12.2453 327.801 12.2563L329.577 11.2606L328.145 6.75754C328.121 6.67723 328.069 6.63707 327.988 6.63707H325.712C325.631 6.63707 325.579 6.67723 325.555 6.75754L322.977 14.9008C322.961 14.957 322.965 15.0012 322.989 15.0333ZM331.63 14.9249V12.2939L333.63 11.173V13.2987C333.63 13.3388 333.65 13.3589 333.69 13.3589H334.931C335.252 13.3589 335.513 13.2465 335.714 13.0216C335.915 12.7887 336.019 12.4795 336.027 12.094V9.82932L337.976 8.73709C338.01 8.90872 338.027 9.08809 338.027 9.2752V12.4313C338.027 12.9533 337.894 13.4151 337.629 13.8166C337.364 14.2182 336.991 14.5274 336.509 14.7442C336.027 14.961 335.473 15.0694 334.847 15.0694H331.775C331.735 15.0694 331.699 15.0574 331.666 15.0333C331.642 15.0012 331.63 14.965 331.63 14.9249ZM331.63 10.1097L333.63 8.98883V8.40787C333.63 8.36772 333.65 8.34764 333.69 8.34764H334.774L336.885 7.16422C336.768 7.08981 336.643 7.02251 336.509 6.96232C336.027 6.74549 335.473 6.63707 334.847 6.63707H331.775C331.735 6.63707 331.699 6.65314 331.666 6.68526C331.642 6.70935 331.63 6.74148 331.63 6.78163V10.1097Z" fill="#DAB487"/>
          <path d="M357.534 8.95151H364.366V10.1675H357.358C356.686 13.5915 354.878 15.8635 351.342 18.1035L350.526 16.9835C353.742 15.0795 355.358 13.0795 355.998 10.1675H350.638V8.95151H356.206C356.27 8.47151 356.318 7.99151 356.334 7.46351C356.35 7.20751 356.382 6.29551 356.398 5.62351H351.566V4.43951H363.422V5.62351H357.694C357.678 6.31151 357.662 7.28751 357.646 7.55951C357.63 8.03951 357.582 8.50351 357.534 8.95151ZM362.926 15.2235C362.942 14.8555 362.974 13.4155 362.974 13.0475L364.27 13.3035L364.174 15.7355C364.126 17.0955 363.598 17.5915 361.918 17.5915H359.63C358.254 17.5915 357.662 17.0475 357.662 15.5435V11.1595H358.926V15.2075C358.926 16.0715 359.246 16.3595 360.046 16.3595H361.646C362.622 16.3595 362.894 16.1195 362.926 15.2235ZM374.574 3.27151L375.566 5.47951H380.526V6.66351H369.55V9.38351C369.55 13.4955 369.214 15.2715 367.662 18.1035L366.446 17.4475C367.966 14.7915 368.254 13.2075 368.254 9.52751V5.47951H374.046L373.182 3.54351L374.574 3.27151ZM390.366 6.91951V9.30351H396.51V10.4875H382.542V9.30351H389.134V6.91951H385.406C385.086 7.54351 384.718 8.13551 384.318 8.69551L383.262 8.03951C384.318 6.58351 384.942 5.35151 385.438 3.84751L386.558 4.23151C386.398 4.72751 386.206 5.22351 385.982 5.73551H389.134V3.51151H390.366V5.73551H395.566V6.91951H390.366ZM385.566 18.0875H384.35V12.1675H394.702V18.0875H393.438V17.3035H385.566V18.0875ZM393.438 16.1355V13.3355H385.566V16.1355H393.438Z" fill="currentColor" fill-opacity="0.8"/>
        </svg>
      </div>
    </div>
    <div class="user-vip-openvip">
      <div class="packages">
        <div v-if="from !== 'mv'" class="packages-title">
          <h3><span>VIP权益</span></h3>
          <img :src="imgs[themeClass].title" />
        </div>
        <div class="vip-packages flex">
          <div
            class="vip-packages-item"
            v-for="(item, index) in packages"
            :key="item.id"
            :class="[
              item.id === selectItem.id && 'active',
              packages.length - 1 === index && 'last',
            ]"
            @click="handleClickItem(item)"
          >
            <div
              v-if="item.tips"
              class="tips"
              :class="item.isOrange && 'orange'"
            >
              {{ item.tips }}
            </div>
            <div class="days">{{ item.title }}</div>
            <div class="day-price">
              <span>¥</span>{{ formatValue(item.day_fee) }}<span>元/天</span>
            </div>
            <div class="price">
              <span>¥{{ formatValue(item.fee) }}</span>
              <span v-if="item.fee !== item.old_fee" class="origin"
                >¥{{ formatValue(item.old_fee) }}</span
              >
              <QuestionMarkTip  v-if="item.old_fee !== item.fee" />
            </div>
          </div>
        </div>
      </div>
      <div class="pay-info">
        <div class="left">
          <div class="price" v-html="formatPayTitle"></div>
          <h3>微信扫码支付</h3>
          <p>有效期至-{{ expirationDate }}</p>
        </div>
        <div class="code">
          <img :src="qrCodeURL" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch, ref, computed, onBeforeMount, defineProps, nextTick, toRefs } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import useQRCode from '@/composables/useQRCode'
import get from 'lodash/get'
import { Toast } from 'vant'
import { addDays, format } from 'date-fns'
import {
  getVipPkg,
  getVipPkgQr,
} from '@/service/vip'
import { sendLog } from '@/directives/v-log/log'
import QuestionMarkTip from '@/subMoudle/underline-price-copywriting/QuestionMarkTip.vue'

const props = defineProps({
  from: {
    type: String,
    default: 'mine',
  },
})

const PAY_LOG_CONFIG = {
  'mv': {
    fr: '1845',
    str1: 'MV页面',
    str2: '我的-VIP 支付',
  },
  'mine': {
    fr: '1847',
    str1: '我的',
    str2: '我的-会员档位展示',
  },
}

const store = useStore()
const router = useRouter()
const { getQRCodeURL } = useQRCode()
const { from } = toRefs(props)

const userInfo = computed(() => store.state.userInfo)
const vipInfo = computed(() => store.state.vipInfo)
const isLogin = computed(() => !!userInfo.value.unionid)
const isVip = computed(() => !!vipInfo.value.end_time)
const unionid = computed(() => store.state.userInfo.unionid)
const currentSize = computed(() => store.state.currentSize);
const userType = computed(() => store.state.userInfo.userType)

let qrCodeURL = ref(
  'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
)

const packages = ref([])
const selectItem = ref({})

const imgs = {
  themeDark: {
    title: 'https://qncweb.ktvsky.com/20240517/other/747ff41a7fe5da2a1b9cc59704befa4d.png',
  },
  themeLight: {
    title: 'https://qncweb.ktvsky.com/20240517/other/99d8ba6932a783c19f099c8fafb9342c.png',
  },
  themeSystem: {
    title: 'https://qncweb.ktvsky.com/20240517/other/99d8ba6932a783c19f099c8fafb9342c.png',
  },
}
const themeClass = computed(() => store.state.themeClass)

const vipDesc = computed(() => {
  if (vipInfo.value.expire) {
    return '您的会员已过期';
  }
  if (isVip.value) {
    const formattedDate = vipInfo.value.end_time.split(' ')[0].replaceAll('-', '.');
    return `会员有效期至: ${formattedDate}`;
  }
  return '未开通 VIP';
})

const expirationDate = computed(() => {
  if (!selectItem.value.days) return ''
  const currentDate = isVip.value ? new Date(vipInfo.value.end_time) : new Date()
  const expirationDate = addDays(currentDate, selectItem.value.days)

  return format(expirationDate, 'yyyy.MM.dd')
})

const formatValue = (value) => {
  if (value === undefined || isNaN(value)) {
    return 'N/A'
  }
  return value / 100
}

const getVipQrcode = async () => {
  try {
    const { fr, str1, str2 } = PAY_LOG_CONFIG[from.value]
    
    const data = await getVipPkgQr({
      unionid: unionid.value,
      pkg_id: selectItem.value.id,
      fr,
    })

    const qr = get(data, 'qr', '')
    if (qr) {
      const qrCodeData = await getQRCodeURL(qr)
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData

        // 支付埋点 - 曝光
        sendLog({
          event_type: 'show',
          event_name: '1021',
          event_data: {
            str1,
            str2,
            str3: '任意支付档位',
            str4: 'click',
            str5: '已登录',
            str6: userType.value,
            str9: '车机端',
            str10: fr
          }
        })
      }
    }
  } catch (e) {
    console.log('vip getVipcode error', e)
  }
}

const formatPayTitle = computed(() => {
  const regex = /(\d+)(年|天)/g
  if (!selectItem.value.title) return ''

  const matches = selectItem.value.title.match(regex)
  if (!matches) return ''

  const formatted = matches
    .map((match) => {
      const [_, number, unit] = match.match(/(\d+)(年|天)/)
      return `<span class="user-vip-year-num">${number}</span>${unit}`
    })
    .join(' ')

  return formatted
})

const fetchConfig = async () => {
  if (!unionid.value) return

  const res = await getVipPkg(unionid.value)
  packages.value = res.data

  if (isVip.value) {
    const index = packages.value.findIndex((item) => !!item.tips)
    packages.value[index].isOrange = true
  } else {
    const reversedIndex = packages.value
      .slice()
      .reverse()
      .findIndex((item) => !!item.tips)
    const index = packages.value.length - 1 - reversedIndex
    packages.value[index].isOrange = true
  }

  const active =
    packages.value.find((item) => item.id === res.recommend_pkg) ||
    packages.value[0]
  selectItem.value = active

  getVipQrcode()
}

const handleClickItem = async (item) => {
  try {
    qrCodeURL.value =
      'https://qncweb.ktvsky.com/20211130/vadd/82b806b8bf560e49df69162835879c65.png'
    selectItem.value = item
    getVipQrcode()

    await nextTick()
    const activeElement = document.querySelector('.vip-packages-item.active');
    if (activeElement) {
      activeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      });
    }
  } catch (error) {
    console.log('handleClickItem error', error)
  }
}

const handleExchange = () => {
  try {
    router.push({
      name: 'vipExchange',
    })
    sendLog({
      event_type: '10000~50000',
      event_name: 10030,
      event_data: {
        str1: '我的',
        str2: '开通VIP',
        str3: '点击进入',
        str4: 'click',
        str5: '会员兑换',
        str6: 'click',
      },
    })
  } catch (error) {
    console.log('handleExchange error', error)
  }
}

const handleSetting = () => {
  try {
    router.push({
      name: 'setting',
    })
  } catch (error) {
    console.log('handleSetting error', error)
  }
}

const handleClickBuyOrders = () => {
  router.push({
    name: 'orders',
  })
}

const handleClickSmallTop = () => {
  router.push({
    name: 'more',
  })
}

watch(unionid, (val) => {
  if (val) {
    fetchConfig()
  }
})

onBeforeMount(() => {
  fetchConfig()
})
</script>

<style lang="stylus" scoped>
.dark-theme
  .user-vip-small
    --card-background: #FFFFFF1A;
    --modal-background: #22202C
    --item-background: rgba(255, 255, 255, 0.9)
    --payinfo-background #FFFFFF1A
    --payinfo-h3-color #FFFFFF66
    --packages-color: rgba(255, 255, 255, 1)
    --payinfo-p-color #DEB868
    --bottom-bg linear-gradient(90deg, rgba(255, 217, 140, 0.1) 0%, rgba(255, 235, 192, 0.1) 37%, rgba(255, 197, 76, 0.1) 100%);
    --border-bg: rgba(29, 29, 31, 0.1)
.user-vip-small
  --card-background: #FFFFFF;
  --packages-color: #66320F
  --packages-color2: #DD9949
  --modal-background: #E8EAEE
  --item-background: #FFFFFF
  --package-days-color: rgba(250, 217, 179, 1)
  --payinfo-background #FFFFFF
  --payinfo-h3-color #00000066
  --payinfo-p-color #66320F
  --bottom-bg linear-gradient(90deg, rgba(255, 217, 140, 0.3) 0%, rgba(255, 235, 192, 0.3) 37%, rgba(255, 197, 76, 0.3) 100%);
  --border-bg: rgba(255, 255, 255, 0.1)
  .user-vip-card
    width calc(359px * 3)
    height calc(140px * 3)
    background var(--card-background)
    border-radius calc(10px * 3)
    margin-top calc(10px * 3)
    .top
      flex 1
      padding calc(13px * 3) calc(20px * 3) 0
      position relative
      .svg-icon
        width calc(48px * 3)
        height calc(48px * 3)
        position absolute
        right calc(8px * 3)
        top 50%
        margin-top calc(-24px * 3)
      .username
        max-width calc(177px * 3)
      .avatar-default-wrapper
        width calc(64px * 3)
        height calc(64px * 3)
        margin-right calc(16px * 3)
      .avatar
        width 100%
        height 100%
      .desc
        font-size var(--font-size-small)
        background-size calc(32px * 3) auto
        padding 0 0 0 calc(38px * 3)
        line-height unset
    .bottom
      height calc(52px * 3)
      background: var(--bottom-bg)
  .vip-packages
    display flex
    padding calc(24px * 3) 0 0
    height auto
    overflow-x scroll
    overflow-y visible
    &-item
      width: calc(122px * 3);
      height: calc(124px * 3);
      border-radius: calc(10px * 3);
      background: var(--song-card-background-color)
      position relative
      text-align left
      padding-left calc(18px * 3)
      margin-right calc(16px * 3);
      flex-shrink: 0;
      
      *
        color #66320F
      .tips
        padding 0 14px
        height calc(26px * 3)
        line-height calc(26px * 3)
        font-size var(--font-size-small)
        position absolute
        left calc(4px * 3)
        top -40px
        background #FF114D
        border-radius 12px
        color #FFFFFF
      .days
        font-weight: 500;
        margin calc(21px * 3) 0 2px
        color: var(--packages-color)
      .price
        font-size: var(--font-size-small)
        display flex
        align-items center
        span
          color: var(--packages-color)
          opacity 0.8
        .origin
          // color rgba(255, 255, 255, 0.3)
          text-decoration line-through
          margin-left 8px
          opacity 0.5
      .day-price
        font-size: var(--font-size-small)
        margin 30px 0 0
        color var(--packages-color2)
        span
          font-size: calc(20px * 3)
          color var(--packages-color2)
          font-weight bold

    .active
      background: linear-gradient(321.82deg, #FFEAB7 5.61%, #FFF0C8 34.88%, #FFECB9 96.2%);
      .days, .day-price, .price
        color #883700
        span
          color #883700
  .pay-info
    height calc(135px * 3)
    background var(--payinfo-background)
    margin calc(16px * 3) 0 0
    border-radius calc(10px * 3)
    padding calc(18px * 3)
    width 100%
    .left
      flex 1
      text-align left
      justify-content space-between
      height 100%
      .price
        font-size var(--font-size-extra-small)
        margin-bottom 0px
        color #DD9949
        :deep(span)
          font-size calc(28px * 3)
          line-height 85px
          color #DD9949
          font-weight 600
      h3
        // color rgba(255, 255, 255, 0.8)
        // font-size 24px
        margin-bottom 0px
        color var(--text-tertiary-color)
      p
        // color rgba(255, 255, 255, 0.6)
        font-size var(--font-size-extra-small)
        // color var(--payinfo-p-color)
        opacity 0.6
        white-space nowrap
    .code
      display: flex;
      align-items: center;
      justify-content: center;
      width calc(108px * 3)
      height calc(108px * 3)
      background #fff
      margin-left 0px
      img
        width 96%
        height 96%
.light-theme
  .user-vip
    --vip-card-bg: rgba(255, 255, 255, 1)
    --vip-area-bg: rgba(255, 255, 255, 0.5)
    --package-days-color: rgba(221, 153, 73, 1)
    --payinfo-color: rgba(221, 153, 73, 1)
    --title-bg: linear-gradient(180deg, #5C390F 17.07%, #A48663 80.49%)

.user-vip
  --vip-area-bg: rgba(255, 255, 255, 0.06)
  --vip-card-bg: linear-gradient(0deg, rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.04)),
    linear-gradient(217.09deg, rgba(255, 238, 222, 0.12) 0%, rgba(255, 255, 255, 0.0228) 100%)
  --package-days-color: rgba(250, 217, 179, 1)
  --payinfo-color: rgba(220, 163, 79, 1)
  --title-bg: linear-gradient(180deg, #F9DBB8 0%, #BE935F 100%)

  &-top
    display flex
    justify-content space-between
    flex-wrap wrap
    align-items center
    margin-bottom 32px
  &-info
    display flex
    align-items center
    .avatar-default-wrapper
      width 120px
      height 120px
      border-radius 100%
      margin-right 16px
      overflow hidden
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(72px * var(--scale)) !important
        height calc(72px * var(--scale)) !important
      div
        background-size 100%
    &-user
      .username
        max-width 439px
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        margin-bottom 16px
        font-size var(--font-size-large)
      .desc
        color var(--text-secondary-color)
        font-size var(--font-size-small)
        line-height 50px
        height 50px
        padding-left 66px
        background-repeat no-repeat
        background-size 50px auto
        background-image url(https://qncweb.ktvsky.com/20250109/other/4b26d6a2cccd3b88e860da9b50d62fa4.png)
        background-position left center
  &-entry
    display flex
    div
      display flex
      justify-content center
      align-items center
      width: 200px;
      height: 80px;
      border-radius: 100px;
      border: 2px solid var(--border-color)
      background: var(--card-bg-color)
      color: var(--text-tertiary-color)
      margin-left 32px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(125px * var(--scale)) !important
        height calc(50px * var(--scale)) !important
    .active
      color rgba(219, 174, 106, 1)
      margin-right 0px
  &-openvip
    background var(--vip-area-bg)
    display flex
    border-radius calc(16px * var(--scale))
    .packages
      width 100%
      flex 1
      background url('https://qncweb.ktvsky.com/20231208/other/b2d860aba32a9a1fdf2d277e579be3d1.png') no-repeat right center
      background-size 143px auto
      padding 0 50px calc(24px * var(--scale)) 48px
      @media (max-width: 900px) and (min-width: 701px)  
        padding 0 50px calc(24px * var(--scale)) 40px!important
      &-title
        display flex
        align-items center
        justify-content space-between
        flex-wrap wrap
        border-bottom 1px solid var(--pressed-color)
        padding 40px 0
        margin-bottom 58px
        h3
          font-weight: 500;
          padding-left 67px
          background url('https://qncweb.ktvsky.com/20231208/other/d87306bc759edd6d14444eb2459f4716.png') no-repeat
          background-position left 0px
          background-size calc(50px * var(--scale)) auto
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            background-position left calc(8px * var(--scale)) !important
          span
            background: var(--title-bg)
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        img
          width 563px
          height auto

          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            width calc(327px * var(--scale)) !important
      .vip-packages
        min-width 640px
        border-radius 10px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          max-width calc(515px * 1.48) !important;
          overflow-x scroll
        &-item
          width: 200px;
          height: 260px;
          border-radius: 10px;
          background: var(--vip-card-bg)
          backdrop-filter: blur(100px);
          position relative
          text-align center
          margin-right 20px
          flex-shrink: 0;
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            width: calc(133px * 1.48) !important;
            height: calc(173px * 1.48) !important;
            border-radius: calc(7px * 1.48) !important;
          .tips
            padding 0 10px
            height 40px
            line-height 40px
            font-size 22px
            color #fff
            position absolute
            left 0
            top -20px
            background linear-gradient(90deg, #FF3D6B 0%, #8C1AFF 100%)
            border-radius 10px 10px 10px 0
            // 三分之二屏
            @media (max-width: 900px) and (min-width: 701px)  
              top 0 !important;
              //width: calc(100px * 1.48) !important;
              height: calc(24px * 1.48) !important;
              line-height: calc(24px * 1.48) !important;
            &.orange
              background linear-gradient(90deg, #ff3d3d 0%, #ff881a 100%)
          .days
            color: var(--package-days-color)
            // font-size: var(--font-size-small)
            font-weight: 700;
            margin-top 40px
            // 三分之二屏
            @media (max-width: 900px) and (min-width: 701px)  
              margin-top calc(40px * 1.48) !important;
          .day-price
            color: var(--package-days-color)
            font-size: 52px;
            margin 12px 0 9px
            font-weight: 700;
            // 三分之二屏
            @media (max-width: 900px) and (min-width: 701px)  
              margin calc(10px * 1.48)  0 calc(13px * 1.48) !important;
            span
              color: var(--package-days-color)
              font-size: var(--font-size-extra-small)
              font-weight: normal;
          .price
            // font-size: var(--font-size-extra-small)
            display flex
            justify-content center
            align-items center
            font-weight: 300;
            // *
            //   font-size: var(--font-size-extra-small)
            .origin
              opacity 0.5
              // font-size: var(--font-size-extra-small)
              text-decoration line-through
        
        .active
          background: linear-gradient(321.82deg, #FFE093 5.61%, #FFEBB5 34.88%, #FFECB9 96.2%);
          .days
            color rgba(67, 57, 31, 1) !important
          .day-price
            color rgba(136, 55, 0, 1) !important
            *
              color rgba(136, 55, 0, 1) !important
          .price
            *
              color rgba(29, 29, 31, 1) !important
    .pay-info
      width 372px
      margin-top 35px
      padding-bottom 60px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        margin-top calc(30px * 1.48) !important
        width calc(203px * 1.48) !important
      .left
        width 100%
        text-align center
        margin 0 auto
        :deep(.price)
          color var(--payinfo-color)
          font-size 28px
          margin-bottom 20px
          min-height 80px
          span
            font-size 80px
            line-height 85px
            color var(--payinfo-color)
        h3
          font-size var(--font-size-large)
          margin-bottom 6px
          white-space nowrap
        p
          color var(--text-tertiary-color)
          font-size var(--font-size-extra-small)
          white-space nowrap
      .code
        width 180px
        height 180px
        background #fff
        margin 20px auto 0
        overflow hidden
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(134px * 1.48) !important
          height calc(134px * 1.48) !important
          border-radius calc(4px * 1.48) !important
        img
          width 170px
          height 170px
          margin 5px
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            width calc(124px * 1.48) !important
            height calc(124px * 1.48) !important
            margin 8px auto !important

.vip
  .username
    color: #E3AB5D;
  .desc
    background-image url(https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png)
</style>

