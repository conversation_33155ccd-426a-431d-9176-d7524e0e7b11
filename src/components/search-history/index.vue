<template>
  <div class="search-history" v-show="isShow">
    <div class="search-history-title">
      <span>搜索历史</span>
      <!-- <DynamicIcon @click="handleDeleteSearchCache(-1)" name="delete" /> -->
      <div class="svg-icon" @click="handleDeleteSearchCacheConfirm">
        <svg v-show="themeClass === 'themeLight'" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.4">
          <path d="M7.25391 1.06641C7.25391 0.514122 7.70162 0.0664062 8.25391 0.0664062H11.7441C12.2964 0.0664062 12.7441 0.514122 12.7441 1.06641C12.7441 1.61869 12.2964 2.06641 11.7441 2.06641H8.2539C7.70162 2.06641 7.25391 1.61869 7.25391 1.06641Z" fill="#1D1D1F"/>
          <path d="M1.33301 5C1.33301 4.44772 1.78072 4 2.33301 4H17.6663C18.2186 4 18.6663 4.44772 18.6663 5C18.6663 5.55228 18.2186 6 17.6663 6H2.33301C1.78072 6 1.33301 5.55228 1.33301 5Z" fill="#1D1D1F"/>
          <path d="M4.59578 5.85913C4.54647 5.49752 4.51893 5.28917 4.5113 5.13653C4.50899 5.09031 4.50934 5.0602 4.5101 5.04257C4.51441 5.0363 4.51943 5.03055 4.52506 5.02544C4.54243 5.0223 4.57221 5.01789 4.61832 5.01393C4.77059 5.00086 4.98075 5 5.3457 5H14.6536C15.0186 5 15.2288 5.00086 15.381 5.01393C15.4271 5.01789 15.4569 5.0223 15.4743 5.02544C15.4799 5.03055 15.4849 5.0363 15.4892 5.04257C15.49 5.0602 15.4904 5.09031 15.488 5.13653C15.4804 5.28917 15.4529 5.49752 15.4036 5.85912L13.8827 17.0121C13.8439 17.2965 13.8218 17.454 13.7973 17.5677C13.7902 17.6007 13.7844 17.6219 13.7807 17.6343C13.7755 17.6401 13.7695 17.6453 13.7631 17.6497C13.7503 17.6517 13.7285 17.6545 13.6949 17.6571C13.5789 17.666 13.4198 17.6667 13.1328 17.6667H6.86656C6.57952 17.6667 6.42046 17.666 6.30445 17.6571C6.27081 17.6545 6.24901 17.6517 6.2363 17.6497C6.22982 17.6453 6.22389 17.6401 6.21864 17.6342C6.21494 17.6219 6.20919 17.6007 6.20209 17.5677C6.17759 17.454 6.15542 17.2965 6.11664 17.0121L4.59578 5.85913Z" stroke="#1D1D1F" stroke-width="2"/>
          </g>
        </svg>

        <svg v-show="themeClass === 'themeDark'" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.4">
          <path d="M7.25391 1.06641C7.25391 0.514122 7.70162 0.0664062 8.25391 0.0664062H11.7441C12.2964 0.0664062 12.7441 0.514122 12.7441 1.06641C12.7441 1.61869 12.2964 2.06641 11.7441 2.06641H8.2539C7.70162 2.06641 7.25391 1.61869 7.25391 1.06641Z" fill="white"/>
          <path d="M1.33301 5C1.33301 4.44772 1.78072 4 2.33301 4H17.6663C18.2186 4 18.6663 4.44772 18.6663 5C18.6663 5.55228 18.2186 6 17.6663 6H2.33301C1.78072 6 1.33301 5.55228 1.33301 5Z" fill="white"/>
          <path d="M4.59578 5.85913C4.54647 5.49752 4.51893 5.28917 4.5113 5.13653C4.50899 5.09031 4.50934 5.0602 4.5101 5.04257C4.51441 5.0363 4.51943 5.03055 4.52506 5.02544C4.54243 5.0223 4.57221 5.01789 4.61832 5.01393C4.77059 5.00086 4.98075 5 5.3457 5H14.6536C15.0186 5 15.2288 5.00086 15.381 5.01393C15.4271 5.01789 15.4569 5.0223 15.4743 5.02544C15.4799 5.03055 15.4849 5.0363 15.4892 5.04257C15.49 5.0602 15.4904 5.09031 15.488 5.13653C15.4804 5.28917 15.4529 5.49752 15.4036 5.85912L13.8827 17.0121C13.8439 17.2965 13.8218 17.454 13.7973 17.5677C13.7902 17.6007 13.7844 17.6219 13.7807 17.6343C13.7755 17.6401 13.7695 17.6453 13.7631 17.6497C13.7503 17.6517 13.7285 17.6545 13.6949 17.6571C13.5789 17.666 13.4198 17.6667 13.1328 17.6667H6.86656C6.57952 17.6667 6.42046 17.666 6.30445 17.6571C6.27081 17.6545 6.24901 17.6517 6.2363 17.6497C6.22982 17.6453 6.22389 17.6401 6.21864 17.6342C6.21494 17.6219 6.20919 17.6007 6.20209 17.5677C6.17759 17.454 6.15542 17.2965 6.11664 17.0121L4.59578 5.85913Z" stroke="white" stroke-width="2"/>
          </g>
        </svg>

      </div>
    </div>
    <div class="search-history-content" ref="contentRef" :class="{ 'collapsed': !isExpanded, 'mvhistory': props.from === 'mv' }">
      <div
        class="search-history-content-item"
        :class="{
          'search-history-content-item-active': false,
        }"
        :key="index"
        @click="handleClickSearchTab(searchItem)"
        v-for="(searchItem, index) in searchCacheList"
      >
        <span>{{ searchItem }}</span>
        <DynamicIcon @click.stop="handleDeleteSearchCache(searchItem, index)" name="close" />
      </div>
    </div>
    <div v-if="showToggle" class="search-history-toggle" @click="toggleExpand">
      <div class="toggle-container">
        <div class="line"></div>
        <div class="toggle-button">
          <span>{{ isExpanded ? '收起' : '展开' }}</span>
          <DynamicIcon :class="{ 'rotate-180': isExpanded }" name="ic_down" />
        </div>
        <div class="line"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps, defineEmits, onBeforeUnmount, ref, onMounted, nextTick, watch } from 'vue'
import { useStore } from 'vuex'
import useOrder from '@/composables/useOrder'
import useSongItem from '@/composables/useSongItem'
import { setSearchCache } from '@/utils/historyCache'
import { sendLog } from '@/directives/v-log/log'
import { Dialog } from 'vant'

const fromEventMapping = {
  search: {
    event_name: 6002,
    str1: '搜索页'
  },
  mv: {
    event_name: 6007,
    str1: '欢唱页',
  },
};
const contentRef = ref(null)
const isExpanded = ref(false)
const showToggle = ref(false)
let ROW_HEIGHT = 53 // 70px height + 24px margin-bottom
const MAX_ROWS = 3

const props = defineProps({
  from: {
    type: String,
    default: 'search'
  }
})

const emit = defineEmits(['click-word', 'delete-word'])

const store = useStore()
const { orderedListNumber, orderSong, addSong } = useOrder()
const { validSong } = useSongItem()

const searchCacheList = computed(() => store.state.search.searchCache)
const searchSongList = computed(() => store.state.search.searchSong)
const themeClass = computed(() => store.state.themeClass)
const searchSong = computed(() => {
  let res = {}
  searchSongList.value.some((v) => {
    if (searchCacheList.value.includes(v.searchname)) {
      res = v
      return true
    }
    return false
  })
  return res
})
const isShow = computed(() => !!searchCacheList.value.length)

const isCanOrder = (song) => {
  if (!song || !Object.keys(song).length || !song.songid) {
    return false
  }
  return validSong(song)
}

const goSing = (song) => {
  if (isCanOrder(song)) {
    addSong(song, {
      from: {
        song_list_source: 5,
      },
    })
    orderSong(song, orderedListNumber.value - 1)
  }
}

const handleGoSing = (item) => {
  if (searchSong.value.searchname === item) {
    goSing(searchSong.value)
  }
}

const handleClickSearchTab = (item) => {
  emit('click-word', item)

  const { event_name = '', str1 = ''} = fromEventMapping[props.from]
  sendLog({
    event_type: 'click',
    event_name,
    event_data: {
      str1,
      str2: '搜索历史',
      str3: '点击任意歌曲/内容',
      str4: 'click',
    }
  })
}

// 新增删除搜索历史二次确认
const handleDeleteSearchCacheConfirm = () => {
  Dialog.confirm({
    className: 'search-cache-delete-confirm',
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
    title: '您确定清空搜索历史记录?',
    // message: ''
  }).then(() => {
    handleDeleteSearchCache(-1)
  })
}

const handleDeleteSearchCache = (searchItem, index) => {
  const { event_name = '', str1 = ''} = fromEventMapping[props.from]

  if (searchItem === -1) {
    store.dispatch('search/clearSearchCache')
    emit('delete-word', searchItem)
    sendLog({
      event_type: 'click',
      event_name,
      event_data: {
        str1,
        str2: '搜索历史',
        str3: '全部删除',
        str4: 'click',
      }
    })
    return
  }
  let newSearchCacheList = [...searchCacheList.value]
  newSearchCacheList.splice(index, 1)
  store.dispatch('search/updateSearchCache', newSearchCacheList)
  store.dispatch('search/deleteSearchSong', searchItem)
  setSearchCache(newSearchCacheList)
  emit('delete-word', searchItem)
  
  sendLog({
    event_type: 'click',
    event_name,
    event_data: {
      str1,
      str2: '搜索历史',
      str3: '删除任意歌曲/内容',
      str4: 'click',
    }
  })
}

const checkContentHeight = async () => {
  await nextTick()
  if (!contentRef.value) {
    return
  }
  showToggle.value = false
  if (window.innerWidth > 400 && props.from == 'search') {
    return;
  }
  if (window.innerWidth > 1200 && props.from == 'mv') {
    return;
  }
  // 设置高度
  if (window.innerWidth == 818) {
    ROW_HEIGHT = 56
  }
  const contentHeight = contentRef.value.scrollHeight
  const maxHeight = ROW_HEIGHT * MAX_ROWS
  showToggle.value = contentHeight > maxHeight
  if (!showToggle.value) {
    isExpanded.value = false
  }
}

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}
const resizeObserver = new ResizeObserver(() => {
  checkContentHeight();
});

onMounted(() => {
  resizeObserver.observe(document.body);
  checkContentHeight()
})
// 清理观察者
onBeforeUnmount(() => {
  resizeObserver.disconnect();
});
// Watch for changes in search cache list to recheck height
watch(() => searchCacheList.value, () => {
  checkContentHeight()
})
</script>

<style lang="stylus" scoped>
.search-history
  width 100%
  margin-top 0px
  &-title
    width 100%
    // height 33px
    display flex
    flex-direction row
    align-items center

    // 三分之一屏 & 三分之二屏
    @media (max-width: 900px)
      padding-top calc(16px * var(--scale)) !important
    *
      color var(--text-secondary-color)
        
    span
      margin-right 0px
      font-size var(--font-size-large)
    .svg-icon
      width calc(20px * var(--scale))
      height calc(20px * var(--scale))
      margin-left calc(8px * var(--scale))

      svg 
        width 100%
        height 100%

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(20px * var(--scale)) !important
        height calc(20px * var(--scale)) !important
  &-content
    width 100%
    margin-top 40px
    display flex
    flex-direction row
    flex-wrap wrap
    &.collapsed
      overflow hidden
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        max-height 380px!important
      @media (min-width: 1200px) and (max-height: 450px)
        max-height 450px!important
      @media (max-width: 700px)
        max-height 450px!important
    &.mvhistory
      max-height fit-content!important
    &-item
      max-width 700px
      height 70px
      display flex
      align-items center
      justify-content space-between
      border-radius: 8px;
      background: var(--card-bg-color2)
      padding 0 24px
      margin-right 40px
      margin-bottom 24px
      
      // 三分之一屏 & 三分之二屏
      @media (max-width: 900px)
        height calc(45px * var(--scale)) !important
        border-radius calc(8px * var(--scale)) !important
        padding 0 calc(16px * var(--scale)) !important

      span
        max-width 447px
        height 74px
        line-height 74px
        color var(--text-tertiary-color)
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        margin-right 24px
      .svg-icon
        width 24px
        height 24px

        // 三分之一屏 & 三分之二屏
        @media (max-width: 900px)
          width calc(16px * var(--scale)) !important
          height calc(16px * var(--scale)) !important
        // 三分之一屏
        @media (max-width: 700px)
          width calc(16px * 3)
          height calc(16px * 3)
  &-toggle
    display none
    width 100%
    display flex
    align-items center
    justify-content center
    padding 12px 0
    cursor pointer
    margin-top 32px
    // 三分之一屏
    @media (max-width: 700px)
      display block
    .toggle-container
      display flex
      align-items center
      width 100%
      .line
        flex 1
        height 1px
        background var(--card-bg-color2)
      .toggle-button
        width 210px
        height 84px
        display flex
        align-items center
        padding 0 12px
        background var(--card-bg-color2)
        justify-content center
        border-radius 24px
        margin 0 54px
        span
          color var(--text-secondary-color)
          margin-right 4px
          font-size var(--font-size-small)
        .svg-icon
          width 48px
          height 48px
          color var(--text-secondary-color)
          transform rotate(180deg)
          &.rotate-180
            transform rotate(0)

</style>

<style lang="stylus">
.search-cache-delete-confirm
  background rgba(232, 234, 238, 1) !important
  .van-dialog__header
    color rgba(29, 29, 31, 0.8) !important
    // font-size 20px !important
  .van-dialog__footer
    flex-direction initial !important
  .van-dialog__footer
    .van-button
      &:nth-child(2)
        background transparent !important
        border 1px solid rgba(29, 29, 31, 0.1) !important
        backdrop-filter blur(133px) !important
        margin-left 26px !important
        .van-button__text
          color rgba(29, 29, 31, 0.8)!important
      &:nth-child(1)
        background rgba(255, 255, 255, 1) !important
        border none !important
        backdrop-filter blur(133px) !important
        .van-button__text
          color rgba(225, 61, 61, 1)!important
</style>