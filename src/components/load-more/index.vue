<template>
  <div
    ref="root"
    class="loadmore"
    :style="{
      height: calcHeight
    }"
  >
  <!-- @scroll="handleScroll" -->
    <slot name="default"></slot>
    <div ref="triggerTarget" class="loadmore-trigger"></div>
    <p v-if="isEnd" class="is-end">到底啦～</p>
  </div>
</template>
<script>
import { ref, onBeforeUnmount, onMounted, computed, toRefs, nextTick } from 'vue'

export default {
  name: 'LoadMore',
  props: {
    safeAreaHeight: {
      type: String,
      default: '13.6991vw',
    },
    isEnd: {
      type: Boolean,
      default: false,
    }
  },
  setup(props, { emit }) {
    
    // const route = useRoute()
    const { safeAreaHeight } = toRefs(props)

    let root = ref(null)
    let triggerTarget = ref(null)
    let observer = ref({})

    const calcHeight = computed(() => {
      if (safeAreaHeight.value.includes('calc')) {
        return safeAreaHeight.value
      }
      return '100%'
    })

    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5
    }
    const init = () => {
      options.root = root.value
      observer.value = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            console.log('load-more')
            emit('load-more')
          }
        });
      }, options);
      observer.value.observe(triggerTarget.value)
    }

    // 也可以在这里实时记录
    // const handleScroll = _.debounce((e) => {
    //   store.commit(
    //     'UPDATE_PAGE_CACHEDATA',
    //     {
    //       data: {
    //         position: e.target.scrollTop || 0
    //       },
    //       type: route.name
    //     }
    //   )
    // },200)

    const destory = () => {
      observer.value = null
      // root.value.removeEventListener('scroll', handleScroll)
    }

    onMounted(async () => {
      await nextTick()
      // root.value.addEventListener('scroll', handleScroll) // 也可以换成监听
      init()
    })

    onBeforeUnmount(destory)

    return {
      root,
      triggerTarget,
      calcHeight,
      // handleScroll,
    }
  },
}
</script>
<style lang="stylus" scoped>
#triggerTarget
  height 10px
.loadmore
  overflow-y: scroll
  padding-bottom 250px
  
  // 三分之一屏
  @media (max-width: 700px)
    padding-bottom calc(220px * 3)
    
  &::-webkit-scrollbar-track
    background: #000000
  &-trigger
    height 10px
.is-end
  font-size 24px
  margin 10px 0
  text-align center
  color var(--text-secondary-color)
</style>