<template>
  <CommonTeleportModal @cancel="close">
    <div class="top-fans-modal" v-show="isShow">
      <div class="top-fans-modal-content">
        <img class="top-fans-modal-content-close" @click="close" src="https://qncweb.ktvsky.com/20230804/vadd/9f611b95ed1ac8122957f683c69e34bf.png" >
        <p class="top-fans-modal-content-title">演唱最多的人，即可成为头号粉丝</p>
        <div class="top-fans-modal-content-main">
          <div class="top-fans-modal-content-main-item">
            <div class="top-fans-modal-content-main-item-header">
              <img class="top-fans-modal-content-main-item-header-img" :src="topfans.img" v-img-fallback="imgUserFallback" alt="">
              <div class="top-fans-modal-content-main-item-header-name">
                <p>{{ topfans.name }}</p>
                <span>头号粉丝</span>
              </div>
            </div>
            <div>
              <p class="top-fans-modal-content-main-item-title">演唱曲数/首</p>
              <p class="top-fans-modal-content-main-item-num1">{{ topfans.count }}</p>
            </div>
          </div>
          <div v-if="isLogin" class="top-fans-modal-content-main-diver"></div>
          <div v-if="isLogin" class="top-fans-modal-content-main-item">
            <div class="top-fans-modal-content-main-item-header">
              <img class="top-fans-modal-content-main-item-header-img" :src="avatar" v-img-fallback="imgUserFallback" alt="">
              <div class="top-fans-modal-content-main-item-header-name">
                <p>我</p>
              </div>
            </div>
            <div>
              <p class="top-fans-modal-content-main-item-title">演唱曲数/首</p>
              <p class="top-fans-modal-content-main-item-num2">{{ userCount }}</p>
            </div>
          </div>
        </div>
        <p class="top-fans-modal-content-bot">头号粉丝展示于次日4点更新，歌曲数以每首歌完整播放为准</p>
      </div>
    </div>
  </CommonTeleportModal>
</template>

<script>
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import CommonTeleportModal from './../common/index.vue'

export default {
  name: 'TopFansModal',
  components: {
    CommonTeleportModal
  },
  props: {
    topfans: {
      type: Object,
      default: () => {
        return {
          img: '',
          name: '',
          count: 0
        }
      }
    },
    userCount: {
      type: Number,
      default: 0
    }
  },
  setup(props, { emit }) {
    const imgUserFallback = {
      loading: 'https://qncweb.ktvsky.com/20230803/vadd/004a58b7a4169842eb4f4bfaa23d277d.png',
      error: 'https://qncweb.ktvsky.com/20230803/vadd/004a58b7a4169842eb4f4bfaa23d277d.png'
    }
    const store = useStore()
    const avatar = computed(() => store.state.userInfo.avatar)
    const isLogin = computed(() => !!store.state.userInfo.unionid)

    let isShow = ref(true)

    const show = () => {
      isShow.value = true
    }

    const close = () => {
      isShow.value = false
      emit('close')
    }

    return {
      imgUserFallback,
      isShow,
      avatar,
      isLogin,
      show,
      close,
    }
  }
}
</script>

<style lang="stylus" scoped>
.top-fans-modal
  z-index 99
  &-content
    width 1000px
    height 580px
    position relative
    overflow hidden
    border-radius 20px
    background rgba(30, 31, 33, 1)
    &-close
      position absolute
      top 30px
      left 30px
      width 90px
      height 90px
    &-title
      color rgba(255, 255, 255, 0.7)
      font-size 36px
      text-align center
      margin-top 48px
      height 54px
      line-height 54px
      font-weight 400
    &-main
      width 1000px
      height 308px
      margin-top 68px
      display flex
      flex-direction column
      align-items center
      justify-content center
      &-item
        width 760px
        height 110px
        display flex
        align-items center
        justify-content space-between
        &-header
          width 321px
          height 110px
          display flex
          align-items center
          &-img
            width 110px
            height 110px
            border-radius 50%
          &-name
            margin-left 16px
            p
              color rgba(255, 255, 255, 0.7)
              font-size 28px
              height 32px
              line-height 32px
              max-width 200px
              white-space nowrap
              overflow hidden
              text-overflow ellipsis
            span
              color #F0D290
              font-size 20px
              margin-top 16px
        &-title
          color rgba(255, 255, 255, 0.50)
          font-size 28px
          height 32px
          line-height 32px
          margin-bottom 20px
          text-align right
        &-num1
          color #F0D290
          font-size 48px
          height 57px
          line-height 57px
          font-weight bolder
          margin-bottom 0
          text-align right
        &-num2
          color rgba(255, 255, 255, 0.80)
          font-size 36px
          height 43px
          line-height 43px
          font-weight bolder
          margin-bottom 0
          text-align right
      &-diver
        width 760px
        height 1px
        background rgba(255, 255, 255, 0.1)
        margin 40px 0
    &-bot
      position absolute
      bottom 30px
      left 0
      text-align center
      width 1000px
      height 39px
      font-size 26px
      line-height 39px
      color rgba(255, 255, 255, 0.2)
</style>