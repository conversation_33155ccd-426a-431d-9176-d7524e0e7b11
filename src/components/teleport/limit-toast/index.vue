<template>
  <transition>
    <div class="thunder-teleport-limit-overlay">
      <div class="thunder-teleport-limit-background"></div>
      <div class="thunder-teleport-limit-content">
        <h1>提示</h1>
        <p>检测到当前为非法设备，无法使用雷石 KTV</p>
        <div class="thunder-teleport-limit-content-btn" @click.stop="$emit('exit')">我知道了</div>
        <div class="tip" @click="handleShowMacinfoModal">当前设备的MAC地址二维码</div>
      </div>
    </div>
  </transition>
</template>

<script>
import { ref, computed, toRefs } from 'vue'
import { useStore } from 'vuex';
import useCommonModal from '@/components/modal/global/mic-online/create.js'

export default {
  name: 'ActivityRenewVipModal',
  props: {
    limitParams: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const store = useStore();
    const { limitParams } = toRefs(props)

    let CommonModalInstance = ref(null)
    const _src = computed(() => store.state.system.systemInfo.src)
    const $useMicOnline = useCommonModal()

    const handleShowMacinfoModal = () => {
      CommonModalInstance.value = $useMicOnline.show({
        info: {
          qr: `src=${_src.value}&mac_id=${limitParams.value.mac_id}`,
          content: '请拍照联系雷石相关人员',
        },
        title: '设备信息',
      })
    }
    
    return {
      handleShowMacinfoModal
    }
  }
}
</script>

<style lang="stylus" scoped>
.thunder-teleport-limit-overlay
  position fixed
  bottom 0
  left 0
  right 0
  top 0
  display flex
  align-items center
  justify-content center
  overflow hidden
  z-index 9999
  .thunder-teleport-limit-background
    position absolute
    bottom 0
    left 0
    right 0
    top 0
    background rgba(0, 0, 0, 1)
  .thunder-teleport-limit-content
    position relative
    width 880px
    height 442px
    border-radius 8px
    background rgba(30, 31, 33, 1)
    display flex
    flex-direction column
    align-items center
    h1
      font-size 36px
      color rgba(255, 255, 255, 0.8)
      font-weight 400
      margin 60px 0 50px
    p
      font-size 32px
      color rgba(255, 255, 255, 0.5)
      font-weight 400
      margin-bottom 50px
    &-btn
      width 300px
      height 80px
      background rgba(255, 255, 255, 0.1)
      display flex
      align-items center
      justify-content center
      font-size 28px
      color rgba(255, 255, 255, 0.8)
      font-weight 400
    .tip
      color rgba(255, 255, 255, 0.4)
      font-size 26px
      margin-top 30px
      padding-right 30px
      background url('https://qncweb.ktvsky.com/20240330/other/c5487be1f43ba501ec89a507fb59c2ca.png') no-repeat right center
      background-size 24px auto
.theme-themeLight
  .thunder-teleport-limit-overlay
    .thunder-teleport-limit-content
      background linear-gradient(180deg, #FFFFFF 0%, #E1E5EE 38.12%);
      h1
        color rgba(29, 29, 31, 0.8)
      p
        color rgba(29, 29, 31, 0.5)
      &-btn
        border 2px solid rgba(29, 29, 31, 0.1)
        color rgba(29, 29, 31, 0.8)
      .tip
        color rgba(29, 29, 31, 0.5)
        background-image url('https://qncweb.ktvsky.com/20240329/other/a4c5989660b4dfa886e2486563581349.png')
</style>