<template>
  <div class="use-tip" v-show="!route.name.includes('agreement')">
    <div class="content">
      <div class="content-title">使用提示</div>
      <div class="tip">
        在使用雷石KTV服务前，请您务必仔细阅读
        <a @click.stop="handleCheckAgreement('agreementUser')" class="privacy_link">《雷石KTV用户协议》</a>
        和
        <a @click.stop="handleCheckAgreement('agreementPrivacy')" class="privacy_link">《雷石KTV隐私权政策》</a>
        。继续使用服务即表示您同意上述文件中的条款。
      </div>
      <div class="buttons-container">
        <div @click.stop="handleAccept" class="btn_accept">已阅读且同意</div>
        <div @click.stop="handleCancel" class="btn_decline">退出应用</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import store2 from 'store2';
// import store from '@/store'
import { TSNativeInstance } from '@/packages/TSJsbridge';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex';

const router = useRouter()
const store = useStore()
const route = computed(() => router.currentRoute.value)

const handleCancel = () => {
  console.log('handleCancel')
  TSNativeInstance.exit()
}

const handleAccept = () => {
  store.commit('YS_TIP_ACCEPT', true)
  store2('storageYSTipAccept', true);
  console.log('handleAccept')
}

const handleCheckAgreement = (name) => {
  console.log('handleCheckAgreement:' + name)
  router.push({
    name
  })
}
</script>

<style lang="stylus" scoped>
.dark-theme
  .use-tip
    --background: #22202C
    --accept-background: #FFFFFF1A
.use-tip {
  --background: #E8EAEE
  --accept-background: #fff

  width 100vw
  height 100vh
  background rgba(0, 0, 0, 0.8)
  display flex
  align-items center
  justify-content center
  position fixed
  left 0
  top 0
  z-index 999999

  .content {
    width 880px
    height 504px
    display flex
    align-items center
    justify-content center
    flex-direction column
    border-radius: 42px;
    background: var(--background);
    padding 0 100px

    &-title {
      // color: rgba(255, 255, 255, 0.80);
      font-size: 36px;
      height auto
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  {
        font-size: var(--font-size-extra-large) !important
      }
    }

    .tip {
      // color: rgba(255, 255, 255, 0.50);
      font-size: 27px;
      margin-top 45px;
      line-height 50px
      color var(--text-secondary-color)
    }

    .privacy_link {
      color: #DBAE6A;
      text-decoration underline
    }

    .buttons-container {
      display: flex;
    }

    .btn_accept {
      margin-top 60px
      display: flex;
      width: 300px;
      height: 80px;
      margin-right 30px
      justify-content: center;
      align-items: center;
      border-radius: 24px;
      background: var(--accept-background);
      backdrop-filter: blur(100px);
      // color: black;
      font-size: 27px;
    }

    .btn_decline {
      margin-top 60px
      display: flex;
      width: 300px;
      height: 80px;
      justify-content: center;
      align-items: center;
      border-radius: 24px;
      background: none;
      backdrop-filter: blur(100px);
      // color: rgba(255, 255, 255, 0.80);
      font-size: 27px;
      border 2px solid var(--border-color)
    }
  }
}
.theme-themeLight
  .use-tip
    background: #000
    .content
      background: linear-gradient(180deg, #E2E5ED 0%, #E1E5EE 38.12%);
      &-title {
        color: rgba(29, 29, 31, 0.9)
      }
      .tip {
        color: rgba(29, 29, 31, 0.8)
      }
      .btn_accept {
        background: rgba(29, 29, 31, 0.8)
        color: #E2E5ED;
      }
      .btn_decline {
        background: rgba(255, 255, 255, 0.6)
        color: rgba(29, 29, 31, 0.6);
      }
@media (max-width: 700px)
    .content 
      width 704px!important
      height 600px!important
    .btn_accept
      width 256px!important
      height 100px!important
    .btn_decline
      width 256px!important
      height 100px!important
</style>