<template>
  <div class="service-licence">
    <div>版本号：v{{finalVn}}</div>
    <div>
      <a @click="handleGoLicence" class="flex-center">
        备案号：{{ serviceLicenceInfo.serviceLicence }}
        <DynamicIcon class="svg-icon" name="right-arrow" />
      </a>
    </div>
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import { getLicence } from '@/service/base';
import config from '@/config'
import { TSBaseInfoInstance, TSNativeInstance } from '@/packages/TSJsbridge'

const store = useStore();
const { vn } = config;

const serviceLicenceInfo = computed(() => store.state.serviceLicenceInfo);

const finalVn = computed(() => {
  // 版本号规则：安卓版本号-前端版本号（01开始）
  const _vn = TSNativeInstance.getParams('_vn') || '1.07';
  return _vn.replace(/v/g, '') + '-' + vn;
});

const getAndSaveLicenceInfo = async () => {
  const { data = {} } = await getLicence();
  store.commit('SAVE_SERVICE_INFO', data);
};

onMounted(() => {
  if (!serviceLicenceInfo.value.serviceUrl) {
    getAndSaveLicenceInfo();
  }
});

const handleGoLicence = () => {
  TSBaseInfoInstance.goToExternalPage(serviceLicenceInfo.value.serviceUrl);
};
</script>

<style lang="stylus" scoped>
.service-licence
  font-size: var(--font-size-small)
  opacity 0.6
  // color: rgba(255, 255, 255, 0.40);
  text-align center
  margin 200px 0
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    font-size calc(12px * 1.48) !important
    margin  calc(21px * 1.48) 0 calc(70px * 1.48) 0 !important
  // 三分之一屏
  @media (max-width: 700px)
    margin calc(24px * 3) 0 calc(120px * 3)
  a
    margin 0 auto
    padding-right 60px
    // background url('https://qncweb.ktvsky.com/20240103/other/7cc982caaff9791d87656fa3bbab7a92.svg') no-repeat right center
    background-size 50px 50px

    .svg-icon
      width 26px
      height 26px

      // 三分之一屏
      @media (max-width: 700px)
        zoom 1.5
.light-theme
  .service-licence a 
    // background url('https://qncweb.ktvsky.com/20240305/vadd/a1be2103de31f225006acf5038ae031c.png') no-repeat right center
    background-size 50px 50px
</style>
