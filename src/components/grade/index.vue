<template>
  <div
    class="grade"
    :style="{
      opacity: (mvIsHide && isShowScore) ? 1 : 0
    }"
  >
    <GradeDashboard
      :musicName="scoreSongInfo?.music_name"
      :score="currentScoreWord.score"
    />
    <div class="grade-divide"></div>
    <template v-if="scoreData.gradeFragmentList?.length">
      <GradeIndicator
        ref="gradeIndicatorCom"
        :duration="scoreData.duration"
        :items="scoreData.gradeFragmentList"
        :position="position"
        :currentScoreWord="currentScoreWord"
        @animate-error="handleAnimateError"
      ></GradeIndicator>
    </template>
  </div>
</template>
<script>
import GradeDashboard from './dashboard.vue';
import GradeIndicator from './indicator.vue';
import { ref, onMounted, toRefs, watch, computed, onUnmounted } from 'vue';
import { TSWebEventInstance } from '@/packages/TSJsbridge/TSWebEvent';
import { isInteger } from '@/packages/TSJsbridge/TSStorage/utils';
import useScore from '@/composables/useScore';
import { useStore } from 'vuex';
import toneRes from './mock';
import eventBus from '@/utils/event-bus';

export default {
  name: 'Grade',
  components: {
    GradeIndicator,
    GradeDashboard,
  },
  setup(props, { emit }) {
    const store = useStore()
    const { musicName } = toRefs(props);
    const { isShowScore, setScoreResult, showGradeResultModal } = useScore()

    const mvIsHide = computed(()=> !store.state.mvIsHide)
    const scoreData = computed(()=> store.state.score.scoreData)
    const scoreSongInfo = computed(()=> store.state.score.scoreSongInfo)

    let duration = ref(0);
    let position = ref(0);
    let gradeIndicatorCom = ref(null);

    let currentScoreWord = ref({
      score: 0,
    });

    const handleScoreStart = (payload, istest) => {
      try {
        const res = istest ? payload : JSON.parse(payload);
        const gradeFragmentList = res.tone_list.slice(1, -1);
        const fistFrontStartTime = gradeFragmentList[0].startTime;
        const duration = res.duration;

        // 调用 Vuex 的 mutation 来更新 score 模块的状态
        store.commit('score/SET_SCORE_DATA', {
          gradeFragmentList,
          fistFrontStartTime,
          duration
        });

        console.log(
          'handleScoreStart',
          `${gradeFragmentList.length}段`,
          `${duration / 1000 / 60} 分`,
          res.tone_list
        );
      } catch (error) {
        console.log('handleScoreStart error', error);
      }
    };

    const handleScore = (payload) => {
      try {
        const res = JSON.parse(payload);
        console.log('handleScore', res);
      } catch (error) {
        console.log('handleScore error', error);
      }
    };

    // 上报当前用户的总得分
    const handleCurrentScore = (payload) => {
      setScoreResult({
        name: musicName.value,
        score: Number(payload),
      })
    }

    // 上报当前MV播放的进度
    const handleTimeupdate = (payload) => {
      position.value = Number(payload)
    }

    const handleScoreStop = async (payload) => {
      console.log('handleScoreStop', payload, scoreSongInfo.value);
      try {
        const res = JSON.parse(payload);
        const score = isInteger(res.score.toFixed(2))
          ? Math.floor(res.score)
          : res.score.toFixed(2);
        
        await setScoreResult({
          name: scoreSongInfo.value.music_name,
          score,
        })
        showGradeResultModal();

        store.commit('score/SET_SCORE_DATA', {});
      } catch (error) {
        console.log('handleScoreStop error', error);
      }
    };

    const handleScoreError = (payload) => {
      try {
        const res = JSON.parse(payload);
        console.log('handleScoreError', res);
      } catch (error) {
        console.log('handleScoreError error', error);
      }
    };

    const handleAnimateError = () => {
      emit('animate-error')
    }

    const handleScoreWord = (start, end, pitch, score, isCorrect) => {
      console.log('handleScoreWord:', isCorrect)
      currentScoreWord.value = {
        start,
        end,
        pitch,
        score,
        isCorrect,
      };
    };

    const handleAttachEvent = () => {
      TSWebEventInstance.on('handleScoreStart', handleScoreStart);
      TSWebEventInstance.on('handleScore', handleScore);
      TSWebEventInstance.on('handleCurrentScore', handleCurrentScore);
      TSWebEventInstance.on('handleScoreStop', handleScoreStop);
      TSWebEventInstance.on('handleScoreError', handleScoreError);
      TSWebEventInstance.on('handleScoreWord', handleScoreWord);

      eventBus.on('handleTimeupdate', handleTimeupdate)

      // 模拟测试打分条动画
      // handleScoreStart(toneRes, true)
      // let startTime = 30 * 1000

      // setInterval(() => {
      //   startTime += 1000
        // handleScorePosition(startTime)
      // }, 1000)
      // 模拟测试打分条动画-end

    };

    const detachIrcPlayerEvents = () => {
      eventBus.off('handleTimeupdate', handleTimeupdate)
    }

    onMounted(handleAttachEvent);

    onUnmounted(() => {
      detachIrcPlayerEvents()
    })

    return {
      gradeIndicatorCom,
      duration,
      position,
      handleAnimateError,
      currentScoreWord,
      scoreSongInfo,
      mvIsHide,
      isShowScore,
      scoreData,
    };
  },
};
</script>
<style lang="stylus" scoped>
.grade {
  display: flex;
  align-items: center;
  width: 1300px;
  height: 130px;
  background: rgba(30,31,33,0.7)
  border-radius: 14px;
  justify-content: space-between;
  padding: 0 40px;
  position: fixed;
  top: 40px;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 11;
  pointer-events: none;

  &-divide {
    width: 2px;
    height: 80px;
    opacity: 0.15;
    background: #FFFFFF;
    border-radius: 4px;
    margin: 0 40px;
  }
}
</style>