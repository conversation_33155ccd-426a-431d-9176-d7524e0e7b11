<template>
  <div class="sing-scoring-box">
    <div
      class="sing-scoring-box-line"
      v-for="line in filteredLineList"
      :key="line.showT"
      :style="{
        right: line.x + 'px',
        width: line.w + 'px',
        bottom: line.y + 'px',
        'animation-delay': line.delay + 'ms',
        'animation-play-state': videoPaused ? 'paused' : 'running'
      }"
    >
      <div :style="{width: (line.showLineScore ? line.w : 0) + 'px', 'transition-duration': line.duration + 'ms'}" class="lineScore"></div>
    </div>
    <div :style="{ transform: 'translateY(-' + ballY + 'px' + ')' }" class="sing-scoring-box-ball"></div>
  </div>
</template>

<script>
import { computed, ref, toRefs, onBeforeMount, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import { TSWebEventInstance } from '@/packages/TSJsbridge';

export default {
  name: 'SingScoring',
  props: {
    items: Array,
    positionKey: String,
    duration: Number,
    position: Number,
    currentScoreWord: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const { items, position, currentScoreWord } = toRefs(props);
    const store = useStore();

    const videoPaused = computed(() => store.state.videoPaused);
    const screenWidth = window.innerWidth;
    const rate = screenWidth / 1920;
    const boxWidth = 1300 * rate;
    const animaTime = 4000;
    const speed = boxWidth / animaTime;
    const scoreInitData = ref([]);

    const ballY = ref(0);
    const leftBoxOpacity = ref(0);

    const initData = () => {
      const tempList = items.value.reduce((acc, { startTime, endTime, pitch, a, b, c }) => {
        const actualStartTime = startTime || a;
        const actualEndTime = endTime || b;
        const actualPitch = pitch || c;

        if (actualStartTime > 0) {
          const duration = actualEndTime - actualStartTime;
          const w = duration * speed;
          const scoreItem = {
            x: -w,
            y: actualPitch * rate,
            w,
            showT: actualStartTime - 1100,
            duration,
            actualStartTime,
            actualEndTime,
            actualPitch,
            first: false,
            delay: 0,
            showLineScore: false
          };
          acc.push(scoreItem);
        }

        return acc;
      }, []);

      tempList.sort((a, b) => a.showT - b.showT); // 按照showT属性升序排序

      scoreInitData.value = tempList;
    };

    const filteredLineList = computed(() => {
      const s = position.value;
      const e = s - animaTime;
      return scoreInitData.value
        .filter(({ showT }) => showT >= e && showT < s)
        .map((item) => {
          if (!item.first) {
            item.first = true;
            item.delay = item.showT - s;
          }

          const withinPitchRange = currentScoreWord.value.isCorrect == 1;
          const withinTimeRange =
            currentScoreWord.value.start >= item.actualStartTime && currentScoreWord.value.end <= item.actualEndTime;
          item.showLineScore = item.showLineScore || withinPitchRange && withinTimeRange;
          
          return item;
        });
    });

    let resetBall;
    const handleScorePitch = (pitch) => {
      ballY.value = Math.min(pitch, 120) * rate;
      leftBoxOpacity.value = ballY.value / 120;
      if (resetBall) {
        clearTimeout(resetBall);
      }
      resetBall = setTimeout(() => {
        ballY.value = 0;
        leftBoxOpacity.value = 0;
      }, 300);
    };

    onBeforeMount(() => {
      initData();
    });

    onBeforeUnmount(() => {
      clearTimeout(resetBall);
    });

    TSWebEventInstance.on('handleScorePitch', handleScorePitch);

    return {
      filteredLineList,
      ballY,
      leftBoxOpacity,
      videoPaused
    };
  }
};
</script>

<style lang="stylus" scoped>
  .sing-scoring {
    position fixed
    width 1300px
    left calc((100vw - 1300px)/2)
    height 130px
    z-index 20
    border-radius: 10px;
    background: rgba(30, 31, 33, 0.70);
    backdrop-filter: blur(15px);
    top 40px
    display flex
    align-items center
    justify-content center
    .line {
      width: 2px;
      height: 100px;
      flex-shrink: 0;
      background: rgba(255, 255, 255, 0.10);
    }
    &-box {
      position relative;
      height 125px //动画高+线条高
      width 960px
      overflow hidden;
      margin-left 15px
      background-image url('https://qncweb.ktvsky.com/20240220/other/77a7061b368f3604423aa229e0b310e7.png')
      background-size auto 100%!important
      background-position 50px center !important
      background-repeat no-repeat
      &-line {
        position absolute
        height 5px
        animation scoreMoveLeft 4s linear
        background: rgba(255, 255, 255, 0.10);
        border-radius 2.5px
        overflow hidden
        .lineScore {
          height 5px
          background-color #E3AB5D
          transition-timing-function linear
          transition-property width
        }
      }
      &-ball {
        position absolute
        left 310px
        bottom 0px
        width 22px
        height 17px
        background url('https://qncweb.ktvsky.com/20240220/other/280cfbfd14c9f45ae05e8dcfd4ad8532.svg') no-repeat center right
        background-size auto 100%
        transition all ease-in-out 0.4s
        //animation scoreMoveTop 1s ease-in-out
      }
      @keyframes scoreMoveLeft {
        0% {
          transform: translateX(0);
        }
        100% {
          transform: translateX(-960px);
        }
      }
    }
  }
</style>
