<template>
  <div class="grade-dashboard">
    <div class="grade-dashboard-song">
      正在播放：{{ musicName }}
    </div>
    <div class="grade-dashboard-info">
      <div class="grade-dashboard-info-score">{{ score.toFixed(2) }}分</div>
      <div class="grade-dashboard-info-level">{{ level }}</div>
    </div>
  </div>
</template>
<script>
import { toRefs, computed } from 'vue';
import useScore from '@/composables/useScore';

export default {
  name: 'GradeDashboard',
  props: {
    musicName: String,
    score: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    const { score } = toRefs(props)
    const { getLevelAndDesc } = useScore()

    const level = computed(() => getLevelAndDesc.value(score).level)
    
    return {
      level
    }
  },
};
</script>
<style lang="stylus" scoped>
.grade-dashboard {
  width: 228px;
  color rgba(255, 255, 255, 0.8)

  &-song {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size 26px;
  }

  &-info {
    display: flex;
    align-items: center;
    justify-content space-between
    font-size: 26px;
    color rgba(255, 255, 255, 0.6)
    margin-top 15px

    img {
      width: 30px;
      height: 30px;
      margin-right: 26px;
    }

    &-score {
      margin-right: 23px;
      background url(https://qncweb.ktvsky.com/20240205/other/47afa72f1f58b1d514d637199ecdea17.png) no-repeat left center
      background-size 32px auto
      padding-left 42px
    }
  }
}
</style>