export default Object.freeze([
    {
        k: 'sss',
        min: 90,
        evo: '此音只应天上有，<br/>人生难得几回闻！',
        scoreColor: 'linear-gradient(350deg, #FFF 22.19%, #89E0FF 52.97%, #FFC0FF 84.41%)',
        exceedColor: '#51D5FF',
    },
    {
        k: 'ss',
        min: 80,
        max: 90,
        evo: '折服于你<br/>完美无缺的唱功！',
        scoreColor: 'linear-gradient(164deg, #FF1DFF 10.03%, #FFD289 44.34%, #FFF 78.9%)',
        exceedColor: '#FF72EE',
    },
    {
        k: 's',
        min: 70,
        max: 80,
        evo: '你已经成为了<br/>超越原唱的存在！',
        scoreColor: 'linear-gradient(175deg, #FFD06B 21.9%, #FFF6D9 76.01%)',
        exceedColor: '#FFC759',
    },
    {
        k: 'a',
        min: 60,
        max: 70,
        evo: '您的歌声已经<br/>达到了专业水准了',
        scoreColor: 'linear-gradient(171deg, #979ED3 24.22%, #ECF3FE 75.84%)',
        exceedColor: '#A6CBFF',
    },
    {
        k: 'b',
        min: 50,
        max: 60,
        evo: '唱的太棒了，<br/>再多来几首试试吧！',
        scoreColor: 'linear-gradient(177deg, #E6967E 24.23%, #FFE9D2 76.05%)',
        exceedColor: '#FFB094',
    },
    {
        k: 'c',
        max: 50,
        evo: '唱的太棒了，<br/>再多来几首试试吧！',
        scoreColor: 'linear-gradient(177deg, #E6967E 24.23%, #FFE9D2 76.05%)',
        exceedColor: '#FFB094',
    }
]);
