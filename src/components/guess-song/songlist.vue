<template>
  <SecContainer
    title="猜你会唱"
    class="sec-gusse-sing"
  >
    <template #header>
      <div
        v-if="!showAllData"
        class="sec-gusse-sing-change flex"
        @click.stop="handlechangePagenation"
      >
        <span>换一批</span>
        <DynamicIcon name="change" useTheme="true" />
      </div>
    </template>
    <div class="sec-gusse-sing-list">
      <RecycleScroller
        v-if="isFastList"
        class="sec-gusse-sing-list"
        :items="guessSongShowList"
        :item-size="tableItemSize"
        key-field="i"
        v-slot="{ item: songItem }"
        :buffer="2000"
      >
        <SongItem
          className="sec-gusse-sing-list-item"
          :songItem="songItem"
          :key="songItem.i"
          :index="songItem.i"
          :orderLog="{
            'str1': from ? fromEventMapping[from].str1 : '',
            'str2': from ? fromEventMapping[from].str2 : '猜你会唱',
            'str4': 'click',
          }"
          :logData="fromEventMapping[from].logData"
          @singer-click="handleClickSinger"
          :noPadding="noPadding"
        />
      </RecycleScroller>
      <SongItem
        v-else
        :renderType="renderType"
        :isStickButton="isStickButton"
        className="sec-gusse-sing-list-item"
        v-for="(songItem, ind) in guessSongShowList"
        :key="ind"
        :songItem="songItem"
        :order-log="{
          'str1': from ? fromEventMapping[from].str1 : '',
          'str2': from ? fromEventMapping[from].str2 : '猜你会唱',
          'str3': renderType === 'block' ? '加入已点' : '',
          'str4': 'click',
        }"
        :logData="fromEventMapping[from].logData"
        :noPadding="true"
        @singer-click="handleClickSinger"
      />
    </div>
  </SecContainer>
</template>

<script setup>
import { onMounted, ref, computed, watch, nextTick, defineProps, defineEmits, toRefs } from 'vue'
import SecContainer from '@/components/section-container/index.vue'
import SongItem from '@/components/song-item/index.vue'
import { useStore } from 'vuex'
import { sendLog } from '@/directives/v-log/log'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { RecycleScroller } from 'vue-virtual-scroller'

const props = defineProps({
  isFastList: Boolean,
  renderType: {
    default: 'list',
    type: String,
  },
  isStickButton: {
    default: true,
    type: Boolean
  },
  pageSize: {
    default: 6,
    type: Number
  },
  showAllData: {
    default: false,
    type: Boolean
  },
  pageRoute: {
    default: '1',
    type: String,
  },
  ponitActionLog: {
    default: () => ({
      event_type: '10000~50000',
      event_name: 6001,
      event_data: {
        str1: '首页',
        str2: '猜你会唱',
        str3: '歌曲列表',
        str4: 'click',
      },
    }),
    type: Object,
  },
  song_list_source: {
    default: 0,
    type: Number,
  },
  noPadding: Boolean,
  itemCount: {
    default: 0,
    type: Number
  },
  from: {
    default: 'home',
    type: String
  },
})

const fromEventMapping = {
  home: {
    event_name: 6001,
    str1: '首页',
    str2: '猜你会唱',
    logData: {
      vipLog: {
        str1: '首页',
        str2: '猜你会唱',
        str3: '点击 VIP 歌曲',
        str4: 'click',
      },
      fr: [1820, 1821],
    },
  },
  singing: {
    event_name: 6001,
    str1: '唱过的歌',
    str2: '猜你会唱',
    logData: {
      vipLog: {
        str1: '首页',
        str2: '猜你会唱',
        str3: '点击 VIP 歌曲',
        str4: 'click',
      },
      fr: [1820, 1821],
    },
  },
  search: {
    event_name: 6002,
    str1: '搜索页',
    str2: '猜你会唱',
    logData: {
      vipLog: {
        str1: '搜索',
        str2: '猜你会唱',
        str3: '点击 VIP 歌曲',
        str4: 'click',
      },
      fr: [1828, 1829],
    },
  },
  mv: {
    event_name: 6007,
    str1: '欢唱页',
    str2: '猜你会唱',
    logData: {
      vipLog: {
        str1: 'MV 页面',
        str2: '快速点歌-搜索-猜你会唱',
        str3: '点击 VIP 歌曲',
        str4: 'click',
      },
      fr: [1834, 1835],
    },
  },
  mvSearch: {
    event_name: 6007,
    str1: '欢唱页',
    str2: '搜索结果-猜你会唱',
    logData: {
      vipLog: {
        str1: 'MV 页面',
        str2: '快速点歌-搜索-猜你会唱',
        str3: '点击 VIP 歌曲',
        str4: 'click',
      },
      fr: [1834, 1835],
    },
  },
};

const emit = defineEmits(['singer-click'])

const store = useStore()
const isLogin = computed(() => !!store.state.userInfo.unionid)
const oftenSingList = computed(() => store.state.oftenSing.oftenSingList)
const isGuessSongList = computed(() => isLogin.value && oftenSingList.value.length)
const top50List = computed(() => store.state.oftenSing.top50)
const { pageSize, showAllData } = toRefs(props)
let page = ref(1)
let guessSongShowList = ref([])
const currentSize = computed(() => store.state.currentSize);

const guessSongShowListUpdate = async () => {
  await nextTick()
  const baseList = isGuessSongList.value 
    ? [...oftenSingList.value, ...top50List.value]
    : [...top50List.value]

  if (baseList.length === 0) {
    guessSongShowList.value = []
    return
  }

  const repeatCount = Math.ceil(90 / baseList.length) || 1
  let guessSongList = Array(repeatCount).fill(baseList).flat().slice(0, 90)

  guessSongList = guessSongList.map((item, i) => ({ ...item, i }))

  const maxPage = Math.ceil(guessSongList.length / pageSize.value)

  if (page.value > maxPage) {
    page.value = 1
  }

  const startIndex = (page.value - 1) * pageSize.value
  const endIndex = page.value * pageSize.value

  guessSongShowList.value = showAllData.value 
    ? guessSongList 
    : guessSongList.slice(startIndex, endIndex)
  }

  const handlechangePagenation = () => {
  if (!oftenSingList.value.length && !top50List.value.length) return
  page.value++
  guessSongShowListUpdate()


  const { event_name = '', str1 = ''} = fromEventMapping[props.from]
  sendLog({
    event_type: 'click',
    event_name,
    event_data: {
      str1,
      str2: '猜你会唱',
      str3: '换一批',
      str4: 'click',
    },
  });
}

const resetPage = () => {
  page.value = 1
  guessSongShowListUpdate()
}

const handleClickSinger = (data) => {
  emit('singer-click', data)
}

watch(isLogin, resetPage)
watch(oftenSingList, resetPage)
watch(top50List, resetPage)
watch(currentSize,(val)=>{
  // 分辨率切换时，及时更新列表条数
  if(val){
    resetPage()
  }
})
onMounted(() => {
  resetPage()
  const { event_name = '', str1 = '', str2 = '猜你会唱'} = fromEventMapping[props.from]
  sendLog({
    event_type: 'show',
    event_name,
    event_data: {
      str1,
      str2,
      str3: '猜你会唱列表展现',
      str4: 'show',
    },
  });
})

// const tableItemSize = ref((document.documentElement.clientWidth || document.body.clientWidth) * 137 / 1920)

//数据行实际高度
const tableItemSize = ref(
  currentSize.value === 'small' 
    ? 85 
    : currentSize.value === 'medium' 
      ? 78
      : (document.documentElement.clientWidth || document.body.clientWidth) * 140 / 1920
);
</script>

<style lang="stylus" scoped>
.dark-theme .home-guess
  --bg-color: rgba(255, 255, 255, 0.08) // 默认浅色背景

.sec-gusse
  &-sing
    margin-bottom 40px
    &-list
      min-height unset
      display grid
      grid-template-columns repeat(3, 498px)
      grid-row-gap 32px
      justify-content space-between
      position relative
      padding 0 10px
      :deep(.song-item)
        height 137px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        grid-template-columns repeat(2, 1fr) !important
        
    &-change
      width auto !important
      *
        color var(--text-name-color)
        font-weight 400
      .svg-icon
        width calc(18px * var(--scale))
        height calc(18px * var(--scale))
        margin-left 12px

        // 三分之一屏 & 三分之二屏 
        @media (max-width: 900px)
          width calc(18px * var(--scale)) !important
          height calc(18px * var(--scale)) !important

.sec-gusse-sing-list
  // 三分之一屏
  @media (max-width: 700px)
    display block
    .song-item
      height calc(76px * 3) 
      border-radius 12px 
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    grid-template-columns repeat(2, 1fr)
    column-gap calc(20px * var(--scale)) !important
    :deep(.song-item)
      height calc(78px * 1.48) !important
      .right
         div
           margin-left: calc(12px * var(--scale)) !important;
.home-guess
  --bg-color: rgba(244, 246, 255, 1) // 默认浅色背景
  margin-top 0px!important
  padding-top 48px!important
  :deep(.section-container)
    &-list
      background-color: var(--card-bg-color) // 使用局部 CSS 变量
      border-radius 28px
      padding calc(16px * var(--scale)) 0px calc(32px * var(--scale))
      .sec-gusse-sing-list
        min-height unset
        grid-template-columns repeat(3, 33.3%)
        grid-row-gap 0px
      .song-item
        padding 0 36px
        border-radius 16px

.guess-small
  :deep(.section-container-header)
    margin calc(24px * 3) 0 calc(12px * 3)
    padding-bottom 0px
  :deep(.song-item)
    &:active
      &:not(:has(.right *:active))
        background-color: var(--pressed-color2)

    background var(--song-card-background-color)
    margin-bottom calc(16px * 3)
    height calc(78px * 3) !important
    border-radius calc(12px * 3)
    padding-left calc(20px * 3)
    padding-right calc(20px * 3)
    .left
      .name
        margin-bottom calc(4px * 3) !important
    &:last-child
      margin-bottom 0px

.sec-gusse-sing-render-list
  .sec-gusse-sing-list
    display block
</style>
