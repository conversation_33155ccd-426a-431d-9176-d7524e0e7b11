<template>
  <div class="network-error flex-column" @click="handleRefresh">
    <svg 
      :class="{ rotate: isRotating }"
      width="118" 
      height="118" 
      viewBox="0 0 118 118" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <g opacity="0.4" clip-path="url(#clip0_1257_79114)">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M67.2741 14.6107C64.7479 14.1758 62.1504 13.9492 59.5 13.9492C34.371 13.9492 14 34.3203 14 59.4492C14 73.9007 20.7373 86.7785 31.2423 95.1132L36.3475 92.439C25.9769 85.1475 19.2 73.0898 19.2 59.4492C19.2 37.1921 37.2429 19.1492 59.5 19.1492C60.0375 19.1492 60.5725 19.1597 61.1048 19.1806L67.2741 14.6107ZM82.4298 26.304C92.926 33.5789 99.8 45.7113 99.8 59.4492C99.8 81.7063 81.7571 99.7492 59.5 99.7492C58.7975 99.7492 58.0992 99.7313 57.4055 99.6957L51.3065 104.213C53.9644 104.697 56.7027 104.949 59.5 104.949C84.629 104.949 105 84.5782 105 59.4492C105 44.903 98.174 31.951 87.5504 23.6218L82.4298 26.304Z" fill="currentColor"/>
      <path d="M59.5559 104.949L63.8073 95.0488C63.8834 94.8715 63.9215 94.7827 63.9382 94.7344C64.3319 93.5908 63.167 92.5307 62.0655 93.0302C62.019 93.0513 61.9343 93.0975 61.7648 93.19L49.1525 100.076C48.3192 100.531 47.8008 101.404 47.8008 102.354C47.8008 103.108 48.4861 103.613 49.221 103.783C52.5244 104.546 55.9655 104.949 59.5008 104.949C59.5192 104.949 59.5375 104.949 59.5559 104.949Z" fill="currentColor"/>
      <path d="M58.9187 13.9529L54.6689 23.8497C54.5927 24.0271 54.5547 24.1158 54.538 24.164C54.1443 25.3076 55.3092 26.3677 56.4107 25.8682C56.4572 25.8471 56.5419 25.8009 56.7113 25.7084L69.3237 18.8227C70.157 18.3677 70.6754 17.4941 70.6754 16.5446C70.6754 15.6752 69.8508 15.123 69.0003 14.9423C65.9364 14.2916 62.7586 13.9492 59.5008 13.9492C59.3065 13.9492 59.1125 13.9504 58.9187 13.9529Z" fill="currentColor"/>
      </g>
      <defs>
      <clipPath id="clip0_1257_79114">
      <rect width="117" height="117" fill="currentColor" transform="translate(0.5 0.449219)"/>
      </clipPath>
      </defs>
    </svg>

    <p>网络异常</p>
    <p>点击刷新二维码</p>
  </div>
</template>

<script setup>
import { defineEmits, ref } from 'vue'

const emit = defineEmits(['refresh'])
const isRotating = ref(false)

const handleRefresh = () => {
  isRotating.value = true
  emit('refresh')

}
</script>

<style lang="stylus" scoped>
@keyframes rotate {
  from {
    transform: rotate(0deg)
  }
  to {
    transform: rotate(360deg)
  }
}

.rotate {
  animation: rotate 0.6s ease-in-out infinite
}

.network-error
  display flex
  flex-direction column
  align-items center
  justify-content center
  padding 0px
  cursor pointer
  transition all 0.3s ease
  width 100%
  height 100%
  border-radius 16px

  svg
    width 35%
    height 35%
    margin-bottom 10px
    transition transform 0.3s
    transform-origin center
    color var(--text-primary-color)

  p
    font-size var(--font-size-small)
    opacity 0.4
    white-space nowrap
</style> 