<template>
  <div class="marquee-container">
    <div class="marquee-content flex">
      <slot></slot> <!-- 使用 slot 插槽接收外部内容 -->
      <slot></slot> <!-- 复制内容以实现无缝滚动 -->
    </div>
  </div>
</template>

<style scoped>
.marquee-container {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
}

.marquee-content {
  display: inline-block;
  animation: marquee linear infinite;
  animation-duration: 10s; /* 根据 speed 控制动画速度 */
  width: calc(100% * 2); /* 调整宽度以适应复制的内容 */
}

@keyframes marquee {
  from {
    transform: translateX(100%); /* 从右侧开始 */
  }
  to {
    transform: translateX(-100%); /* 移动到左侧 */
  }
}
</style>