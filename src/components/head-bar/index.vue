<template>
  <div class="header">
    <div class="header-back">
      <img
        @click="handleBack"
        :src="imgs[themeClass].back"
      />
    </div>
    <span>
      {{ title }}
    </span>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'HeadBar',
  props: {
    title: String,
  },
  setup(_, { emit }) {
    const router = useRouter()
    const store = useStore()

    const imgs = {
      themeDark: {
        back: 'https://qncweb.ktvsky.com/20231207/vadd/72ff0b114ee2cb3153ce901af19bc813.png',
      },
      themeLight: {
        back: 'https://qncweb.ktvsky.com/20240221/vadd/7691c0a91bc7f951374f15c175f8d69a.png',
      },
      themeSystem: {
        back: 'https://qncweb.ktvsky.com/20231207/vadd/72ff0b114ee2cb3153ce901af19bc813.png',
      },
    }

    const themeClass = computed(() => store.state.themeClass)

    const handleBack = () => {
      store.dispatch('getCarplayInfo') // 返回上一页时更新下用户状态
      router.back()
      emit('back')
      store.commit('base/SET_IS_ROUTER_BACK', true)
    }

    return {
      handleBack,
      imgs,
      themeClass
    }
  },
}
</script>

<style lang="stylus" scoped>
.header
  width 100vw
  padding 36px 80px
  background none
  display flex
  align-items center
  position fixed
  top 0
  left 0
  z-index 6
  @media screen and (max-width 1200px)
    padding 0px 60px 0px 48px
    height 140px
  &-back
    width fit-content
    height 90px
    display flex
    align-items center
    justify-content center
    img
      width 40px
      height 40px
      margin-right 60px
  span
    color rgba(255, 255, 255, 1)
    font-size 32px
    font-weight 300
.theme-themeLight
  span
    color #1D1D1F
</style>

