<template>
  <div v-if="currentSize === 'small'" class="nav-list-small">
    <div class="flex-between">
      <div class="left flex-column">
        <div @click="handleClickNav({ name: 'singer' })">
          <img src="https://qncweb.ktvsky.com/20250212/vadd/95367d2dc52a14d222dfe938bc45e650.jpg"/>
        </div>
        <!-- <div @click="handleClickNav({ name: 'aiface', disable: true })">
          <img src="https://qncweb.ktvsky.com/20250108/other/70ca3a724442e36c8644d7d615f8788a.png"/>
        </div> -->
        <div @click="handleClickNav({ name: 'singing' })">
          <img src="https://qncweb.ktvsky.com/20250212/vadd/55ea8a74b780b94b2724e326b87f0e26.jpg"/>
        </div>
      </div>
      <div class="right" :class="{ 'not-vip': !isVip }">
        <NavListUnlogin v-show="!isLogin" :currentSize="currentSize" />
        <NavListLogin v-show="isLogin" :currentSize="currentSize" />
      </div>
    </div>
    <div
      class="bottom"
      @click="handleClickNav({
        name: 'songList',
        query: {
          name: '小米汽车专属',
          image: 'https://qncweb.ktvsky.com/20250212/vadd/34ab3a13ecb96266805124adb0f68901.jpg',
          from: 'gedan'
        } 
      })"
    >
      <!-- <img src="https://qncweb.ktvsky.com/20250328/other/b7213857f003c761d3aef09aca3fc1a1.jpg" /> -->
      <img src="https://qncweb.ktvsky.com/20250529/vadd/f1a1ee84272059aa4307ad523e4c42e7.jpg" />

    </div>
  </div>
  
  <div v-else-if="currentSize === 'medium'" class="nav-list-wrapper-medium">
    <div class="nav-list flex-between">
      <div class="left">
        <NavListUnlogin v-show="!isLogin" />
        <NavListLogin v-show="isLogin" />
      </div>

      <div class="center" v-if="currentSize !== 'medium'">
        <div class="card-wrapper flex-between">
          <div
            class="nav-list-item"
            @click="handleClickNav({ name: 'singer' })"
            v-log="{
              event_type: 'click',
              event_name: '6001',
              event_data: {
                str1: '首页',
                str2: '歌手',
                str3: '歌手',
                str4: 'click',
              }
            }"
          >
            <!-- <img src="@/assets/images/nav-img1.png" alt="全部歌手" /> -->
            <img src="https://qncweb.ktvsky.com/20250212/vadd/7be9e367a522719fda25bcdf62cb94d1.jpg" alt="歌手" />
            
          </div>
          <!-- <div
            class="nav-list-item"
            @click="handleClickNav({ name: 'aiface', disable: true })"
            v-log="{
              event_type: 'click',
              event_name: '6001',
              event_data: {
                str1: '首页',
                str2: '换脸 MV',
                str3: '换脸 MV 点击',
                str4: 'click',
              }
            }"
          >
            <img src="@/assets/images/nav-img2.png" alt="换脸MV" />
            
          </div> -->
          <div
            class="nav-list-item"
            @click="handleClickNav({ name: 'singing', disable: true })"
            v-log="{
              event_type: 'click',
              event_name: '6001',
              event_data: {
                str1: '首页',
                str2: '唱过的歌',
                str3: '唱过的歌 点击',
                str4: 'click',
              }
            }"
          >
            <!-- <img src="@/assets/images/nav-img2.png" alt="唱过的歌" /> -->
            <img src="https://qncweb.ktvsky.com/20250212/vadd/5357e1f7d36008feb5a673c808b6372c.jpg" alt="歌手" />
          </div>
        </div>
        <div
          class="nav-list-item"
          v-log="{
            event_type: 'click',
            event_name: '6001',
            event_data: {
              str1: '首页',
              str2: '歌单卡片',
              str3: '小米定制',
              str4: 'click',
            }
          }"
          @click="handleClickNav({
            name: 'songList',
            query: {
              name: '小米汽车专属',
              image: 'https://qncweb.ktvsky.com/20250212/vadd/34ab3a13ecb96266805124adb0f68901.jpg',
              from: 'gedan'
            } 
          })">
          <!-- <img src="@/assets/images/nav-img3.png" alt="定制歌单" /> -->
          <img src="https://qncweb.ktvsky.com/20250212/vadd/9aa7c0d686a880ba16ed07f21f5f7603.jpg" alt="定制歌单" />
        </div>
      </div>
      <div class="mv-mini"
        :class="{ 'mv-mini-medium': currentSize === 'medium' }"
        id="mvMini"
        @click.self="handleClickMini"
        v-touch:swipe="handleSwipe"
      >
        <MvMiniDefault
          v-if="isInitOrderd && orderedListNum === 0"
          ref="mvMiniDefaultRef"
        />
        <MvMiniControls />
        <LrcList
          v-show="!isMvMode && mvIsHide && currentSize === 'full'"
          class="mini-lrc"
          :lrc-data="ircListData"
          :singer-name="singerName"
          :song-name="songName"
          :current-iyric-index="currIrcIndex"
          :paused="videoPaused"
          id="mini-lrc"
          from="mini"
        />
        <div v-show="isCoverVisible" class="cover"></div>
      </div>
      <div class="flex-1"></div>
    </div>

    <div class="nav-list-medium flex-between">
      <div
        class="nav-list-medium-item"
        @click="handleClickNav({ name: 'singer' })"
        v-log="{
          event_type: 'click',
          event_name: '6001',
          event_data: {
            str1: '首页',
            str2: '歌手',
            str3: '歌手',
            str4: 'click',
          }
        }"
      >
        <img src="https://qncweb.ktvsky.com/20250212/vadd/48e375840a9fd43ba492f1108c9256b7.jpg" alt="歌手" />
      </div>
      <div
        class="nav-list-medium-item"
        @click="handleClickNav({ name: 'singing', disable: true })"
        v-log="{
          event_type: 'click',
          event_name: '6001',
          event_data: {
            str1: '首页',
            str2: '唱过的歌',
            str3: '唱过的歌点击',
            str4: 'click',
          }
        }"
      >
        <img src="https://qncweb.ktvsky.com/20250212/vadd/c50d75aa9ea112330e3745b4437517df.jpg" alt="唱过的歌" />
      </div>
      <div
          class="nav-list-medium-right"
          v-log="{
            event_type: 'click',
            event_name: '6001',
            event_data: {
              str1: '首页',
              str2: '歌单卡片',
              str3: '小米定制',
              str4: 'click',
            }
          }"
          @click="handleClickNav({
            name: 'songList',
            query: {
              name: '小米汽车专属',
              image: 'https://qncweb.ktvsky.com/20250212/vadd/34ab3a13ecb96266805124adb0f68901.jpg',
              from: 'gedan'
            } 
          })">
          <!-- <img src="@/assets/images/nav-img3-m.png" alt="定制歌单" /> -->
          <img src="https://qncweb.ktvsky.com/20250328/other/0f7285732fbcd4da585bade0d4a73738.jpg" alt="定制歌单" />
        </div>
    </div>
  </div>

  <div v-else class="nav-list-wrapper">
    <div class="nav-list flex-between">
      <div class="left">
        <NavListUnlogin v-show="!isLogin" />
        <NavListLogin v-show="isLogin" />
      </div>

      <div class="center">
        <div class="card-wrapper flex-between">
          <div
            class="nav-list-item"
            @click="handleClickNav({ name: 'singer' })"
            v-log="{
              event_type: 'click',
              event_name: '6001',
              event_data: {
                str1: '首页',
                str2: '歌手',
                str3: '歌手',
                str4: 'click',
              }
            }"
          >
            <!-- <img src="@/assets/images/nav-img1.png" alt="全部歌手" /> -->
            <img src="https://qncweb.ktvsky.com/20250212/vadd/7be9e367a522719fda25bcdf62cb94d1.jpg" alt="歌手" />
            
          </div>
          <!-- <div
            class="nav-list-item"
            @click="handleClickNav({ name: 'aiface', disable: true })"
            v-log="{
              event_type: 'click',
              event_name: '6001',
              event_data: {
                str1: '首页',
                str2: '换脸 MV',
                str3: '换脸 MV 点击',
                str4: 'click',
              }
            }"
          >
            <img src="@/assets/images/nav-img2.png" alt="换脸MV" />
            
          </div> -->
          <div
            class="nav-list-item"
            @click="handleClickNav({ name: 'singing', disable: true })"
            v-log="{
              event_type: 'click',
              event_name: '6001',
              event_data: {
                str1: '首页',
                str2: '唱过的歌',
                str3: '唱过的歌 点击',
                str4: 'click',
              }
            }"
          >
            <!-- <img src="@/assets/images/nav-img2.png" alt="唱过的歌" /> -->
            <img src="https://qncweb.ktvsky.com/20250212/vadd/5357e1f7d36008feb5a673c808b6372c.jpg" alt="歌手" />
          </div>
        </div>
        <div
          class="nav-list-item"
          v-log="{
            event_type: 'click',
            event_name: '6001',
            event_data: {
              str1: '首页',
              str2: '歌单卡片',
              str3: '小米定制',
              str4: 'click',
            }
          }"
          @click="handleClickNav({
            name: 'xiaomiExclusive',
            query: {
              name: '小米汽车专属',
              image: 'https://qncweb.ktvsky.com/20250212/vadd/34ab3a13ecb96266805124adb0f68901.jpg',
              from: 'gedan'
            } 
          })">
          <!-- <img src="@/assets/images/nav-img3.png" alt="定制歌单" /> -->
          <img src="https://qncweb.ktvsky.com/20250328/other/0f7285732fbcd4da585bade0d4a73738.jpg" alt="定制歌单" />
        </div>
      </div>
      <div class="mv-mini"
        id="mvMini"
        @click.self="handleClickMini"
        v-touch:swipe="handleSwipe"
      >
        <MvMiniDefault
          v-if="isInitOrderd && orderedListNum === 0"
          ref="mvMiniDefaultRef"
        />
        <MvMiniControls />
        <LrcList
          v-show="!isMvMode && currentSize === 'full'"
          class="mini-lrc"
          :lrc-data="ircListData"
          :singer-name="singerName" 
          :song-name="songName"
          :current-iyric-index="currIrcIndex"
          :paused="videoPaused"
          id="mini-lrc"
          from="mini"
        />
        <div v-show="isMvMode && !isCoverVisible" class="cover"></div>
      </div>
      <div class="flex-1"></div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch, nextTick, defineAsyncComponent, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import useLoginValid from '@/composables/useLoginValid'
import useQRCode from '@/composables/useQRCode'
import { getCarplayInfo } from '@/service/carplay-info'
import { withTimeoutHandling } from '@/utils/promiseUtils'
import NavListUnlogin from './unlogin.vue'
import NavListLogin from './user.vue'
import MvMiniDefault from '@/components/mv/mini-default.vue'
import MvMiniControls from './mv-control.vue'
import useActivity from '@/composables/useActivity';
import { TSBaseInfoInstance, TSMediaInstance } from '@/packages/TSJsbridge'
import getComponentLrcData from '@/components/mv/utils'
import get from 'lodash/get'
import useMvMode from '@/composables/useMvMode';
import eventBus from '@/utils/event-bus';
import Toast from '@/utils/toast'
import { sendLog } from '@/directives/v-log/log'

const LrcList = defineAsyncComponent(() => import('@/components/lrc/index.vue'));

const store = useStore()
const router = useRouter()
const route = useRoute()
const {showLoginQrcode, isLogin } = useLoginValid()
const { getQRCodeURL } = useQRCode()
const { showAiFace } = useActivity()

const vipInfo = computed(() => store.state.vipInfo)
const isVip = computed(() => !!vipInfo.value.end_time)
const net_status = computed(() => store.state.base.net_status)
const carplayInfo = computed(() => store.state.carplayInfo)
const mvIsHide = computed(()=> store.state.mvIsHide)
const orderedListNum = computed(() => store.state.orderedList.length)
const videoPlayer = computed(() => store.state.videoPlayerHistory);
const defaultSong = computed(() => store.state.miniMv.defaultSong);
const ircListData = computed(() => {
  if (orderedListNum.value === 0) {
    return getComponentLrcData(defaultSong.value?.lrc?.json)
  }
  return getComponentLrcData(store.state.songItemLrc)
})
const currIrcIndex = computed(() => store.state.currIrcIndex)
const songName = computed(() => get(store.state.orderedList, '[0].music_name', ''))
const singerName = computed(() => get(store.state.orderedList, '[0].singer', ''))
const videoPaused = computed(() => store.state.videoPaused)
const isInitOrderd = computed(() => store.state.isInitOrderd)
const isMvMode = computed(() => store.state.mvMode.mode === 'mv')
const currentSize = computed(() => store.state.currentSize);

let qrCodeURL = ref('')
let isRequest = ref(false)
// const isInitOrderd = ref(false)
let observer = null
const isCoverVisible = ref(true)
const mvMiniDefaultRef = ref(null)

const handleSwipe = (direction) => {
  console.log('handleSwipe', direction)
  if (mvMiniDefaultRef.value) {
    mvMiniDefaultRef.value.handleSwipe(direction)
  }
}

const handleClickMini = () => {
  sendLog({
    event_type: 'click',
    event_name: '6001',
    event_data: {
      str1: '首页',
      str2: '小窗 MV',
      str3: '点击画面区域进入MV 页面',
      str4: 'click',
    }
  })
  store.commit('UPDATE_MV_ISHIDE', false)
}

const getLoginQrcode = async (payload) => {
  try {
    if (isRequest.value || !net_status.value && payload !== 'reload') return;

    isRequest.value = true
    
    let pay_qr
    if (payload === 'reload') {
      pay_qr = (await getCarplayInfo(true)).data.pay_qr
    } else {
      pay_qr = carplayInfo.value.pay_qr || (await withTimeoutHandling(getCarplayInfo())).data.pay_qr
    }
    if (pay_qr) {
      const qrCodeData = await withTimeoutHandling(getQRCodeURL(`${pay_qr}&fr=mine`))
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData
      }
    }
  } catch (error) {
    console.log('getLoginQrcode error', error)
  } finally {
    isRequest.value = false
  }
}

const handleClickNav = (payload) => {
  if (!isLogin.value && payload.name == 'singing') {
    sendLog({
      event_type: 'show',
      event_name: '1021',
      event_data: {
        str1: '首页',
        str2: '唱过的歌',
        str3: '点击 VIP 歌曲',
        str4: 'click',
        str5: isLogin.value ? '已登录' : '未登录',
        str6: '',
        str9: '手机端',
        str10: 1817,
      }
    })
    showLoginQrcode({
      log: '首页-唱过的歌-未登录'
    });
    return
  }

  // if (payload.disable) {
  //   Toast('功能开发中，敬请期待')
  //   return
  // }
  // if (payload.name === 'aiface') {
  //   showAiFace()
  //   return
  // }
  router.push(payload)
}

watch(isLogin, (val) => {
  try {
    if (!val) {
      getLoginQrcode();
    }
  } catch (error) {
    console.error('watch isLogin 发生错误', error);
  }
}, { immediate: true });

watch(net_status, (val) => {
  try {
    if (val) {
      getLoginQrcode();
    }
  } catch (error) {
    console.error('watch net_status 发生错误', error);
  }
}, {
  immediate: true,
  deep: true
});

watch(orderedListNum, (val) => {
  if (val === 0) {
    const isMuted = TSMediaInstance.getMute();
    store.commit('SET_VIDEO_MUTE', isMuted);
  }
})

watch(currentSize, async(val) => {
  try {
    if (val !== 'small') {
      await nextTick();
      handleSetVideo();

      setTimeout(() => {
        handleSetVideo();
      }, 1000);
    } else if (orderedListNum.value === 0) {
      TSMediaInstance.stop();

      store.commit('miniMv/SET_DEFAULT_SONG', {
        song: store.state.miniMv.defaultList[0],
        index: 0,
      });

      console.log('默认资源 暂停');
    }
  } catch (error) {
    console.error('watch currentSize 发生错误', error);
  }
});

watch(mvIsHide, async(val) => {
  try {
    if (val) {
      await nextTick();

      const isMuted = TSMediaInstance.getMute();
      store.commit('SET_VIDEO_MUTE', isMuted);

      handleSetVideo();

      setTimeout(() => {
        handleSetVideo();
      }, 1000);
    }
  } catch (error) {
    console.error('watch mvIsHide 发生错误', error);
  }
});

// 监听路由变化
watch(
  () => route.name, // 监听路由的 path 变化
  async (newName) => {
    // 判断当前路由是否为 'home'
    if (newName === 'home') {
      await nextTick()
      handleSetVideo()

      setTimeout(() => {
        // const scrollArea = document.querySelector('.scroll-area');
        // scrollArea.scrollTo(0, 10)
        console.log('watch home')
        handleSetVideo()
      }, 1000)
      const isMuted = TSMediaInstance.getMute()
      store.commit('SET_VIDEO_MUTE', isMuted)
    }
  }
)


// const handleInitOrderedList = (payload) => {
//   isInitOrderd.value = payload
// }

onMounted(async () => {
  try {
    const isMuted = TSMediaInstance.getMute();
    store.commit('SET_VIDEO_MUTE', isMuted);
    eventBus.on('toggle-cover', (show) => {
      isCoverVisible.value = show;
    });
    if (!isLogin.value) {
      await getLoginQrcode();
      console.log(net_status.value, '1108');
    }
    await nextTick();
    // if (currentSize.value === 'small') {
    //   return;
    // }
    const scrollArea = document.querySelector('.scroll-area');
    if (scrollArea) {
      scrollArea.addEventListener('scroll', handleScroll);
    }
    handleSetVideo();
  } catch (error) {
    console.error('onMounted 发生错误', error);
  }
});

onUnmounted(() => {
  // eventBus.off('init-orderedList', handleInitOrderedList)
  eventBus.off('toggle-cover')
  observer.disconnect();
})

const handleSetVideo = async () => {
  try {
    await nextTick();

    const scrollArea = document.querySelector('#mvMini');
    if (!scrollArea) {
      console.warn('scrollArea 不存在');
      return;
    }
    const { x, y, width, height } = scrollArea.getBoundingClientRect();
    console.log('handleSetVideo', window.devicePixelRatio, scrollArea.getBoundingClientRect());

    TSBaseInfoInstance.setHomeVideoParams({
      width: width * window.devicePixelRatio + 10,
      height: height * window.devicePixelRatio + 10,
      top: y * window.devicePixelRatio,
      left: x * window.devicePixelRatio,
    });
  } catch (error) {
    console.error('handleSetVideo 发生错误', error);
  }
};

const handleScroll = () => {
  if (currentSize.value === 'small') {
    console.warn('三分之一屏禁止滑动');
    return
  }
  try {
    const scrollArea = document.querySelector('.mv-mini');
    if (!scrollArea) {
      console.warn('scrollArea 不存在');
      return;
    }
    const { y } = scrollArea.getBoundingClientRect();
    TSBaseInfoInstance.scollHomeVideo(y * window.devicePixelRatio);
  } catch (error) {
    console.error('handleScroll 发生错误', error);
  }
}
</script>

<style lang="stylus" scoped>
.nav-list-wrapper-medium
  background transparent !important
  padding-right 0px !important
  padding-left 0px !important
  padding-top 0 !important
  box-shadow none !important
  position relative
  .nav-list
    margin 0px
    padding-bottom 48px
    position relative
    z-index 10000
    clip-path: none !important
    background transparent !important
    padding 0px !important
    box-shadow inset 30px 0px 20px var(--background-primary);
    height 610px !important
    justify-content space-between
    margin-top 0 !important
    .cover
      width 100%
      height 100%
      background url('https://qncweb.ktvsky.com/20241227/other/b1e6cb49a76d753245ec0a5dd71356dc.png') no-repeat center
      background-size auto 100%
      position absolute
      top 0
      left 0
      z-index 10
    .left, .center, .flex-1
      background var(--background-primary)

    .left
      box-shadow: none !important
      padding-left calc(32px * 2.5)
      padding-right calc(15px * 2.5)
    .mv-mini
      overflow hidden 
      clip-path: none !important
      overflow hidden
      box-shadow 0 0 0 24px var(--background-primary)
      position relative
      background transparent !important
      
      width calc(460px * 2.5)
      height 100%
      border-radius calc(16px * 2.5)
    .flex-1
      height 100%
      width calc(30px * 2.5)
      
    .center
      width calc(552px + 24px * 2)
      margin 0 0px !important
      padding 0 24px
      .card-wrapper
        margin-bottom 24px
      img
        height 198px
        border-radius 30px
  .nav-list-medium
    height auto
    margin-top calc(15px * var(--scale))
    background var(--background-primary)
    box-shadow 0 0 0 24px var(--background-primary)
    padding calc(15px * 1.48) calc(50px * 1.48) 0
    img
      width auto
      height calc(200px * var(--scale))
      border none
      border-radius 40px
</style>

<style lang="stylus" scoped>
.nav-list-small
  padding-top calc(20px * 1.48) !important
  .bottom
    margin-top calc(12px * 3)
    img
      border-radius 40px
  .left
    height calc(152px * 3)
    justify-content space-between
    div
      width calc(174px * 3)
      height auto
      border-radius calc(12px * 3)
      &:first-child
        background #388EFF
      &:last-child
        background #A449F4

      img
        width 100%
        border-radius 40px
  .right
    width calc(174px * 3)
    height calc(152px * 3)
    border-radius calc(12px * 3)
    background: linear-gradient(90deg, #FBF6D8 0%, #D9A85E 100%);
    overflow hidden
    &.not-vip
      background: linear-gradient(113.69deg, #8B68E3 0%, #9029CA 97.87%);

.nav-list-wrapper
  background transparent !important
  padding-right 0px !important
  padding-left 0px !important
  padding-top 0 !important
  box-shadow none !important
  position relative
  .nav-list
    margin 0px
    padding-bottom 48px
    height 419px
    position relative
    z-index 10000
    clip-path: none !important
    background transparent !important
    padding 0px !important
    box-shadow inset 30px 0px 20px var(--background-primary);
    .cover
      width 100%
      height 100%
      background url('https://qncweb.ktvsky.com/20241227/other/b1e6cb49a76d753245ec0a5dd71356dc.png') no-repeat center
      background-size auto 100%
      position absolute
      top 0
      left 0
      z-index 10
    .left, .center, .flex-1
      background var(--background-primary)

    .left
      box-shadow: none !important
      padding-left var(--padding-size)
      padding-right 0px
      flex 1
    .mv-mini
      width calc(498px * var(--scale))
      height 100%
      border-radius 24px
      overflow hidden 
      clip-path: none !important
      overflow hidden
      box-shadow 0 0 0 24px var(--background-primary)
      position relative
      background transparent !important
      
    .flex-1
      width calc(43px * var(--scale))
      height 100%
      
    .center
      width calc(552px + 24px * 2)
      margin 0 0px !important
      padding 0 24px
      .card-wrapper
        margin-bottom 24px
      img
        height 198px
        border-radius 30px
</style>
