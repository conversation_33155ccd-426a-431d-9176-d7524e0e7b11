<template>
  <div class="mvmini-control flex-between">
    <div class="mvmini-control-left flex">
      <p
        v-if="isPlayDefault"
        class="mute"
        @click="handleClickMute"
      >
        <svg v-show="videoMute" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11.6048 19.8783L10.9193 20.8632L11.6048 19.8783ZM11.6048 4.12198L10.9193 3.13705L11.6048 4.12198ZM6.20917 7.87727L5.52366 6.89234L6.20917 7.87727ZM6.20415 16.1195L6.88965 15.1346L6.20415 16.1195ZM5.81907 14.7987H2.67215V17.1987H5.81907V14.7987ZM3.19805 15.3246V8.67219H0.798047V15.3246H3.19805ZM2.67215 9.19809H5.82409V6.79809H2.67215V9.19809ZM6.89467 8.8622L12.2903 5.10691L10.9193 3.13705L5.52366 6.89234L6.89467 8.8622ZM11.4639 4.67527V19.325H13.8639V4.67527H11.4639ZM12.2903 18.8934L6.88965 15.1346L5.51865 17.1045L10.9193 20.8632L12.2903 18.8934ZM11.4639 19.325C11.4639 18.9002 11.9416 18.6507 12.2903 18.8934L10.9193 20.8632C12.1618 21.7281 13.8639 20.8389 13.8639 19.325H11.4639ZM12.2903 5.10691C11.9416 5.34959 11.4639 5.10009 11.4639 4.67527H13.8639C13.8639 3.16138 12.1618 2.27224 10.9193 3.13705L12.2903 5.10691ZM5.82409 9.19809C6.20687 9.19809 6.58048 9.08087 6.89467 8.8622L5.52366 6.89234C5.61183 6.83098 5.71667 6.79809 5.82409 6.79809V9.19809ZM3.19805 8.67219C3.19805 8.96263 2.96259 9.19809 2.67215 9.19809V6.79809C1.63711 6.79809 0.798047 7.63715 0.798047 8.67219H3.19805ZM2.67215 14.7987C2.96259 14.7987 3.19805 15.0342 3.19805 15.3246H0.798047C0.798047 16.3597 1.63711 17.1987 2.67215 17.1987V14.7987ZM5.81907 17.1987C5.71165 17.1987 5.60681 17.1658 5.51865 17.1045L6.88965 15.1346C6.57547 14.9159 6.20186 14.7987 5.81907 14.7987V17.1987Z" fill="white"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M23.7628 9.59824C24.2048 9.15622 24.2048 8.43956 23.7628 7.99753C23.3208 7.55551 22.6041 7.55551 22.1621 7.99753L19.761 10.3986L17.3604 7.99792C16.9184 7.55589 16.2017 7.55589 15.7597 7.99792C15.3177 8.43994 15.3177 9.1566 15.7597 9.59862L18.1603 11.9993L15.7593 14.4004C15.3172 14.8424 15.3172 15.559 15.7593 16.0011C16.2013 16.4431 16.9179 16.4431 17.36 16.0011L19.761 13.6L22.1625 16.0014C22.6045 16.4435 23.3212 16.4435 23.7632 16.0014C24.2052 15.5594 24.2052 14.8428 23.7632 14.4007L21.3617 11.9993L23.7628 9.59824Z" fill="white"/>
        </svg>

        <svg v-show="!videoMute" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12.2727 19.8783L12.9582 18.8934L12.2727 19.8783ZM6.87212 16.1195L6.18662 17.1045L6.87212 16.1195ZM6.48704 14.7987H3.34011V17.1987H6.48704V14.7987ZM3.86602 15.3246V8.67219H1.46602V15.3246H3.86602ZM3.34011 9.19809H6.49205V6.79809H3.34011V9.19809ZM7.56264 8.8622L12.9582 5.10691L11.5872 3.13705L6.19163 6.89234L7.56264 8.8622ZM12.1319 4.67527V19.325H14.5319V4.67527H12.1319ZM12.9582 18.8934L7.55762 15.1346L6.18662 17.1045L11.5872 20.8632L12.9582 18.8934ZM12.1319 19.325C12.1319 18.9002 12.6096 18.6507 12.9582 18.8934L11.5872 20.8632C12.8298 21.7281 14.5319 20.8389 14.5319 19.325H12.1319ZM12.9582 5.10691C12.6096 5.34959 12.1319 5.10009 12.1319 4.67527H14.5319C14.5319 3.16138 12.8298 2.27224 11.5872 3.13705L12.9582 5.10691ZM6.49205 9.19809C6.87484 9.19809 7.24845 9.08087 7.56264 8.8622L6.19163 6.89234C6.2798 6.83098 6.38464 6.79809 6.49205 6.79809V9.19809ZM3.86602 8.67219C3.86602 8.96263 3.63056 9.19809 3.34011 9.19809V6.79809C2.30508 6.79809 1.46602 7.63715 1.46602 8.67219H3.86602ZM3.34011 14.7987C3.63056 14.7987 3.86602 15.0342 3.86602 15.3246H1.46602C1.46602 16.3597 2.30508 17.1987 3.34011 17.1987V14.7987ZM6.48704 17.1987C6.37962 17.1987 6.27478 17.1658 6.18662 17.1045L7.55762 15.1346C7.24344 14.9159 6.86983 14.7987 6.48704 14.7987V17.1987Z" fill="white"/>
          <path d="M18.2422 16.4853C20.5853 14.1421 20.5853 10.3431 18.2422 8" stroke="white" stroke-width="2.66667" stroke-linecap="round"/>
        </svg>
      </p>
      <p
        v-show="!isPlayDefault"
        class="next"
        @click="handleVideoNext"
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15.2211 11.6802C15.6214 11.9684 15.6214 12.5642 15.2211 12.8524L5.14234 20.1091C4.6646 20.4531 3.99812 20.1117 3.99812 19.523L3.99812 5.00956C3.99812 4.42087 4.6646 4.07948 5.14234 4.42345L15.2211 11.6802Z" stroke="white" stroke-width="2.66667"/>
          <path d="M17.3496 4.66634C17.3496 3.92996 17.9466 3.33301 18.6829 3.33301C19.4193 3.33301 20.0163 3.92996 20.0163 4.66634L20.0163 19.333C20.0163 20.0694 19.4193 20.6663 18.6829 20.6663C17.9466 20.6663 17.3496 20.0694 17.3496 19.333L17.3496 4.66634Z" fill="white"/>
        </svg>
      </p>
      <p v-show="!isPlayDefault" class="playpause" @click.stop="handleVideoPause">
        <svg v-show="!videoPaused" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16 4.99968C16 4.0792 16.7462 3.33301 17.6667 3.33301C18.5871 3.33301 19.3333 4.0792 19.3333 4.99967L19.3333 18.9997C19.3333 19.9201 18.5871 20.6663 17.6667 20.6663C16.7462 20.6663 16 19.9201 16 18.9997L16 4.99968Z" fill="white"/>
          <path d="M5.33398 4.99968C5.33398 4.0792 6.08018 3.33301 7.00065 3.33301C7.92113 3.33301 8.66732 4.0792 8.66732 4.99967L8.66732 18.9997C8.66732 19.9201 7.92113 20.6663 7.00065 20.6663C6.08018 20.6663 5.33398 19.9201 5.33398 18.9997L5.33398 4.99968Z" fill="white"/>
        </svg>
        <svg v-show="videoPaused" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19.0963 11.9508C19.5398 12.27 19.5397 12.93 19.0963 13.2492L7.93216 21.2874C7.40297 21.6685 6.66471 21.2903 6.66471 20.6382L6.66471 4.56179C6.66471 3.90971 7.40297 3.53155 7.93216 3.91256L19.0963 11.9508Z" stroke="white" stroke-width="2.95385"/>
        </svg>
      </p>
    </div>
    <div class="right">
      <p
        v-show="!isPlayDefault"
        class="fullscreen"
        @click="handleClickFullscreen"
        v-log="{
          event_type: 'click',
          event_name: '6001',
          event_data: {
            str1: '首页',
            str2: '小窗 MV',
            str3: '全屏icon',
            str4: 'click',
          }
        }"
      >
        <svg width="24" height="24" viewBox="0   0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M5.61338 3.33301H3.5549C3.06398 3.33301 2.66602 3.73098 2.66602 4.2219V6.76158" stroke="white" stroke-width="2.66667" stroke-linecap="round"/>
          <path d="M5.61338 20.666H3.5549C3.06398 20.666 2.66602 20.268 2.66602 19.7771V17.2374" stroke="white" stroke-width="2.66667" stroke-linecap="round"/>
          <path d="M18.3847 3.33301H20.4431C20.9341 3.33301 21.332 3.73098 21.332 4.2219V6.76158" stroke="white" stroke-width="2.66667" stroke-linecap="round"/>
          <path d="M18.3847 20.666H20.4431C20.9341 20.666 21.332 20.268 21.332 19.7771V17.2374" stroke="white" stroke-width="2.66667" stroke-linecap="round"/>
        </svg>
      </p>
      <p class="order" v-show="isPlayDefault" @click.stop="handleClickDefaultButton">
        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.8">
          <mask id="mask0_1476_51470" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="2" y="1" width="22" height="22">
          <rect x="2" y="1.33301" width="21.3333" height="21.3333" fill="#D9D9D9"/>
          </mask>
          <g mask="url(#mask0_1476_51470)">
          <circle cx="15.7249" cy="9.3333" r="5.33333" stroke="white" stroke-width="1.93939"/>
          <path d="M10.2852 10.6016L5.26298 17.3037C4.78664 17.9393 4.85002 18.8285 5.41171 19.3902V19.3902C5.97339 19.9519 6.86255 20.0153 7.49823 19.5389L14.2003 14.5167" stroke="white" stroke-width="1.93939"/>
          </g>
          </g>
        </svg>
        就唱这首
      </p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import { sendLog } from '@/directives/v-log/log'
import useSongItem from '@/composables/useSongItem'
import { debounce } from 'lodash'
import eventBus from '@/utils/event-bus'
import { TSMediaInstance } from '@/packages/TSJsbridge';
import get from 'lodash/get'
import useVip from '@/composables/useVip'
import useDownload from '@/composables/useDownload';

const store = useStore();
const { orderSong } = useSongItem()
const { showVipQrcode, isVipUser } = useVip()
const { getIsLocalSong } = useDownload();

const isPlayDefault = computed(() => !store.state.orderedList.length);
const playingSong = computed(() => get(store.state, 'orderedList[0]', {}));
const isLogin = computed(() => !!store.state.userInfo.unionid)
const userType = computed(() => store.state.userInfo.userType)
const defaultSong = computed(() => store.state.miniMv.defaultSong);
const videoPaused = computed(() => store.state.videoPaused);
const videoMute = computed(() => store.state.videoMute);
const videoVolume = computed(() => store.state.videoVolume);

const checkMvCanPlay = async () => {
  if (!isVipUser.value) {
    const isLocal = await getIsLocalSong(playingSong.value)
    if (playingSong.value.is_vip && !isLocal) {
      showVipQrcode()
      console.log('checkMvCanPlay 禁止执行后续')
      return false
    }
  }
  return true
}

const handleClickMute = async () => {
  sendLog({
    event_type: 'click',
    event_name: '6001',
    event_data: {
      str1: '首页',
      str2: '小窗 MV',
      str3: '静音状态',
      str4: 'click',
      str5: videoMute.value ? 2 : 1
    }
  })
  await store.commit('SET_VIDEO_MUTE', !videoMute.value)
  TSMediaInstance.setMute(videoMute.value)
  if (!videoMute.value) {
    TSMediaInstance.setStreamVolume(videoVolume.value);
  }
}

const handleVideoNext = debounce(() => {
  sendLog({
    event_type: 'click',
    event_name: '6001',
    event_data: {
      str1: '首页',
      str2: '小窗 MV',
      str3: '切歌',
      str4: 'click',
      str5: playingSong.value.songid,
    }
  })
  eventBus.emit('handle-video-next', 'bottom-bar')
}, 1000, { leading: true, trailing: false })

const handleClickDefaultButton = async () => {
  sendLog({
    event_type: '10000~50000',
    event_name: 30242,
    event_data: {
      str1: '首页',
      str2: '就唱这首',
      str3: '点击',
      str4: 'click',
      str5: isLogin.value ? '已登录' : '未登录',
      str7: userType.value,
    },
  })
  
  orderSong(defaultSong.value.song, {
    immediate: true,
    orderLog: {
      str1: '首页',
      str2: '小窗 MV',
      str3: '就唱这首',
    },
    vipLog: {
      str1: '首页',
      str2: '小窗MV',
      str3: '就唱这首（VIP）',
    },
    fr: [1815, 1816],
  })
}

const handleClickFullscreen = async () => {
  const canPlay = await checkMvCanPlay()

  console.log('handleClickFullscreen', canPlay)

  if (!canPlay) return

  store.commit('UPDATE_MV_ISHIDE', false)
}

const handleVideoPause = async () => {
  const canPlay = await checkMvCanPlay()

  if (!canPlay) return

  try {
    const isPlaying = TSMediaInstance.getIsPlaying()
    if (isPlaying) {
      eventBus.emit('handle-video-pause')
    } else {
      eventBus.emit('video-control-resume')
    }
    sendLog({
      event_type: 'click',
      event_name: 10096,
      event_data: {
        str1: '首页',
        str2: '小窗 MV',
        str3: '播控',
        str4: 'click',
        str5: playingSong.value.songid,
        str6: isPlaying ? 2 : 1,
      },
    })
  } catch (error) {
    console.error('handleVideoPause error', error)
  }
}

</script>

<style scoped lang="stylus">
.mvmini-control
  position absolute
  width 100%
  height auto
  left 0
  top 16px
  padding 0 16px
  z-index 100

  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    padding 0 calc(16px * var(--scale)) !important
    top calc(16px * var(--scale)) !important
  .order
    width 177px

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      font-size var(--font-size-medium)
      width fit-content !important
      padding 0 calc(14px * var(--scale)) !important
  p
    width 87px
    height 63px
    background: rgba(31, 31, 32, 0.3);
    backdrop-filter: blur(120px);
    color #FFFFFF
    display flex
    align-items center
    justify-content center
    border-radius 18px
    border 2px solid var(--border-color)

    &:nth-child(2)
      margin 0 12px

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        margin 0 calc(12px * var(--scale)) !important
    svg
      width 36px
      height 36px

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      font-size var(--font-size-small)
      width calc(58px * var(--scale)) !important
      height calc(42px * var(--scale)) !important
      border-radius calc(12px * var(--scale)) !important

      svg
        width calc(24px * var(--scale)) !important
        height calc(24px * var(--scale)) !important
</style>
