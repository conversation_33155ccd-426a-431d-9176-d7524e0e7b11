<template>
  <div
    v-if="currentSize === 'small'"
    class="user-small flex-column"
    :class="{ 'active-vip': isVip }"
    @click.stop="handleGoMine"
  >
    <div
      class="avatar"
    >
      <img :src="userInfo.avatar" alt="">
      <img v-if="isVip" class="icon" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" alt="">
      <img v-else class="icon" src="https://qncweb.ktvsky.com/20250109/other/4b26d6a2cccd3b88e860da9b50d62fa4.png" alt="">
    </div>
    <h3 class="ellipsis bold">{{ userInfo.username }}</h3>
    <p>{{ isVip ? `会员有效期至${ end_time.replace(/-/g, '.') }` : vipInfo.expire ? '您的会员已过期' : '未开通VIP' }}</p>
  </div>
  <div
    v-else
    class="user"
    :class="{ 
      'active-vip': isVip,
      'active-not-vip': !isVip,
   }"
    @click="handleGoMine"
  >
    <div class="bg">
      <DynamicIcon v-if="isVip" name="navlist-user-vip" />
      <DynamicIcon v-else name="navlist-user" />
    </div>
    <div
      class="user-top"
      v-log="{
        event_type: 'click',
        event_name: '6001',
        event_data: {
          str1: '首页',
          str2: '我的',
          str3: '我的',
          str4: 'click',
        }
      }"
    >
      <div
        class="avatar" 
        :style="{
          backgroundImage: `url(${userInfo?.avatar || 'https://qncweb.ktvsky.com/20230424/vadd/608c9e8b386f6493599d2cc79ba361d4.png'})`
        }"
      >
        <img v-if="isVip" class="vip-icon" src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" alt="">
        <img v-else class="vip-icon" src="https://qncweb.ktvsky.com/20250109/other/4b26d6a2cccd3b88e860da9b50d62fa4.png" alt="">
      </div>
      <img v-show="isVip"  class="icon" src="./imgs/vip.png" alt="">
      <img v-show="!isVip" class="icon" src="./imgs/not-vip.png" alt="">
      <div class="user-name">
        <p class="ellipsis bold">{{ userInfo.username }}</p>
        <p>{{ isVip ? `会员有效期至：${ end_time }` : vipInfo.expire ? '您的会员已过期' : '未开通VIP' }}</p>
      </div>
    </div>
    <div
      class="user-bottom flex-between"
      @click.stop="handleOpenVip"
      v-log="{
        event_type: 'click',
        event_name: '6001',
        event_data: {
          str1: '首页',
          str2: '我的',
          str3: '小卡片运营位',
          str4: 'click',
          str5: userType
        }
      }"
    >
      <div class="vipicon">
        <img src="https://qncweb.ktvsky.com/20250208/vadd/8cb91c0584f5ee021a71c058f2690d6c.png"/>
      </div>
      <div v-if="isVip" class="user-bottom-left">
        <h3 v-if="isVip">续费低至<span>0.2</span><i>/天</i></h3>
        <p>回馈老用户，感谢您一路支持</p>
        <DynamicIcon class="svg-icon" name="right-arrow" />
      </div>
      <div v-else class="user-bottom-left">
        <h3 :style="{'font-size': !isVip ? '14px' : '20px'}">您有一张VIP卡待领取</h3>
        <p><span class="not-vip bold">8</span>项会员权益，尽享奢华体验</p>
        <DynamicIcon class="svg-icon" name="right-arrow" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch, defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import useLoginValid from '@/composables/useLoginValid'
import useVip from '@/composables/useVip'
import { sendLog } from '@/directives/v-log/log'
import useQRCode from '@/composables/useQRCode'
import { getCarplayInfo } from '@/service/carplay-info'
import { withTimeoutHandling } from '@/utils/promiseUtils'
import { vipLogFrom } from '@/constants/index'

defineProps({
  currentSize: String
})

const router = useRouter()
const store = useStore()
const { showLoginQrcode, isLogin } = useLoginValid()
const { showVipQrcode } = useVip()

const userInfo = computed(() => store.state.userInfo)
const vipInfo = computed(() => store.state.vipInfo)
const isVip = computed(() => !!vipInfo.value.end_time)
const net_status = computed(() => store.state.base.net_status)
const end_time = computed(() => (isVip.value ? vipInfo.value.end_time.split(' ')[0] : ''))
const { getQRCodeURL } = useQRCode()
const carplayInfo = computed(() => store.state.carplayInfo)
const userType = computed(() => store.state.userType)

let qrCodeURL = ref('')
let isRequest = ref(false)

const getLoginQrcode = async (payload) => {
  try {
    if (isRequest.value || !net_status.value && payload !== 'reload') return;

    isRequest.value = true
    
    let pay_qr
    if (payload === 'reload') {
      pay_qr = (await getCarplayInfo(true)).data.pay_qr
    } else {
      pay_qr = carplayInfo.value.pay_qr || (await withTimeoutHandling(getCarplayInfo())).data.pay_qr
    }
    if (pay_qr) {
      const qrCodeData = await withTimeoutHandling(getQRCodeURL(`${pay_qr}&fr=mine`))
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData
      }
    }
  } catch (error) {
    console.log('getLoginQrcode error', error)
  } finally {
    isRequest.value = false
  }
}


const handleGoMine = () => {
  sendLog({
    event_type: 'click',
    event_name: 210,
  })
  if (!isLogin.value) {
    showLoginQrcode({
      log: '我的入口-头像',
    })
    return
  }
  router.push({
    name: 'mine',
  })
  sendLog({
    event_type: '10000~50000',
    event_name: 10010,
    event_data: {
      str1: '首页',
      str2: '我的入口',
      str3: '进入我的',
      str4: 'click',
    },
  })
}

const handleOpenVip = () => {
  showVipQrcode({
    fr: vipLogFrom.get('首页-我的-用户卡片运营'),
    logData: {
      str1: '首页',
      str2: '我的-用户卡片运营',
      str4: 'click'
    },
    isLogin: isLogin.value,
  })

  sendLog({
    event_type: 'click',
    event_name: '6001',
    event_data: {
      str1: '首页',
      str2: '我的',
      str3: '小卡片运营位',
      str4: 'click',
      str5: userType.value
    }
  })
}
watch(isLogin, (val) => {
  if (!val) {
    getLoginQrcode()
  }
}, { immediate: true })

watch(net_status, (val) => {
  if (val) {
    getLoginQrcode()
  }
}, {
  immediate: true,
  deep: true
})

onMounted(() => {
  if (!isLogin.value) {
    getLoginQrcode()
    console.log(net_status.value, '1108')
  }
})
</script>

<style lang="stylus" scoped>
.user-small
  height 100%
  text-align center
  align-items center
  padding-top calc(18px * 3)
  .avatar
    width calc(60px * 3)
    height calc(60px * 3)
    position relative
    img:first-child
      border-radius 50%
      overflow hidden
    .vip-icon
      position absolute
      width calc(26px * 3)
      bottom calc(-5px * 3)
      left 50%
      margin-left calc(-13px * 3)
      top unset !important
    .icon
      position absolute
      width calc(26px * 3)
      bottom calc(-5px * 3)
      left 50%
      margin-left calc(-13px * 3)
      top unset !important
  h3
    // font-size var(--font-size-extra-large)
    color: #FFFFFF;
    margin calc(10px * 3) 0 calc(4px * 3)
    max-width 96%
  p
    font-size var(--font-size-small)
    color: #FFFFFF80;

  &.active-vip
    h3
      color #4E350D;
    p
      color: #6D5016;

.dark-theme .user{
  --background-color: rgba(255, 255, 255, 0.1);
  --button-background rgba(103, 0, 186, 1)
  --highlight-color rgba(255, 193, 46, 1)
}
.user
  --background-color: #fff;
  --button-background rgba(160, 74, 240, 1)
  --highlight-color rgba(255, 20, 36, 1)
  width 445px
  heigh 100%
  border-radius 28px
  position relative
  &:after
    content ""
    position absolute
    z-index 1
    width  100%
    height 70%
    bottom 0
    left 0
    border-radius 32px
    background var(--background-color)
  & > div
    position relative
    z-index 3
  .bg
    position absolute
    left 0px
    top 0px
    z-index 2
    overflow hidden
    .svg-icon
      width 445px
      height 315px

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(273px * var(--scale)) !important
        height calc(192px * var(--scale)) !important
  &-top
    height 315px
    border-radius 32px
    padding 32px 0 0 32px
    .icon
      width 157px
      position absolute
      top 0px
      right 34px
      filter: grayscale(80%);

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(96px * var(--scale)) !important

    .avatar
      width 108px
      height 108px
      border-radius 50%
      margin-bottom 60px
      background-size 100%
      position relative
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(66px * var(--scale)) !important
        height calc(66px * var(--scale)) !important
        margin calc(6px * var(--scale)) 0 calc(37px * var(--scale)) calc(6px * var(--scale)) !important
      img
        position absolute
        width calc(32px * var(--scale))
        bottom calc(-8px * var(--scale))
        left 50%
        margin-left calc(-16px * var(--scale))
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(29px * var(--scale)) !important
          height calc(15px * var(--scale)) !important
          left 45%
          bottom calc(-8px * var(--scale)) !important
  &-name
    max-width 80%
    p
      line-height 42px
      font-size var(--font-size-extra-large)
      color #fff
    p:first-child
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        height calc(27px * var(--scale)) !important
        line-height calc(27px * var(--scale)) !important
        font-size calc(20px * var(--scale)) !important
        margin-left calc(6px * var(--scale)) !important
    p:last-child
      font-size var(--font-size-extra-small)
      margin-top 8px
      line-height 28px
      opacity 0.5
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        font-size calc(14px * var(--scale)) !important
        margin-top calc(8px * var(--scale)) !important
        margin-left calc(6px * var(--scale)) !important
  &-right
    background #fff
    border-radius 6px
    position absolute
    top 32px
    right 32px
    width 170px
    height 170px
    img 
      width 160px
      height 160px
  &-bottom
    height 104px
    padding 0 calc(50px * var(--scale)) 0 calc(20px * var(--scale))
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      margin-top calc(70px * var(--scale))
      padding 0 calc(40px * var(--scale)) 0 calc(11px * var(--scale))!important
    h3
      // font-size 26px
      font-size var(--font-size-medium)
      color var(--text-primary-color)
    p
      font-size var(--font-size-small)
      color var(--text-secondary-color)
      span
        color var(--highlight-color)
        font-size 20px
        margin-right 6px
    .vipicon
      width calc(54px * 1.48)
      height calc(44px * 1.48)
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(54px * 1.48)!important
        height calc(44px * 1.48)!important
    .svg-icon
      position absolute
      right calc(20px * var(--scale))
      top 50%
      transform translateY(-50%)
      width calc(12px * var(--scale))
      height calc(12px * var(--scale))
      color var(--text-secondary-color)
  
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(12px * var(--scale)) !important
        height calc(12px * var(--scale)) !important
    button
      background var(--button-background)
      font-size var(--font-size-extra-small)
      width 120px
      height 64px
      border-radius 20px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width  calc(70px * var(--scale))  !important
        height calc(36px * var(--scale)) !important
        border-radius calc(9px * var(--scale)) !important
.active-vip
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width  calc(273px * var(--scale))  !important
    height calc(258px * var(--scale)) !important
    // --background-color none  !important
    // // background url(https://qncweb.ktvsky.com/20250109/vadd/7d6e280a54c256a7216a70d5a4045ab6.png) no-repeat
    // background-size 100% 100% !important
    // margin-top calc(101px * var(--scale)) !important
  .icon
    filter: grayscale(0%);
  .avatar
    img
      filter: grayscale(0%);
  .user-name
    p
      color rgba(78, 53, 13, 1)
  .user-bottom
    h3
      span
        font-size var(--font-size-medium)
        color var(--highlight-color)
      i
        color var(--highlight-color)
        font-size 18px
        margin-left 6px
.active-not-vip
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width  calc(273px * var(--scale))  !important
    height calc(258px * var(--scale)) !important
    .not-vip
      color  var(--text-secondary-color) !important
      font-size calc(12px * var(--scale)) !important
</style>