<template>
  <div v-if="currentSize === 'small'" class="unlogin-small flex-column">
    <!-- <DynamicIcon name="navlist-unlogin-avatar" />
    <h3>未登录</h3> -->
    <div class="code flex-center">
      <img v-if="net_status" :src="qrCodeURL">
      <NetworkError
        v-else
        @refresh="getLoginQrcode('reload')"
        class="network-error-wrapper"
      />
    </div>
    <p>登录解锁更多特权</p>
  </div>
  <div v-else class="unlogin flex-column">
    <div class="unlogin-top">
      <div class="unlogin-left">
        <DynamicIcon v-if="isLogin" name="navlist-unlogin-avatar" />
        <div v-if="!isLogin" class="unlogin-left-img">
          <!-- <DynamicIcon name="img-user-notlog" /> -->
        </div>
        <p class="first">扫码登录</p>
        <p>手机扫码登录解锁更多特权</p>
      </div>
      <div class="unlogin-right flex-center">
        <img v-if="net_status" :src="qrCodeURL">
        <NetworkError
          v-else
          @refresh="getLoginQrcode('reload')"
          class="network-error-wrapper"
        />
        <!-- <div v-else class="net-error" @click="getLoginQrcode('reload')">
          <p>网络异常</p>
          <p>点击刷新二维码</p>
        </div> -->
      </div>
    </div>
  <div
    v-log="{
      event_type: 'show',
      event_name: '1021',
      event_data: {
        str1: '首页',
        str2: '我的',
        str3: '扫码登录',
        str4: 'show',
        str5: '未登录',
        str9: '手机端',
        str10: 1851,
      }
    }"
    class="unlogin-bottom flex-column"
  >
    <!-- <p>解锁点歌特权</p>
    <p>经典专属歌单</p>
    <p>VIP限时优享0.2元/天</p> -->
    <img :src="bgImage"/>
  </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch, defineProps } from 'vue'
import { useStore } from 'vuex'
import useLoginValid from '@/composables/useLoginValid'
import useQRCode from '@/composables/useQRCode'
import { getCarplayInfo } from '@/service/carplay-info'
import { withTimeoutHandling } from '@/utils/promiseUtils'
import NetworkError from '@/components/network-error/NetworkError.vue'

defineProps({
  currentSize: String
})

const store = useStore()
const { isLogin } = useLoginValid()

const net_status = computed(() => store.state.base.net_status)
const { getQRCodeURL } = useQRCode()
const carplayInfo = computed(() => store.state.carplayInfo)
const themeClass = computed(() => store.state.themeClass)
const bgImage = computed(() => {
  return themeClass.value === 'themeLight' 
    ? 'https://qncweb.ktvsky.com/20250208/vadd/b88c64c686fb003b2c65cf953a82c1ae.png'
    : 'https://qncweb.ktvsky.com/20250211/vadd/66feace74b54467601596590564eec19.png'
})

let qrCodeURL = ref('')
let isRequest = ref(false)

const getLoginQrcode = async (payload) => {
  try {
    if (isRequest.value || !net_status.value && payload !== 'reload') return;

    isRequest.value = true
    
    let pay_qr
    if (payload === 'reload') {
      pay_qr = (await getCarplayInfo(true)).data.pay_qr
    } else {
      pay_qr = carplayInfo.value.pay_qr || (await withTimeoutHandling(getCarplayInfo())).data.pay_qr
    }
    if (pay_qr) {
      const qrCodeData = await getQRCodeURL(`${pay_qr}&log=1851`)
      if (qrCodeData) {
        qrCodeURL.value = qrCodeData
      }
    }
  } catch (error) {
    console.log('getLoginQrcode error', error)
  } finally {
    isRequest.value = false
  }
}

watch(isLogin, (val) => {
  if (!val) {
    getLoginQrcode()
  }
}, { immediate: true })

watch(net_status, (val) => {
  if (val) {
    getLoginQrcode()
  }
}, {
  immediate: true,
  deep: true
})

onMounted(() => {
  if (!isLogin.value) {
    getLoginQrcode()
    console.log(net_status.value, '1108')
  }
})
</script>

<style lang="stylus" scoped>
.unlogin-small
  background url('https://qncweb.ktvsky.com/20250212/vadd/dfbe0dfec1b7bcc329d325f67e47bfdf.png') no-repeat
  background-size 100%
  height 100%
  text-align center
  align-items center
  padding-top calc(11px * 3)
  .code
    width calc(110px * 3)
    height calc(110px * 3)
    border-radius calc(8px * 3)
    background #fff
    img
      width calc(100px * 3)
      height calc(100px * 3)
  p
    font-size var(--font-size-small)
    color: #FFFFFF;
    margin-top calc(4px * 3)

.unlogin
  width 445px
  height 420px
  backdrop-filter: blur(40px)
  background: var(--card-bg-color)
  border-radius 28px
  position relative

  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    width  calc(273px * var(--scale))  !important
    height calc(258px * var(--scale)) !important
    margin-top 0 !important
    border-radius calc(16px * 1.48) !important
  &-top
    //background: linear-gradient(113.9deg, #8948F2 0%, #851EE1 97.59%);
    background: linear-gradient(113.9deg, #9259EE 0%, #8B2FDE 97.59%);
    height 291px
    border-radius 28px
    padding 32px 0 0 32px
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      padding calc(13px * var(--scale)) calc(22px * var(--scale)) 0 calc(22px * var(--scale)) !important
      border-radius calc(16px * 1.48) !important
    .svg-icon
      width 108px 
      height 108px
      margin-bottom 45px
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width  calc(273px * var(--scale)) !important
      height calc(179px * var(--scale))  !important
  &-left 
    p
      line-height 42px
      color #fff
    p.first
      font-size var(--font-size-extra-large)
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        font-size calc(20px * var(--scale)) !important
    p:last-child
      font-size var(--font-size-extra-small)
      opacity 0.6
      margin-top 8px
      line-height 28px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        margin-top calc(11px * var(--scale)) !important
    &-img
      width calc(72px * var(--scale))
      height calc(72px * var(--scale))
      border-radius 50%
      margin 0 0 calc(25px * var(--scale))  0
      background url(https://qncweb.ktvsky.com/20250516/other/4cdfa2ba9fee19235bb8c5b342128676.png) no-repeat
      background-size 100% 100% !important
      img
        width 100%
        height 100%
      // background url(https://qncweb.ktvsky.com/20250109/vadd/b14f07aa0a4d0cb031472f8b3f59d008.png) no-repeat
      // background-size 100% 100% !important
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(66px * 1.48) !important
        height calc(66px * 1.48) !important
        margin calc(13px * 1.48) 0 calc(30px * 1.48) 0 !important
        .svg-icon
          width calc(66px * 1.48) !important
          height calc(66px * 1.48) !important
  &-right
    background #fff
    border-radius 6px
    position absolute
    top 32px
    right 32px
    width 170px
    height 170px
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width  calc(104px * var(--scale)) !important
      height calc(104px * var(--scale)) !important
      top calc(26px * var(--scale)) !important
      right calc(22px * var(--scale)) !important
    img 
      width 160px
      height 160px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width  calc(95px * var(--scale)) !important
        height calc(95px * var(--scale)) !important
  &-bottom
    padding-top 10px
    flex 1
    justify-content center
    img
      width 90%
      margin-left calc(20px * var(--scale))
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        margin-left calc(20px * var(--scale))!important
    p
      color var(--text-secondary-color)
      font-size var(--font-size-extra-small)
      padding-left 44px
      position relative
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        font-size var(--font-size-small) !important
        padding-left calc(24px * 1.48) !important
        color #7E8196 !important
      &:after
        content ""
        width 8px
        height 8px
        background var(--text-secondary-color)
        border-radius 100%
        position absolute
        left 28px
        top 50%
        margin-top -4px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(5px * 1.48) !important
          height calc(5px * 1.48) !important
          background #D9DEF1 !important
          top 40% !important
</style>