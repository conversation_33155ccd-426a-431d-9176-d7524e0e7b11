<template>
  <div class="progress-container">
    <div class="progress-bar"></div>
  </div>
</template>

<style scoped lang="stylus">
.progress-container {
  width: 100vw;
  height: 6px;
  background-color: transprent;
  overflow: hidden;
  position: fixed;
  z-index: 10000;
  top: 0;
  left: 0;

  .progress-bar {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #42b983 0%, #42b983 50%, transparent 50%, #42b983 100%);
    background-size: 200% 100%;
    animation: move 2s linear infinite;
  }
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>