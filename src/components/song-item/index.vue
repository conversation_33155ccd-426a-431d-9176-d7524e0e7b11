<template>
  <div
    class="song-item flex-between"
    :class="[
      renderType,
      {
        ordered: isOrdered,
        noPadding: noPadding,
        active: isActive,
        'first-song-item': index === 0,
      }
    ]"
    @click="handleOrder({
      immediate: !hasOrderedSongs,
      orderArea: 'song-item'
    })"
  >
    <div 
      class="left"
    >
      <div class="name ellipsis">
        {{ songItem.music_name }}
      </div>
      <div class="desc flex">
        <div 
          v-if="songItem.singer" 
          @click.stop.prevent="handleClickSinger"
          class="author flex" 
          :class="{ 
            'has-sing-cnt': songItem.sing_cnt,
            'exceeded': renderType.includes('block') && songItem.singer.length > 3
          }" 
        >
          <span class="ellipsis">
            {{ renderType.includes('block') ? formattedSingerName: formattedSingerNamelist }}
          </span>
          <DynamicIcon v-show="singerCanClick" name="right-arrow" />
        </div>
        <div class="center flex">
          <span v-show="showFlag" class="album flag">
            {{ songItem.flag && songItem.flag.toString() }}
          </span>
          <template v-if="!(songItem.sing_cnt && currentSize == 'small')">
            <img 
              v-if="songItem.is_vip" 
              class="vip" 
              src="https://qncweb.ktvsky.com/20231206/vadd/56dc0bc6477bf2c7a6c4fcdc8360804e.png" 
              alt=""
            >
            <img 
              v-if="enableScoring && songItem.is_score" 
              class="song-block-score" 
              src="https://qncweb.ktvsky.com/20240205/other/151412e04f9df9e6bbc1a781ddd6ab7d.png" 
              alt=""
            />
            <span
              v-if="showXiaomiFree"
              class="xiaomi flex-center"
            >
              <img src="https://qncweb.ktvsky.com/20241231/other/b66b6a18dd20b2425d2643dff4f3db5e.png" />
            </span>
          </template>
        </div>
        <span v-if="songItem.sing_cnt" class="sing-cnt">
          演唱<span>{{ songItem.sing_cnt > 99 ? '99+' : songItem.sing_cnt }}</span>次
        </span>
      </div>
    </div>
    <div v-show="renderType !== 'block'" class="right flex">
      <div
        @click.stop.prevent="handleOrder({
          immediate: true,
          isOpenFullScreen: true
        })"
        v-log="{
          event_type: 'click',
          event_name: '6012',
          event_data: {
            str1: '通用',
            str2: '歌曲',
            str3: '立即开唱',
            str4: 'click',
          }
        }"
      >
        <DynamicIcon name="order1" />
      </div>
      <div @click.stop="handleOrder({ immediate: !hasOrderedSongs })"><DynamicIcon name="order2" /></div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, toRefs, ref } from 'vue'
import { useStore } from 'vuex'
import useSongItem from '@/composables/useSongItem'
import { sendLog } from '@/directives/v-log/log'
import { useRouter, useRoute } from 'vue-router'
import config from '@/config'
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  className: String,
  songItem: Object,
  index: Number,
  startPosition: { type: Number, default: 0 },
  singerEnable: { type: Boolean, default: true },
  renderType: { type: String, default: 'list' },
  isStickButton: { type: Boolean, default: false },
  noPadding: { type: Boolean, default: false },
  orderLog: Object,
  logData: Object,
  from: String,
})

const emit = defineEmits(['singer-click'])

const router = useRouter()
const route = useRoute()
const { songItem, singerEnable, renderType, orderLog, logData, from } = toRefs(props)
const store = useStore()
const orderedSongIdMap = inject('orderedSongIdMap')
const { orderSong } = useSongItem()

const currentSize = computed(() => store.state.currentSize);
const isOrdered = computed(() => orderedSongIdMap.value[songItem.value.songid])
const mvIsHide = computed(() => store.state.mvIsHide)
const hasOrderedSongs = computed(() => {
  return store.state.orderedList.length > 0;
});
const showXiaomiFree = computed(() => {
  return songItem.value.singerid == 115281
});
const showFlag = computed(() => {
  const { singer = '', is_vip = false, sing_cnt = 0, flag = false } = songItem.value;

  if (currentSize.value === 'small') {
    return true
  }

  return showXiaomiFree.value
    ? !sing_cnt
    : !(is_vip && sing_cnt)
})

const getTruncatedName = (name, maxLength) => {
  return name.length > maxLength ? name.slice(0, maxLength) + '...' : name;
};

const formattedSingerName = computed(() => {
  const { singer = '', is_vip = false, sing_cnt = 0, flag = false } = songItem.value;
  if (!singer || !flag) return singer;

  // 三分之一屏
  if (currentSize.value === 'medium') {
    if (singer.length > 25 && flag) {
      return getTruncatedName(singer, 25);
    }
  }

  if (singer.length > 4 && is_vip && sing_cnt) {
    return getTruncatedName(singer, 4);
  } else if (singer.length > 7 && sing_cnt && flag) {
    return getTruncatedName(singer, sing_cnt > 9 ? 4 : 7);
  }
  return singer;
});

const formattedSingerNamelist = computed(() => {
  const { singer = '', sing_cnt = 0, flag = false, is_vip = false } = songItem.value;
  if (!singer || !flag) return singer;

  // 三分之一屏
  if (currentSize.value === 'small') {
    if (sing_cnt) {
      if (singer.length > 2 && showXiaomiFree.value) {
        return getTruncatedName(singer, 1);
      }
      if (is_vip && singer.length > 3) {
        return getTruncatedName(singer, 2);
      }
      if (singer.length > 5) {
        return getTruncatedName(singer, 5);
      }
    } else if (is_vip && singer.length > 10) {
      return getTruncatedName(singer, 10);
    }
    return singer;
  }

  // 快速点歌
  if (mvIsHide.value === false) {
    if (singer.length > 7 && sing_cnt && flag) {
      return getTruncatedName(singer, 7);
    }
    return getTruncatedName(singer, 15);
  }

  // 三分之一屏
  if (currentSize.value === 'medium') {
    if (singer.length > 25 && flag) {
      return getTruncatedName(singer, 25);
    }
  }

  // 普通歌曲列表
  if (singer.length > 22 && sing_cnt && flag) {
    return getTruncatedName(singer, 22);
  }

  return getTruncatedName(singer, 35);
});

const enableScoring = config.enableScoring
const isActive = ref(false)

const singerCanClick = computed(() => {
  return (
    (mvIsHide.value && !route.query.singerid) ||
    (!mvIsHide.value && singerEnable.value)
  );
});

const handleOrder = (payload) => {
  store.commit('UPDATE_CONTROL_FROM_TYPE', 1);
  const defaultLogData = { orderLog: null, vipLog: null, fr: null };
  const effectiveLogData = logData.value || defaultLogData;
  console.log('handleOrder', effectiveLogData.orderLog)

  orderSong(songItem.value, {
    ...payload,
    orderLog: effectiveLogData.orderLog || orderLog.value,
    vipLog: effectiveLogData.vipLog,
    fr: effectiveLogData.fr,
  });
};

const handleClickSinger = (e) => {
  console.log('handleClickSinger');
  sendLog({
    event_type: '10000~50000',
    event_name: 6012,
    event_data: {
      str1: '通用',
      str2: '歌曲条目点击歌手名称',
      str3: '歌手详情页',
      str4: 'click',
    },
  });
  const singer = songItem.value.singer.split(',')[0];
  if (mvIsHide.value) {
    router.push({
      name: 'songList',
      query: {
        name: singer,
        image: songItem.value.singer_head,
        singerid: songItem.value.singerid,
        from: from.value || '3',
      },
    });
  } else {
    emit('singer-click', {
      singer,
      singerhead: songItem.value.singer_head,
      singerid: songItem.value.singerid,
    });
  }
};
</script>

<style lang="stylus" scoped>
.song-item
  --list-offset: 36px;
  padding 0 var(--list-offset)
  height 123px
  border-radius 8px
  position relative

  &:active
    &:not(:has(.right *:active))
      background-color: var(--pressed-color);
  // 三分之一屏
  @media (max-width: 700px)
    height calc(85px * 3)
    --list-offset: 0px;
  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)  
    height calc(90px * var(--scale)) !important 
  &.first-song-item
    &::before
      opacity 0 !important
  &.active
    & + .song-item
      &::before
        opacity 0
    &::before
      opacity 0
    &:after
      content ""
      position absolute
      width 100%
      height 100% !important
      left 0
      top 0
      background-color: var(--pressed-color);
      border-radius 8px
      
      // 三分之一屏
      @media (max-width: 700px)
        border-radius calc(12px * 3)
   
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)   
        border-radius calc(16px * 3)
  &.noPadding
    padding 0
    &::before
      content ""
      width 100%
      left 0
      // 三分之一屏
      @media (max-width: 700px)
        display none
  &::before
    content: '';
    position: absolute;
    bottom unset
    top 0px
    left: var(--list-offset); /* 左侧偏移量 */
    width: calc(100% - var(--list-offset) * 2); /* 父元素宽度 + 60px + 60px */
    height: 2px;
    background-color: var(--pressed-color);
    
    // 三分之一屏 & 三分之二屏
    @media (max-width: 900px)
      height 1px !important
    // 三分之一屏
    @media (max-width: 700px)
      height 1px
    
  .left
    height 100%
    display flex
    flex-direction column
    justify-content center
          
  .right
    div
      width calc(50px * var(--scale))
      height calc(50px * var(--scale))
      margin-left calc(36px * var(--scale))

      // 三分之一屏
      @media (max-width: 700px)
        width calc(44px * 3)
        height calc(44px * 3)
        margin-left calc(16px * 3)

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)   
        width calc(50px * var(--scale)) !important
        height calc(50px * var(--scale)) !important
        margin-left calc(36px * var(--scale)) !important

      &:active
        background-color: var(--pressed-color);
      &:first-child
        margin-left 0px !important
    .svg-icon
      color var(--song-control-color)
  &.block
    padding 0px 36px
    background var(--song-card-background-color)
    height calc(85px * var(--scale))
    border-radius 16px

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      height calc(85px * var(--scale)) !important
      border-radius calc(12px * var(--scale)) !important
    .left
      width 100%
      max-width 100%
    .name
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        font-size var(--font-size-medium) !important
    .author
      .ellipsis
        max-width 130px
    &::before
      content ""
      display none
      
  &.block, &.block2
    .author
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        max-width 30%
    .exceeded
      .svg-icon
        margin-left 0px
    .sing-cnt
      height calc(18px * var(--scale))
      border-radius 14px
      font-size 15px
      border 1px solid var(--text-secondary-color)
      padding 0 5px
      white-space nowrap
      span
        color #A04AF0
        font-size 15px

      // // 三分之一屏 & 三分之二屏
      // @media (max-width: 838px)
      //   height calc(16px * var(--scale)) !important
      //   line-height calc(14px * var(--scale)) !important
      //   padding 0 calc(6px * var(--scale)) !important
      //   font-size calc(10px * var(--scale)) !important
      //   border-radius calc(16px * var(--scale)) !important
      //   span
      //     font-size calc(10px * var(--scale)) !important

  &.block2
    padding 0
    height calc(77px * var(--scale))
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)   
      height calc(77px * var(--scale)) !important
    .left
      max-width 66%

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        max-width 55%
    .right
      div
        width calc(44px * var(--scale))
        height calc(44px * var(--scale))
        margin-left calc(28px * var(--scale))

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(44px * var(--scale)) !important
          height calc(44px * var(--scale)) !important
          margin-left calc(28px * var(--scale)) !important
    &:before
      content ""
      display none
</style>