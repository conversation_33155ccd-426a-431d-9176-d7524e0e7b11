<template>
  <div v-if="showOffline && mvIsHide" class="offline-container">
    <h1>网络连接已断开</h1>
    <p>请检查你的网络连接并重试。</p>
  </div>
</template>
<script setup>
import { ref, watch, computed, nextTick, defineProps, toRefs } from 'vue'
import { useStore } from 'vuex';
import eventBus from '@/utils/event-bus'

const props = defineProps({
  curRoute: {
    type: Object,
    default: () => ({})
  }
})

const store = useStore();
const { curRoute } = toRefs(props)

const net_status = computed(() => store.state.base.net_status);
const mvIsHide = computed(() => store.state.mvIsHide);

const showOffline = ref(false)

const checkHasSingerDom = async () => {
  await nextTick()
  return !!document.querySelector('.singer-list')
}

watch([net_status, curRoute], async (val) => {
  if (val[0]) {
    if (val[1].name === 'singer' && showOffline.value) {
      if (!await checkHasSingerDom()) {
        eventBus.emit('singer-online')
      }
      showOffline.value = false
    }
  } else {
    if (val[1].name === 'singer') {
      if (!await checkHasSingerDom()) {
        showOffline.value = true
      }
    }
  }
  if (val[1].name !== 'singer') {
    showOffline.value = false
  }
})
</script>
<style lang="stylus" scoped>
.offline-container
  position fixed
  top 45vh
  left 0
  width 100%
  text-align center
  h1
    font-size 32px
    color #1D1D1FE5
  p
    font-size 28px
    color #1D1D1F66
.theme-themeDark
  .offline-container
    color #FFFFFFE5
    p, h1
      color #FFFFFF66
</style>
