/* Skin Style Variables */

/* Default: Light theme */
:root {
  /* Text Colors */
  --text-primary-color: #1D1D1F; /* 主文本颜色 */
  --text-secondary-color: rgba(29,29,31,0.5); /* 副文本颜色 */
  --text-tertiary-color: rgba(29,29,31,0.6); /* 第三文本颜色 */
  --song-name-color: rgba(29,29,31,0.8); /* 歌曲名称颜色 */
  --tab-button-text-color: rgba(29,29,31,0.7); /* Tab按钮文案颜色 */
  --tab-button-text-highlight-color: #684006; /* Tab按钮文案高亮颜色 */

  /* Background Colors */
  --background-primary: #E8EAEE; /* 主要背景色 */
  --highlight-color: #A04AF0; /* 高亮背景色 */
  --top-right-corner-background: #A04AF0; /* 右上角已点背景 */
  --tab-button-background-color: #E8EAEE; /* Tab按钮背景色 */
  --song-card-background-color: #FFFFFF; /* 歌曲卡片背景色 */
  --popup-background-color: #FFFFFF; /* 新增：已点弹窗背景色（浅色） */
  --capsule-background: #E8EAEE; /* 胶囊高亮背景色 */
  --capsule-highlight-background: #A04AF0; /* 胶囊高亮背景色 */
  --capsule-highlight-color: #FFFFFF; /* 胶囊高亮文字色 */
  --bottom-lyric-bar-background: rgba(200, 205, 218, 1); /* 新增：底部歌词栏背景色（浅色） */
  --bottom-lyric-bar-flag-background: linear-gradient(135.3deg, #313137 -0.52%, #3C3C45 100%); /* 新增：底部歌词栏 flag背景色（浅色） */
  --bottom-lyric-bar-flag-color: #F9F8FF; /* 新增：底部歌词栏 flag字体色（浅色） */
  --van-dialog-background-color: rgba(232, 234, 238, 1) !important
  --card-bg-color: #fff
  --card-bg-color2: #1D1D1F14

  /* Other Colors */
  --border-color: rgba(29,29,31,0.1); /* 边框色 */
  --border-color2: rgba(29,29,31,0.2); /* 边框色 */
  --pressed-color: rgba(0,0,0,0.05); /* 按下色 */
  --pressed-color2: rgba(0,0,0,0.05); /* 按下色 */
  --song-control-color: #5500C3; /* 歌曲播控颜色 */
  --close-color: rgba(0, 0, 0, 0.8); /* 关闭按钮颜色 */
}

/* Dark theme overrides */
.dark-theme {
  /* Text Colors */
  --text-primary-color: #fff; /* 主文本颜色 */
  --text-secondary-color: rgba(255,255,255,0.4); /* 副文本颜色 */
  --text-tertiary-color: rgba(255,255,255,0.6); /* 第三文本颜色 */
  --song-name-color: rgba(255,255,255,0.7); /* 歌曲名称颜色 */
  --tab-button-text-color: rgba(255,255,255,0.6); /* Tab按钮文案颜色 */
  --tab-button-text-highlight-color: #fff; /* Tab按钮文案高亮颜色 */

  /* Background Colors */
  --background-primary: #0B0522; /* 主要背景色 */
  --background-secondary: linear-gradient(0deg, #000000, #000000), linear-gradient(180deg, #24222F 0%, rgba(0, 0, 0, 0) 100%); /* 第二种背景色 */
  --highlight-color: #DBAE6A; /* 高亮背景色 */
  --top-right-corner-background: linear-gradient(180deg, #6700BA 0%, #7C00BA 100%); /* 右上角已点背景 */
  --tab-button-background-color: rgba(0,0,0,0.2); /* Tab按钮背景色 */
  --song-card-background-color: rgba(255,255,255,0.1); /* 歌曲卡片背景色 */
  --popup-background-color: #22202C; /* 新增：已点弹窗背景色（深色） */
  --capsule-background: #00000033; /* 胶囊高亮背景色 */
  --capsule-highlight-background: #F8C477; /* 胶囊高亮背景色 */
  --capsule-highlight-color: #684006; /* 胶囊高亮文字色 */
  --bottom-lyric-bar-background: rgba(34, 28, 43, 1); /* 新增：底部歌词栏背景色（深色） */
  --bottom-lyric-bar-flag-background: linear-gradient(135.3deg, #FFFFFF -0.52%, rgba(255, 255, 255, 0.49) 100%); /* 新增：底部歌词栏 flag背景色（深色） */
  --bottom-lyric-bar-flag-color: #231F3C; /* 新增：底部歌词栏 flag字体色（深色） */
  --van-dialog-background-color: rgba(34, 32, 44, 1) !important
  --card-bg-color: rgba(255, 255, 255, 0.08)
  --card-bg-color2: rgba(255, 255, 255, 0.08)

  /* Other Colors */
  --border-color: rgba(255,255,255,0.1); /* 边框色 */
  --border-color2: rgba(255,255,255,0.2); /* 边框色 */
  --pressed-color: rgba(255,255,255,0.1); /* 按下色 */
  --pressed-color2: rgba(255,255,255,0.2); /* 按下色 */
  --song-control-color: #fff; /* 歌曲播控颜色 */
  --close-color: rgba(255, 255, 255, 0.8); /* 关闭按钮颜色 */
}