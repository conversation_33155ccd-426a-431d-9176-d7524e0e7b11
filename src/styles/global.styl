*
  &::-webkit-scrollbar
    display none
  
  // 三分之一屏 & 三分之二屏
  @media (max-width: 900px)
    border-width 4px
    
div, p, span
  color: var(--text-primary-color);
  font-size: var(--font-size-medium);
  // // 三分之二屏
  // @media (max-width: 900px)
  //   font-size: calc(14px * var(--scale)) !important;
.app-main
  height 100vh
  background var(--background-primary)
  & > *
    position relative
    z-index 2
  .bg-circle
    // 三分之一屏
    @media (max-width: 700px)
      display none
  &::before,
  &::after,
  .bg-circle
    content: "";
    position: fixed;
    border-radius: 50%;
    opacity: 0.05;
    filter: blur(90px);
    z-index 1

  &::before
    width: 854px;
    height: 655px;
    top: 200px;
    left: -160px;
    transform: rotate(30.26deg);
    background: linear-gradient(312.71deg, #011AFF 39.32%, #FF0091 84.13%);
    // 三分之一屏
    @media (max-width: 700px)
      width calc(384px * var(--scale))
      height calc(384px * var(--scale))
      top calc(16px * var(--scale))
      left calc(-36px * var(--scale))
      border-radius 100%
      transform: rotate(0deg);

  &::after
    width: 1221px;
    height: 1221px;
    top: 613px;
    right: -142px;
    border-radius: 100%;
    background: linear-gradient(312.71deg, #0185FF 69.12%, #CC00FF 84.13%);
    // 三分之一屏
    @media (max-width: 700px)
      width calc(408px * var(--scale))
      height calc(408px * var(--scale))
      top calc(367px * var(--scale))
      right calc(-204px * var(--scale))
      background: #63238E;

.app-main-lrc
  &:after, &:before
    z-index 3


.dark-theme
  #app
    .bg-circle
      width: 774px;
      height: 774px;
      top -507px
      left 804px
      background: #CF317D;
      opacity 0.4
      filter: blur(500px);
    &::before
      width: 880px;
      height: 880px;
      top: 0px;
      left: -293px;
      border-radius: 100%
      transform: rotate(30.26deg);
      background: linear-gradient(310.64deg, #011AFF 38.04%, #FF0091 78.25%);
      opacity 0.3
      filter: blur(500px);

    &::after
      width: 1180px;
      height: 1180px;
      top: 600px;
      left: 370px;
      border-radius: 100%;
      background: #63238E;
      opacity 0.7
      filter: blur(500px);
      
.lrc-page::before,
.lrc-page::after,
  z-index 10

.app-main
  & > *
    position relative
    z-index 2
.page
  height 100vh
  display flex
  flex-direction column
  .scroll-area
    flex 1
    overflow-y scroll
    padding var(--padding-base)
    padding-bottom 100px

    // 三分之一屏
    @media (max-width: 700px)
      padding-left calc(20px * 3) !important
      padding-right calc(20px * 3) !important

.flex {
  display: flex;
  align-items: center;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-column {
  display: flex;
  flex-direction column;
}
.align-items-center {
  display: flex;
  align-items: center
}

.ellipsis {
  white-space: nowrap; /* 强制文本不换行 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}
.font-bold {
  font-weight 700
}

.close {
  color var(--close-color)
}

.svg-icon
  width 100%
  height 100%

.song-item
  position relative
  overflow hidden

  .xiaomi
    margin-left 10px
    img
      height 27px
      width auto
    // 三分之一屏
    @media (max-width: 700px)
      margin-left calc(4px * var(--scale)) !important
      img
        height calc(16px * var(--scale)) !important
    // 三分之一屏 & 三分之二屏
    @media (max-width: 900px)
      margin-left calc(4px * var(--scale)) !important
      img
        height calc(18px * var(--scale)) !important
  
  .left
    flex 1
    max-width 78%

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)   
      max-width 60%
    // 三分之一屏
    @media (max-width: 700px)
      max-width 60%!important
    .name
      color var(--song-name-color)
      margin-bottom calc(8px * var(--scale)) !important

    .desc
      *
        font-size var( --font-size-extra-small)
        color var(--text-secondary-color)
      .author
        white-space nowrap
        position relative
        padding-right 0
        margin-right 0
        max-width 80%
        z-index 10

        span
          max-width 100%
        
        .svg-icon
          // width 24px
          // height 24px
          width calc(12px * var(--scale))
          height calc(12px * var(--scale))
          color var(--text-secondary-color)

          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            width calc(12px * var(--scale)) !important
            height calc(12px * var(--scale)) !important

        .ellipsis
          flex 1

      .flag
        white-space nowrap
        position relative
        padding-left calc(6px * var(--scale))
        margin-left calc(4px * var(--scale))

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)   
          padding-left calc(6px * var(--scale)) !important
          margin-left calc(4px * var(--scale)) !important

        &:after
          content ""
          width 1px
          height calc(8px * var(--scale))
          // background-color var(--text-secondary-color)
          border-right 1px solid var(--text-secondary-color)
          position absolute
          right unset
          left 0
          top 50%
          transform translateY(-46%)

          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)   
            height calc(10px * var(--scale)) !important
            width 1px !important

          // 三分之一屏
          @media (max-width: 700px)
            height calc(10px * 3)
      .vip
        height calc(18px * var(--scale))
        margin-left calc(4px * var(--scale))

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)   
          height calc(18px * var(--scale)) !important
      .album
        white-space nowrap
      
      .sing-cnt
        margin-left calc(4px * var(--scale))
        font-size var(--font-size-tiny)
        position relative
        top 0px
        height 27px
        line-height 23px
        white-space nowrap
        border 1px solid var(--text-secondary-color)
        display inline-block
        border-radius calc(16px * 3)
        padding 0 calc(6px * 1.5)
      
        // 三分之一屏
        @media (max-width: 700px)
          height  calc(16px * 3)
          line-height calc(16px * 3)
          font-size calc(10px * 3) !important
        span
          color var(--highlight-color)
          font-size var(--font-size-tiny)

        // 三分之一屏 & 三分之二屏
        @media (max-width: 900px)
          margin-left calc(4px * var(--scale))!important
          height calc(18px * var(--scale)) !important
          line-height calc(15px * var(--scale)) !important
          padding 0 calc(6px * var(--scale)) !important
          font-size calc(10px * var(--scale)) !important
          border-radius calc(16px * var(--scale)) !important
          span
            font-size calc(10px * var(--scale)) !important

        // // 三分之一屏
        // @media (max-width: 700px)
        //   font-size var( --font-size-small)
        //   span
        //     font-size var(--font-size-small)
  &.ordered
    .left
      *:not(.sing-cnt)
        color var(--highlight-color) !important
      .author::after
        content ""
        background var(--highlight-color) !important

.empty
  display flex
  flex-direction column
  justify-content center
  align-items center
  *
    color var(--text-secondary-color)
    font-size var(--font-size-medium)
  .svg-icon
    width 90px
    height 90px
    margin-bottom 40px

    // 三分之一屏
    @media (max-width: 700px)
      width calc(50px * 3) !important
      height calc(50px * 3) !important
      margin-bottom calc(20px * 3)
      
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(50px * var(--scale)) !important
      height calc(50px * var(--scale)) !important
      margin-bottom calc(20px * var(--scale))

.global-tab
  width 100%
  height auto!important
  border-radius 0px
  padding 0px !important
  display flex
  align-items center
  overflow-x scroll
  margin-bottom 0px
  .item
    width auto
    height unset
    display flex
    align-items center
    padding 0px !important
    border-bottom 2px solid var(--border-color)
    @media (max-width: 900px)
      border-bottom calc(2px * var(--scale)) solid var(--border-color)!important
    // 三分之一屏
    @media (max-width: 700px)
      margin-right 0px
    span
      width 2px
      height calc(20px * var(--scale))
      background var(--border-color)
      margin 0 28px

      // 三分之一屏 & 三分之二屏
      @media (max-width: 900px)
        width calc(2px * var(--scale)) !important
        height calc(16px * var(--scale)) !important

    &-txt
      color var(--text-secondary-color)
      font-size var(--font-size-large)
      font-weight 400
      padding calc(18px * var(--scale)) calc(10px * var(--scale))
      width auto
      margin 0 0px
      white-space nowrap
      position relative

      // 三分之一屏
      @media (max-width: 700px)
        font-size var(--font-size-large) !important

  .active
    color var(--highlight-color)
    // font-weight bold
    // font-weight 700
    &::after
      content ''
      position absolute
      left 0
      bottom -2px
      width 100%
      height 2px
      background-color var(--highlight-color)

      // 三分之一屏 & 三分之二屏
      @media (max-width: 900px)
        height calc(2px * var(--scale)) !important
        bottom calc(-2px * var(--scale)) !important

  // 三分之一屏
  @media (max-width: 700px)
    .item
      &-txt
        max-width unset
        font-size var(--font-size-large)

.vue-recycle-scroller
  height 100%

.text-align-left
  .van-dialog__message
    text-align left !important



.avatar-default-wrapper
  background url('../assets/images/singer-light.png') no-repeat
  background-size 100%
  border-radius 50%
  overflow hidden
  
  div
    width: 100%
    height: 100%
    background-size: 100% auto

.dark-theme {
  .avatar-default-wrapper {
    background-image url('../assets/images/singer-dark.png')
  }
}




video
  &:focus
    outline none !important
:root
  --van-warning-color #fff !important
  --van-button-warning-background-color #fff !important
  --van-button-warning-border-color #fff !important
.thunder-modal-container
  @media screen and (max-height 650px)
    zoom 0.85
.net-error
  width 214px
  height 214px
  display flex
  flex-direction column
  justify-content center
  align-items center
  border-radius 12px
  img, svg
    width 90px !important
    height 90px !important
  .active
    animation rotate 1s linear infinite
  p
    height 28px
    line-height 28px
    font-size 24px
    color rgba(0, 0, 0, 0.40)!important
    &:nth-child(2)
      margin 20px 0 8px
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

// .theme-themeLight
//   .avatar-default-wrapper
//     background-image url('../assets/images/singer-light.png')
.v-progressive-image, .v-progressive-image-placeholder
  width: 100%
  height: 100%
  overflow hidden
  background none !important 
.v-progressive-image
  max-width 100vw!important
  & > div
    padding-bottom 0!important
    width: 100%
    height: 100%
  &-main
    width: 100%
    height: 100%

.order-song-item, .already-song-item, .song-item
  .flag
    white-space nowrap