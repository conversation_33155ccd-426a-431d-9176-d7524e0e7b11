// :root {
//   --font-size-large: 36px; /* 大字体 */
//   --font-size-medium: 32px; /* 中等字体 */
//   --font-size-small: 28px; /* 小字体 */
//   --font-size-extra-small: 24px; /* 超小字体 */
//   --font-size-tiny: 16px; /* 新增：极小字体 */
//   --font-size-extra-small-22: 22px; /* 新增：22px字体 */
// }

:root { // 全屏 1200
  --font-size-extra-large: calc(22px * 1.5);
  --font-size-large: calc(18px * 1.5); /* 大字体 */
  --font-size-medium: calc(16px * 1.5); /* 大字体 */
  --font-size-small: calc(12px * 1.5); /* 小字体 */
  --font-size-tiny: calc(10px * 1.5); /* 新增：极小字体 */
  --font-size-extra-small: calc(14px * 1.5); /* 超小字体 */
}

// 三分之一屏
@media (max-width: 700px)
  :root {
    --font-size-extra-large: calc(22px * 3);
    --font-size-large: calc(18px * 3); /* 大字体 */
    --font-size-medium: calc(16px * 3); /* 大字体 */
    --font-size-small: calc(12px * 3); /* 小字体 */
    --font-size-tiny: calc(10px * 3); /* 新增：极小字体 */
    --font-size-extra-small: calc(14px * 3); /* 超小字体 */
  }

// 三分之二屏
@media (max-width: 900px) and (min-width: 701px)  
  :root {
    --font-size-extra-large: calc(24px * 1.48) !important;
    --font-size-large: calc(18px * 1.48)  !important; /* 大字体 */
    --font-size-medium: calc(16px * 1.48) !important; /* 大字体 */
    --font-size-small: calc(12px * 1.48) !important; /* 小字体 */
    --font-size-tiny: calc(10px * 1.48) !important; /* 新增：极小字体 */
    --font-size-extra-small: calc(14px * 1.48) !important; /* 超小字体 */
  }

// @media screen and (orientation: portrait) {
//   :root {
   
//   }
// }