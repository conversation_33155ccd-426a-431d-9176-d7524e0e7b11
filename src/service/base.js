
import httpV2 from '@/utils/httpV2'
import store2 from 'store2'

export async function getBaseInfo() {
  const res = await httpV2.get('/stb/v2/info/base', {
    params: {
      session: store2('_boot_id'),
    }
  })
  return res
}

export async function reportSession() {
  // const res = await httpV2.post('/stb/v2/info', {
  //   session: store2('_boot_id')
  // })
  // return res
}

export async function getLicence() {
  const res = await httpV2.get('/stb/v2/beian')
  return res
}

export async function getMall() {
  const res = await httpV2.get('/stb/v2/mall')
  return res
}

export async function getConfig() {
  const res = await httpV2.get('/stb/v2/config')
  return res
}

export async function checkConfig({ vin, mac_id, androidid, sys_func, car_sdk, car_name }) {
  const res = await httpV2.options(`/stb/v2/config?vin=${vin}&mac_id=${mac_id}&androidid=${androidid}&sys_func=${sys_func}&car_sdk=${car_sdk}&car_name=${car_name}`)
  return res
}