import http from '@/utils/http'
import httpV2 from '@/utils/httpV2'
import get from 'lodash/get'

let pollingTimer = null;
let pollingAttempts = 0;

export async function getVipQR({ mac_id, pkg_id, unionid, openid }) {
  const res = await http.get('/v2/stb/carplay/vip/qr', {
    params: {
      mac_id,
      pkg_id,
      unionid,
      openid
    }
  })
  return get(res, 'data', {})
}

export async function getPayPkg() {
  const res = await http.get('/stb/carplay/paypkg')
  return get(res, 'data', [])
}

export async function exchangeVip({ cdkey, mac_id }) {
  // '/stb/carplay/coupon'
  const res = await httpV2.get('/stb/v2/coupon', {
    params: {
      cdkey,
      mac_id
    }
  })
  // mac_id
  // 车机唯一标识
  // days
  // VIP有效时长
  // finish_time
  // 兑换完成时间
  // start_time
  // vip开始时间
  // end_time
  // vip到期时间
  return res
}

export async function getExchangeHistory(unionid, p) {
  // '/stb/carplay/coupon/history'
  const res = await httpV2.get('/stb/v2/coupon/history', {
    params: {
      unionid,
      p,
      size: '20'
    }
  })
  return get(res, 'data', [])
}

export async function checkOrderPayStatus(order_id) {
  const res = await http.get('/carplay/vip/query', {
    params: {
      order_id
    }
  })
  return res
}
export async function getOperationVipQR(unionid, pkg_id = 6) {
  const res = await httpV2.get('/stb/v2/vip/qr', {
    params: {
      unionid,
      pkg_id
    }
  })
  return res
}
export async function getVipPkg(unionid) {
  const res = await httpV2.get('/stb/v2/vip/pkg', {
    params: {
      unionid,
    }
  })
  return res
}
export async function getVipPkgQr({ unionid, pkg_id, fr }) {
  if (!unionid || !pkg_id || !fr) {
    throw new Error('必须传递 unionid、pkg_id 和 fr 参数');
  }
  const res = await httpV2.get('/stb/v2/vip/qr', {
    params: {
      unionid,
      pkg_id,
      fr
    }
  })
  return res.data
}

export async function getUserOrders(unionid, p = 1) {
  const res = await httpV2.get('/stb/v2/user/order', {
    params: {
      unionid,
      p,
    }
  })
  return get(res, 'data', [])
}