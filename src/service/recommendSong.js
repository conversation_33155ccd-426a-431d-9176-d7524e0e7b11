import httpV2 from '@/utils/httpV2'
import get from 'lodash/get'

export async function getRecommendSongList() {
//   return [{
//     "singer": "DJ小鱼儿",
//     "music_name": "黑桃A(HD)",
//     "flag": [
//         "流水影"
//     ],
//     "songid": 8620060,
//     "org": "1",
//     "acc": "2",
//     "lrc": 5,
//     "singer_head": "",
//     "singerid": 131777,
//     "is_vip": 0
// },
// {
//     "singer": "小阿枫",
//     "music_name": "爱江山更爱美人(DJ版)(HD)",
//     "flag": [
//         "流水影"
//     ],
//     "songid": 8707299,
//     "org": "1",
//     "acc": "2",
//     "lrc": 5,
//     "singer_head": "",
//     "singerid": 620896,
//     "is_vip": 0
// }]
  const res = await httpV2.get('/stb/v2/pop/songlist', {
    params:{
      size: 6
    }
  })
  return get(res, 'data', []);
}
