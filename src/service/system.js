import { getMacAddress } from '@/utils/mac'
import uaInfo from '@/utils/ua'
import { TSNativeInstance } from '../packages/TSJsbridge/TSNative'
import appConfig from '@/config'
import { TSVehicleInstance } from '../packages/TSJsbridge/TSVehicle'

export function getMac() {
  return getMacAddress()
}

export function getSystem() {
  let systemInfo = {
    brand: uaInfo.browser.name,
    model: uaInfo.device.model || uaInfo.browser.name,
    system: uaInfo.os.name,
    resolution: '',
    orientation: '',
    vn: appConfig.vn,
    mac: getMac(),
    src: appConfig.src,
    extra: {
      leftCorner: {
        width: 0,
      },
      rightCorner: {
        width: 0,
      }
    },
    hasThunderMic: false
  }
  try {
    // {"TAG":"BaseEventTrackBuilder","androidid":"927f78d21080b30","_api":30,"_boot_app":3,"_boot_id":"7F076B34-5838-0004-0001-000000000003","_car_id":"927f78d21080b30","_did":"927f78d21080b30","_firm":"UnKnow","imei":"00e07e076a20","imsi":"00e07e076a20","ktv_id":"THUNDER_STORE_ID","_lat":0.0,"_lng":0.0,"_mac":"00e07e076a20","_mike_bind":0,"_m":"TS_KTV_O","_net_type":"1","_h":"1","_r1":"1280,720","_r2":"1280,720","_sdk_info":"11","_src":"THUNDER_CHANNEL","_tm":"2023-06-29 14:58:38","_vn":"1.0.1"}
    const res = JSON.parse(TSNativeInstance.getParams('all_pub_params')) || {}
    console.info('TSNativeBridge all_pub_params', res)
    console.info('TSNativeBridge all_pub_params', res)
    const uniqueId = TSVehicleInstance.getUniqueId() || ''
    const uuid = uniqueId || res.androidid
    console.info('TSNativeBridge all_pub_params uuid', uuid)
    if (res && uuid) {
      systemInfo.model = res._m
      systemInfo.vn = res._vn
      systemInfo.src = res._src
      systemInfo.mac = uuid
      systemInfo.androidid = res.androidid
      systemInfo._car_id = res.unique_id
      systemInfo._did = res.unique_id
    }
  } catch (error) {
    console.info('TSNativeBridge error', error)
  }
  return systemInfo
}