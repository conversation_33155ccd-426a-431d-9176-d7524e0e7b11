// import http from '@/utils/http'
import http from '@/utils/httpV2'
import store from '@/store'
import store2 from 'store2'

export async function getCarplayInfo(noLoading = false) {
  // /stb/carplay/info
  const res = await http.get('/stb/v2/info', {
    params: {
      session: store2('_boot_id'),
      mac_id: store.state.system.systemInfo.mac,
      noLoading: noLoading
    }
  })
  return res;
}

export async function getSoundfile() { // 音效下发接口
  const res = await http.get('/stb/v2/soundfile')
  return res;
}

export async function getAIface(unionid) {
  const res = await http.get(`/stb/v2/aiface?unionid=${unionid}`)
  return res;
}