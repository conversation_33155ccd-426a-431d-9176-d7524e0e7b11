import httpV2 from '@/utils/httpV2'
import get from 'lodash/get'

// 开屏弹窗运营活动
export async function getTopfans({ singerid, unionid }) {
  const res = await httpV2.get(`stb/v2/singer/topfans?singer_id=${ singerid }&unionid=${ unionid }`)
  return {
    topfans: {
      ...get(res, 'data.topfans', {}),
      ...get(res, 'data.user_info', {}),
    },
    usercount: get(res, 'data.my_song_count', 0),
  };
}


// {
//   "errcode": 200,
//   "errmsg": "服务正常",
//   "data": {
//       "topfans": {
//           "id": 4341,
//           "day": "2023-08-01 00:00:00",
//           "singer_id": 111064,
//           "unionid": "o6qE3t0rMdQO7lMrYXofYl67pags",
//           "singer_name": "王心凌",
//           "count": 147
//       },
//       "user_info": {
//           "sex": 0,
//           "unionid": "o6qE3t0rMdQO7lMrYXofYl67pags",
//           "openid": "o14xfwPLGt_tq67DA7FnqIGgnHDg",
//           "nickname": "勇往直前",
//           "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJkvgibBQtWNwgSPfN9LW6pRnCdh75icGNVz323jjXg0WWyKBGTudxjyz3RAuGO3o0N6hSK7qXSULog/132",
//           "is_new": false
//       },
//       "my_song_count": 2
//   }
// }