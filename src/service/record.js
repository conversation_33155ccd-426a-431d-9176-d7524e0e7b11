import httpV2 from '@/utils/httpV2'
import get from 'lodash/get'

export async function getRecords({ unionid, p = 1 }) {
  const res = await httpV2.get(`/stb/v2/user/record?unionid=${unionid}&p=${p}`)
  return get(res, 'data.data_list', []);
}

export async function deleteRecording({ id, unionid }) {
  const res = await httpV2.delete(`/stb/v2/user/record?id=${id}&unionid=${unionid}`);
  const errcode = get(res, 'data.errcode');
  return errcode === 200;
}

export async function reportPlayback({ id, unionid }) {
  await httpV2.put(`/stb/v2/user/record?id=${id}&unionid=${unionid}`);
}