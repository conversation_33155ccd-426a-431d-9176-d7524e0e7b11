{"v": "5.6.10", "fr": 30, "ip": 0, "op": 150, "w": 400, "h": 400, "nm": "倒彩 导出", "ddd": 1, "assets": [{"id": "image_0", "w": 274, "h": 448, "u": "img/booing/", "p": "img_0.png", "e": 0}, {"id": "image_1", "w": 474, "h": 476, "u": "img/booing/", "p": "img_1.png", "e": 0}, {"id": "image_2", "w": 86, "h": 136, "u": "img/booing/", "p": "img_2.png", "e": 0}, {"id": "image_3", "w": 149, "h": 120, "u": "img/booing/", "p": "img_3.png", "e": 0}, {"id": "image_4", "w": 135, "h": 79, "u": "img/booing/", "p": "img_4.png", "e": 0}, {"id": "image_5", "w": 193, "h": 192, "u": "img/booing/", "p": "img_5.png", "e": 0}, {"id": "image_6", "w": 56, "h": 52, "u": "img/booing/", "p": "img_6.png", "e": 0}, {"id": "image_7", "w": 154, "h": 142, "u": "img/booing/", "p": "img_7.png", "e": 0}, {"id": "image_8", "w": 165, "h": 153, "u": "img/booing/", "p": "img_8.png", "e": 0}, {"id": "image_9", "w": 40, "h": 42, "u": "img/booing/", "p": "img_9.png", "e": 0}, {"id": "image_10", "w": 120, "h": 126, "u": "img/booing/", "p": "img_10.png", "e": 0}, {"id": "image_11", "w": 306, "h": 163, "u": "img/booing/", "p": "img_11.png", "e": 0}, {"id": "image_12", "w": 158, "h": 178, "u": "img/booing/", "p": "img_12.png", "e": 0}, {"id": "image_13", "w": 312, "h": 213, "u": "img/booing/", "p": "img_13.png", "e": 0}, {"id": "image_14", "w": 468, "h": 468, "u": "img/booing/", "p": "img_14.png", "e": 0}, {"id": "comp_0", "layers": [{"ddd": 1, "ind": 1, "ty": 2, "nm": "鞋子", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [100]}, {"t": 92, "s": [0]}], "ix": 11}, "rx": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [0]}, {"t": 40, "s": [720]}], "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [332, 30, 58], "to": [58, 0, 0], "ti": [0, 0, 0]}, {"t": 40, "s": [0, 0, 58]}], "ix": 7}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [-121.4, 484.35, 0], "to": [51.525, -43.8, 0], "ti": [-51.525, 43.8, 0]}, {"i": {"x": 0.701, "y": 0.701}, "o": {"x": 0.285, "y": 0.285}, "t": 40, "s": [187.75, 221.55, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 79, "s": [187.75, 221.55, 0], "to": [2.175, 52.5, 0], "ti": [-2.175, -52.5, 0]}, {"t": 99, "s": [200.8, 536.55, 0]}], "ix": 2}, "a": {"a": 0, "k": [137, 224, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [23.4, 23.4, 23.4]}, {"t": 40, "s": [55.8, 55.8, 55.8]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 30, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "笑脸", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [201.7, 194.55, 0], "ix": 2}, "a": {"a": 0, "k": [237, 238, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 6, "s": [45, 45, 100]}], "ix": 6, "x": "var $bm_rt;\nvar fudu, pinlv, zuni, n, n, t, t, v;\ntry {\n    fudu = 1.3;\n    pinlv = 0.1;\n    zuni = 6;\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, fudu), Math.sin($bm_mul($bm_mul($bm_mul(pinlv, t), 2), Math.PI))), Math.exp($bm_mul(zuni, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ip": 0, "op": 40, "st": 30, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "图层 26", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 71, "s": [100]}, {"t": 81, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [120.25, 86.55, 0], "ix": 2}, "a": {"a": 0, "k": [67, 132, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 41, "s": [0, 0, 100]}, {"t": 46, "s": [45, 45, 100]}], "ix": 6, "x": "var $bm_rt;\nvar fudu, pinlv, zuni, n, n, t, t, v;\ntry {\n    fudu = 1.3;\n    pinlv = 0.1;\n    zuni = 10;\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, fudu), Math.sin($bm_mul($bm_mul($bm_mul(pinlv, t), 2), Math.PI))), Math.exp($bm_mul(zuni, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ip": 0, "op": 94, "st": 30, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "图层 27", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 71, "s": [100]}, {"t": 81, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [85.375, 105.45, 0], "ix": 2}, "a": {"a": 0, "k": [142.5, 104, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [0, 0, 100]}, {"t": 45, "s": [45, 45, 100]}], "ix": 6, "x": "var $bm_rt;\nvar fudu, pinlv, zuni, n, n, t, t, v;\ntry {\n    fudu = 1.3;\n    pinlv = 0.1;\n    zuni = 10;\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, fudu), Math.sin($bm_mul($bm_mul($bm_mul(pinlv, t), 2), Math.PI))), Math.exp($bm_mul(zuni, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ip": 0, "op": 94, "st": 30, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "图层 25", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 71, "s": [100]}, {"t": 81, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [81.775, 151.575, 0], "ix": 2}, "a": {"a": 0, "k": [137.5, 41.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 41, "s": [0, 0, 100]}, {"t": 46, "s": [45, 45, 100]}], "ix": 6, "x": "var $bm_rt;\nvar fudu, pinlv, zuni, n, n, t, t, v;\ntry {\n    fudu = 1.3;\n    pinlv = 0.1;\n    zuni = 10;\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, fudu), Math.sin($bm_mul($bm_mul($bm_mul(pinlv, t), 2), Math.PI))), Math.exp($bm_mul(zuni, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ip": 0, "op": 94, "st": 30, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "图层 28", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [38]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 127, "s": [38]}, {"t": 136, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 22, "ix": 10}, "p": {"a": 0, "k": [179.157, 34.678, 0], "ix": 2}, "a": {"a": 0, "k": [96.5, 96, 0], "ix": 1}, "s": {"a": 0, "k": [17.55, 17.55, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 30, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "图层 29", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [38]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [38]}, {"t": 142, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [326.612, 87.747, 0], "ix": 2}, "a": {"a": 0, "k": [96.5, 96, 0], "ix": 1}, "s": {"a": 0, "k": [32.85, 32.85, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 30, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "图层 24", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [38]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 121, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 130, "s": [38]}, {"t": 139, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": -21, "ix": 10}, "p": {"a": 0, "k": [50.275, 123, 0], "ix": 2}, "a": {"a": 0, "k": [96.5, 96, 0], "ix": 1}, "s": {"a": 0, "k": [24.75, 24.75, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 30, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "表情3", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [197.2, 190.05, 0], "ix": 2}, "a": {"a": 0, "k": [1524, 518, 0], "ix": 1}, "s": {"a": 0, "k": [45, 45, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 980, "ip": 40, "op": 480, "st": 30, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 2, "ty": 0, "nm": "右眼", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1610, 394, 0], "ix": 2}, "a": {"a": 0, "k": [1610, 394, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 980, "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "左眼", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1425, 498, 0], "ix": 2}, "a": {"a": 0, "k": [1425, 498, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1920, "h": 980, "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "牙", "refId": "image_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1559, 593.5, 0], "ix": 2}, "a": {"a": 0, "k": [153, 81.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "舌头", "refId": "image_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1531, 733, 0], "ix": 2}, "a": {"a": 0, "k": [79, 89, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 34, "nm": "操控", "np": 6, "mn": "ADBE FreePin3", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "人偶引擎", "mn": "ADBE FreePin3 Puppet Engine", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "网格旋转调整", "mn": "ADBE FreePin3 Auto Rotate Pins", "ix": 2, "v": {"a": 0, "k": 20, "ix": 2}}, {"ty": 7, "nm": "在透明背景上", "mn": "ADBE FreePin3 On Transparent", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 30, "nm": "arap", "np": 3, "mn": "ADBE FreePin3 ARAP Group", "ix": 4, "en": 1, "ef": [{"ty": 6, "nm": "自动追踪形状", "mn": "ADBE FreePin3 Outlines", "ix": 1, "v": 0}, {"ty": 1, "nm": "网格", "np": 2, "mn": "ADBE FreePin3 Mesh Group", "ix": 2, "en": 1, "ef": [{"nm": "网格 1", "np": 8, "mn": "ADBE FreePin3 Mesh Atom", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "网格", "mn": "ADBE FreePin3 Mesh", "ix": 1, "v": 0}, {"ty": 0, "nm": "三角形", "mn": "ADBE FreePin3 Mesh Tri Count", "ix": 2, "v": {"a": 0, "k": 50, "ix": 2}}, {"ty": 0, "nm": "密度", "mn": "ADBE FreePin3 Mesh Tri Density", "ix": 3, "v": {"a": 0, "k": 10, "ix": 3}}, {"ty": 0, "nm": "扩展", "mn": "ADBE FreePin3 Mesh Expansion", "ix": 4, "v": {"a": 0, "k": 3, "ix": 4}}, {"nm": "变形", "np": 6, "mn": "ADBE FreePin3 PosPins", "ix": 5, "en": 1, "ef": [{"nm": "操控点 5", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 1, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 18, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [81, 69], "ix": 4}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"nm": "操控点 4", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 19, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [23, 21], "ix": 4}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"nm": "操控点 3", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 3, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 20, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [137, 6], "ix": 4}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"nm": "操控点 2", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 4, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 21, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0, "y": 0}, "o": {"x": 0, "y": 0}, "t": 0, "s": [76, 169], "to": [7.333, -0.667], "ti": [0, 0]}, {"i": {"x": 0, "y": 0}, "o": {"x": 0, "y": 0}, "t": 0, "s": [120, 165], "to": [0, 0], "ti": [7.333, -0.667]}, {"t": null, "s": [76, 169]}], "ix": 4, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle', 0);"}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"nm": "操控点 1", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 5, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 22, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [83, 10], "ix": 4}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}]}, {"nm": "重叠", "np": 1, "mn": "ADBE FreePin3 HghtPins", "ix": 6, "en": 1, "ef": []}, {"nm": "硬度", "np": 1, "mn": "ADBE FreePin3 StarchPins", "ix": 7, "en": 1, "ef": []}]}]}]}]}], "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "嘴", "refId": "image_13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1557, 619.5, 0], "ix": 2}, "a": {"a": 0, "k": [156, 106.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "圆脸", "refId": "image_14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1532, 528, 0], "ix": 2}, "a": {"a": 0, "k": [234, 234, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "椭圆 19 拷贝 3", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.333, "y": 0}, "t": 41, "s": [1606, 398, 0], "to": [1.333, 9, 0], "ti": [-9.5, -8.5, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.173}, "t": 61, "s": [1614, 452, 0], "to": [9.5, 8.5, 0], "ti": [-7.5, 8.5, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.168}, "t": 81, "s": [1663, 449, 0], "to": [7.5, -8.5, 0], "ti": [9.5, 8.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.162}, "t": 101, "s": [1659, 401, 0], "to": [-9.5, -8.5, 0], "ti": [8.833, 0.5, 0]}, {"t": 121, "s": [1606, 398, 0]}], "ix": 2}, "a": {"a": 0, "k": [28, 26, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "椭圆 19 拷贝", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1627, 423, 0], "ix": 2}, "a": {"a": 0, "k": [77, 71, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "椭圆 19", "refId": "image_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1627.5, 424.5, 0], "ix": 2}, "a": {"a": 0, "k": [82.5, 76.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 2, "ty": 2, "nm": "椭圆 18", "refId": "image_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0}, "t": 47, "s": [1425, 499, 0], "to": [4.5, -6.167, 0], "ti": [-6, -1.167, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.162}, "t": 67, "s": [1452, 462, 0], "to": [6, 1.167, 0], "ti": [5.333, -11.5, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.171}, "t": 86, "s": [1461, 506, 0], "to": [-5.333, 11.5, 0], "ti": [6, 1.167, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.2}, "t": 107, "s": [1420, 531, 0], "to": [-6, -1.167, 0], "ti": [-0.833, 5.333, 0]}, {"t": 127, "s": [1425, 499, 0]}], "ix": 2}, "a": {"a": 0, "k": [20, 21, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "椭圆 16", "refId": "image_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1431, 493, 0], "ix": 2}, "a": {"a": 0, "k": [60, 63, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 450, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "倒彩", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 139, "s": [100]}, {"t": 147, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [200, 200, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 139, "s": [100, 100, 100]}, {"t": 147, "s": [50, 50, 100]}], "ix": 6}}, "ao": 0, "w": 400, "h": 400, "ip": 0, "op": 150, "st": 0, "bm": 0}], "markers": []}