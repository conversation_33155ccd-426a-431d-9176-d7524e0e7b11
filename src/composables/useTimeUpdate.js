import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import getComponentLrcData from '@/components/mv/utils'
import eventBus from '@/utils/event-bus'

export default function useTimeUpdate() {
  const store = useStore();
  const videoPaused = computed(() => store.state.videoPaused);
  const defaultSong = computed(() => store.state.miniMv.defaultSong);
  const orderedListNum = computed(() => store.state.orderedList.length)

  const ircListData = computed(() => {
    if (orderedListNum.value === 0) {
      return getComponentLrcData(defaultSong.value?.lrc?.json)
    }
    return getComponentLrcData(store.state.songItemLrc)
  })
  const isMvMode = computed(() => store.state.mvMode.mode === 'mv');
  const currVideoTime = ref(0);
  const currentPlayTime = ref(-1);

  const handleTimeupdate = (payload) => {
    eventBus.emit('handleTimeupdate', payload)
    if (videoPaused.value) {
      store.dispatch('setVideoPaused', false);
    }
    eventBus.emit('toggle-cover', false)
    
    const t = payload / 1000;
    
    currVideoTime.value = t;
    // 计算倒计时并更新到 store
    if (ircListData.value.length > 0) {
      if (t < ircListData.value[0].t) {
        const countdownValue = Math.floor(ircListData.value[0].t - t);
        store.commit('SET_COUNTDOWN_TO_FIRST_LYRIC', countdownValue); // 更新到 store
      }
    } else {
      store.commit('SET_COUNTDOWN_TO_FIRST_LYRIC', 999); // 更新到 store
    }
    // 原有逻辑
    if (!isMvMode.value && ircListData.value.length) {
      ircTimeCompare(t);
      return;
    }
    if (!ircListData.value.length) return; // isMvMode.value
    ircTimeCompare(t, absCompare);
  };

  const absCompare = (n1, n2, p, max) => {
    return Math.abs(Math.floor(n1 * p) - Math.floor(n2 * p)) <= max * p;
  };

  const easyCompare = (n1, n2, p) => {
    return Math.floor(n1 * p) < Math.floor(n2 * p);
  };

  // mv、歌词播放时间同步
  const ircTimeCompare = (t, func) => {
    // 释放歌曲首次时间同步
    if (t < 0.3 || t <= ircListData.value[0].t) {
      currentPlayTime.value = t;
    }
    if (t >= ircListData.value[ircListData.value.length - 1].t) {
      store.commit('UPDATE_CURR_IYRIC_INDEX', ircListData.value.length - 1);
      return;
    }
    // 更新歌词位置状态
    ircListData.value.find((v, i, arr) => {
      let compareRes = false;
      if (typeof func === 'function') {
        compareRes = func(v.t, t, 10, 0.5); // 临近值实时对比
      } else {
        compareRes = easyCompare(v.t, t, 10) && easyCompare(t, arr[i + 1].t, 10); // 区间模糊对比
      }
      if (compareRes) {
        store.commit('UPDATE_CURR_IYRIC_INDEX', i);
        return true;
      }
      return false;
    });
  };

  return {
    handleTimeupdate,
    currentPlayTime,
  };
}