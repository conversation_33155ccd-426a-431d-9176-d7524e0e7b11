import { useStore } from 'vuex'
import { computed } from 'vue'
import get from 'lodash/get'
import useVip from './useVip'
import { getSongDetail } from '@/service/song'

export default function useSongAuth() {
  const store = useStore()
  const { showVipQrcode } = useVip()

  // 校验用户播放歌曲权限
  const checkSongAuth = (songData, is_vip, error) => {
    const currPlaySong = computed(() => store.state.videoPlayerHistory.songItem)
    // 歌曲是vip歌曲时，双重验证，再次通过后端验证用户登陆状态以及vip并提示
    // 这里有处坑 - 后端返的is_user_login、is_user_vip两个字段只在m3u8无数据时有效
    if (is_vip && (!songData.is_user_login || !songData.is_user_vip)) {
      // showVipQrcode()
    } else {
      if (!songData.src) {
        // Toast('暂未下载，无法播放')
        // 暂时做hide处理的优化
        // if (!store.state.mvIsHide) store.commit('UPDATE_MV_ISHIDE', true)
      }
    }
  }
  // 添加当前歌曲的m3u8资源
  const addCurrSongAuth = async (song) => {
    const songData = await getSongDetail(song.songid)
    let authData = {
      is_user_login: songData.is_user_login,
      is_user_vip: songData.is_user_vip || true,
      msg: songData.msg,
      src: '',
    }
    checkSongAuth(authData, song.is_vip, get(songData, 'error', undefined))
  }

  return {
    addCurrSongAuth,
  }
}