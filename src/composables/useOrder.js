import { computed } from 'vue'
import store from '@/store'
import useSongItem from './useSongItem'
import useM3u8 from './useM3u8'
import useDownload from './useDownload'
import { sendLog } from '@/directives/v-log/log'
import { get } from 'lodash'
import { setOrderedList, setAlreadyList } from '@/utils/historyCache'
import { TSMediaInstance } from '@/packages/TSJsbridge';
import { DownloadStatus } from '@/utils/download'
import eventBus from '@/utils/event-bus'
import useScore from '@/composables/useScore';
import { nanoid } from 'nanoid'

export default function useOrder() {
  const { orderSong: normalOrderSong } = useSongItem()
  const { addCurrSongM3u8 } = useM3u8()
  const { getIsLocalSong, checkAutoDownload } = useDownload()
  const { stopScore, handleScoreRelated, isShowScore } = useScore()

  const mvIsHide = computed(() => store.state.mvIsHide);

  const orderedList = computed(() => {
    const list = store.state.orderedList;
    list.forEach((item, index) => {
      if (!item._i)
        item._i = 'k_' + nanoid();
        item._index = index;
    });
    return list;
  });
  
  const alreadyList = computed(() => {
    const list = store.state.alreadyList;
    list.forEach((item, index) => {
      if (!item._i)
        item._i = 'k_' + nanoid();
        item._index = index;
    });
    return list;
  });

  const orderedListNumber = computed(() => orderedList.value.length)
  const orderedSongIdMap = computed(() => store.state.orderedSongIdMap)
  const currPlayingSong = computed(() => store.state.videoPlayerHistory.songItem)

  const isSingStatus = computed(() => store.state.isSingStatus)
  const initControlPlay = computed(() => store.state.videoInitAutoPlay)

  // 检查已点列表是否为 “空” 逻辑：length为0 或 仅包括自己
  const playNext = async (playingSongTemp, notHasCallback, from = 'control', isUnablePlay) => {
    const playingSong = orderedList.value[0]
    await store.dispatch('download/stopDownload');

    let nextSong = orderedList.value[1]

    console.log('切歌: playNext', orderedList.value, store.state.orderedList)
    if (!nextSong) {
      console.log('切歌: 无下一首')
      if (from !== 'ended') {
        deleteSong(0)
        setOrderedList()
        setAlreadyList()
      }
      if (typeof notHasCallback === 'function') {
        notHasCallback.call()
      }
      return
    }

    handleScoreRelated(nextSong)

    const nextSongIsLocal = await getIsLocalSong(nextSong)

    await store.commit('DELETE_SONG_ORDER_LIST', 0)

    sendLog({
      event_type: 'click',
      event_name: 105,
      event_data: {
        type: 1,
        song_id: nextSong.songid,
        song_name: nextSong.music_name,
        singer: nextSong.singer
      }
    })
    if (from == 'microphone') {
      sendLog({
        event_type: 'click',
        event_name: 106,
        event_data: {
          type: 2,
          song_id: nextSong.songid,
          song_name: nextSong.music_name,
          singer: nextSong.singer
        }
      })
    }
    await store.commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', nextSong)

    if (!isUnablePlay) {
      console.log('切歌: 播放下一首', nextSong)
      // 播下一首
      // store.commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', nextSong)
      if (nextSong.songid) {
        await addCurrSongM3u8(nextSong)
        TSMediaInstance.playNext(nextSong, nextSongIsLocal)
      } else if (nextSong.isAIMV) {
        TSMediaInstance.playAi(nextSong)
      }
    }
    //需更新下一首歌曲orderedSongIdMap 后续可考虑数据转换Map
    const isAddSongIdMap = (nextSong.songid === orderedList.value[0].songid) || !Object.prototype.hasOwnProperty.call(orderedSongIdMap.value, nextSong.songid)
    if (isAddSongIdMap) {
      store.commit('SAVE_ORDERED_SONGIDMAP', nextSong.songid)
    }
    /**
     * 现在歌曲与歌曲资源数据是分开的，歌曲资源通过全局检测当前播放歌曲id的变化去更新
     * 有一个特殊场景：当前播放歌曲与下一首歌曲相同时需在这里做额外获取歌曲资源处理
     */
    if (nextSong.songid === playingSong.songid) {
      addCurrSongM3u8(playingSong)
    }

    // 首先查找 playingSong 在数组中的索引
    const alreadyIndex = alreadyList.value.findIndex((item) => item.songid === playingSong.songid);

    // 如果 playingSong 已经在数组中，将其移动到第一位
    if (alreadyIndex !== -1) {
      // 使用 splice 方法从数组中移除 playingSong，并将其插入到数组的开始位置
      alreadyList.value.splice(alreadyIndex, 1);
      alreadyList.value.unshift(playingSong);
    }

    // 然后，无论 playingSong 是否已经在数组中，都保存更新后的数组
    await store.commit('SAVE_ALREADY_LIST', alreadyList.value);

    // 如果 setAlreadyList 是用来更新组件状态的函数，确保它在 commit 之后调用
    setAlreadyList();

    setOrderedList()
    checkAutoDownload();
  }

  // 已点列表中点歌
  const orderSong = async(songItem, index, isAlreadyStick = false) => {
    const songIsLocal = await getIsLocalSong(songItem)

    if (isShowScore.value && !mvIsHide.value) {
      if (index > 0) {
        if (index > 1) stickSongToTop(index, true);
        const shiftSong = get(orderedList, 'value.0', null);
        if (shiftSong) {
          await store.commit('PUSH_SONG_TO_ALREADY_LIST', shiftSong); // 添加第一首歌曲到已唱列表
          setAlreadyList();
        }
      }
      stopScore({
        from: 'useOrder'
      })
      return;
    }

    if (!songIsLocal && store.state.download.currentTask.songid === songItem.songid) {
      await store.dispatch('download/stopDownload');
    }

    // 已点列表切歌时更新【已点列表切歌】状态标识，供歌曲播放上报使用 (点正播放歌曲不进行上报)
    if(index !== 0) store.dispatch('songLog/updateCutSongTag')

    let posData = {}
    //点击当前播放歌曲继续播放，点击其他从零播放
    if(index === 0) {
      posData = { position: 'recovery' }
    }
    if (index !== 0 || !initControlPlay.value) {
      posData.useFirstSongAutoPlay = true
    }
    if (!isSingStatus.value) posData.useFirstSongAutoPlay = true
    // 播放
    normalOrderSong(songItem, {
      immediate: true,
      isOpenFullScreen: true,
      beforeImmediate: async () => {
        if (index > 0) {
          await store.commit('DELETE_SONG_ORDER_LIST', index)
          
          setOrderedList()
          setAlreadyList()
          handleScoreRelated(songItem, { from: 'useOrder' });
        }
      },
      afterOrder: async () => {
        await store.commit('UPDATE_CURR_IYRIC_INDEX', -1)
        eventBus.emit('irc-control-next')
        // 首次进入mv页 更新演唱状态
        if (!isSingStatus.value) {
          store.commit('UPDATE_IS_SING_STATUS', true)
          store.dispatch('searchTips/updateIsShowSingTips', false)
        }
        if (currPlayingSong.value.songid) {
          await addCurrSongM3u8(currPlayingSong.value)
        }
        await setOrderedList()
        if (isAlreadyStick) return
        checkAutoDownload()
      },
      ...posData //点击当前播放歌曲继续播放，点击其他从零播放
    })
  }

  // 置顶
  const stickSongToTop = async(index, isFromOrder) => {
    await store.dispatch('download/stopDownload');
    await store.commit('download/RESET_CURRENT_TASK');

    const _index = (typeof index === 'undefined' || index === -999)
      ? orderedList.value.length - 1
      : index

    console.log('stickSongToTop', index)
    await store.commit('STICK_SONG_TO_TOP_ORDERED_LIST', _index)

    if(!isFromOrder) {
      checkAutoDownload()
    }
  }

  // 删除
  const deleteSong = async (index) => {
    const currentSong = orderedList.value[index]
    // 下载中的歌曲被切歌，进入已唱列表，记忆下载进度，再次回到已点会继续下载。
    if (currentSong.downloadState === DownloadStatus.PROCESSING) {
      await store.dispatch('download/stopDownload');
    }

    store.commit('DELETE_SONG_ORDER_LIST', index)
    Promise.all([
      setOrderedList()
    ]).then(() => {
      checkAutoDownload();
    })
  }

  const addSong = (songItem, option) => {
    store.commit('PUSH_SONG_TO_ORDERED_LIST', { song: songItem, from: get(option, 'from', {}) })
  }

  return {
    playNext,
    addSong,
    orderSong,
    stickSongToTop,
    deleteSong,
    orderedList,
    orderedListNumber,
  }
}
