import { computed, inject, watch } from 'vue'
import { useStore } from 'vuex'
import Toast from '@/utils/toast'
import { TSScoreInstance, TSMicrophoneInstance, TSMediaInstance } from '@/packages/TSJsbridge';
import { sendLog } from '@/directives/v-log/log';
import store2 from 'store2'
import eventBus from '@/utils/event-bus'
import config from '@/config'

let STOP_SCORE_FROM = ''
let STOP_SCORE_MVISHIDE = false
const enableScoring = config.enableScoring

export default function useScore() {
  const store = useStore()
  const scoreSongInfo = computed(() => store.state.score.scoreSongInfo);
  const scoreResult = computed(() => store.state.score.result);
  const isEnabledScore = computed(() => store.state.score.enabled);
  const isShowScore = computed(() => isEnabledScore.value && scoreSongInfo.value?.isScore && !mvIsHide.value);
  const getLevelAndDesc = computed(() => store.getters['base/getLevelAndDesc'])
  const isHideScoreResult = computed(() => store.state.isHideScoreResult);
  const mvIsHide = computed(() => store.state.mvIsHide);
  
  const $gradeResultModal = inject('$gradeResultModal');
  let gradeResultModal = null;

  const handleToggleEnabledScore = () => {
    if (!isEnabledScore.value) {
      store.dispatch('score/open');
      TSMicrophoneInstance.requestRecordPermission()
      return
    }
    Toast('关闭打分');
    store.dispatch('score/close');
  };

  const startScore = (payload) => {
    TSScoreInstance.start(payload);
  }

  const stopScore = ({from = '', isMvHide = false} = {}) => {
    STOP_SCORE_FROM = from
    if (!scoreSongInfo.value?.songId) return;
    console.log('stopScore', from)
    STOP_SCORE_MVISHIDE = isMvHide
    TSScoreInstance.stop()
  }

  const resetScoreResult = () => {
    store.commit('score/RESET_SCORE_RESULT')
  }

  const setScoreResult = async (payload) => {
    await store.commit('score/SET_SCORE_RESULT', payload)
  }

  const handleScoreRelated = async (_song, { from = '' } = {}) => {
    if (!enableScoring) return

    const { is_score, songid, music_name } = _song;
    console.log('是否开启打分：', isEnabledScore.value && is_score, scoreSongInfo.value, _song);

    if (is_score && isEnabledScore.value) {
      if (scoreSongInfo.value?.songId) {
        stopScore({
          from,
          isMvHide: mvIsHide.value,
        });
        await store.dispatch('score/updateCurSongIsSupport', {});
      }

      await store.dispatch('score/updateCurSongIsSupport', {
        songId: songid,
        isScore: is_score,
        // scoreSourceUrl,
        musicName: music_name,
        ..._song
      });

      setTimeout(() => {
        console.log('开始为歌曲评分:', music_name);
        startScore({
          ..._song,
          songId: songid,
          // score_source_url: scoreSourceUrl,
        });
      }, 1000);
    } else if (scoreSongInfo.value?.songId) {
      stopScore();
      await store.dispatch('score/updateCurSongIsSupport', {});
    }
  }

  const showGradeResultModal = async () => {
    if (!isEnabledScore.value || STOP_SCORE_MVISHIDE) return;
    console.log('showGradeResultModal', scoreResult.value);
    
    const levelAndDesc = await getLevelAndDesc.value(scoreResult.value.score);
    sendLog({
      event_type: '10000~50000',
      event_name: 30149,
      event_data: {
        str1: 'MV页',
        str2: '评分结果',
        str3: '评分结果展示',
        str4: 'show',
      },
    });

    const results = store2.get('score-results') || [];
    results[0] = {
      ...results[0],
      ...levelAndDesc,
      ...scoreResult.value,
    };
    store2.set('score-results', results);

    store.commit('UPDATE_HIDE_SCORE_RESULT', '-1');
    await store.commit('score/SET_SCORE_DATA', {});

    if (mvIsHide.value) {
      await handlePlayNext();
      return;
    }

    await store.commit('UPDATE_HIDE_SCORE_RESULT', '999');

    TSMediaInstance.stop()
    gradeResultModal = $gradeResultModal.show({
      ...scoreSongInfo.value,
      ...levelAndDesc,
      ...scoreResult.value,
      onConfirm: async () => {
        await store.commit('UPDATE_HIDE_SCORE_RESULT', '1');
        await store.dispatch('score/updateCurSongIsSupport', {})
        if (STOP_SCORE_FROM === 'replay') {
          handlePlayReplay()
          return
        } else if (STOP_SCORE_FROM === 'useOrder') {
          handlePlayNext()
          return
        }
        handlePlayNext()
      },
    });
  };

  const handlePlayReplay = () => {
    eventBus.emit('video-control-replay');
    eventBus.emit('irc-control-replay');
    store.commit('UPDATE_CURR_IYRIC_INDEX', -1)
    STOP_SCORE_FROM = ''
  }

  const handlePlayNext = async () => {
    await store.dispatch('score/updateScoreInfo', {});
    await store.dispatch('score/updateCurSongIsSupport', {});
    eventBus.emit('handle-video-next');
    await store.commit('UPDATE_HIDE_SCORE_RESULT', '1');
    hideGradeResultModal();
    STOP_SCORE_FROM = ''
    STOP_SCORE_MVISHIDE = false
  };

  const hideGradeResultModal = () => {
    store.commit('UPDATE_HIDE_SCORE_RESULT', '1')
    if (gradeResultModal) {
      gradeResultModal.hide();
    }
    STOP_SCORE_MVISHIDE = false
  };

  watch(isHideScoreResult, (val) => {
    if (val === '1') {
      console.log('isHideScoreResult', val)
      hideGradeResultModal()
    }
  }, {
    immediate: true,
    deep: true,
  })

  return {
    isEnabledScore,
    scoreSongInfo,
    startScore,
    stopScore,
    handleToggleEnabledScore,
    resetScoreResult,
    setScoreResult,
    handleScoreRelated,
    showGradeResultModal,
    getLevelAndDesc,
    isShowScore,
    isHideScoreResult,
  }
}