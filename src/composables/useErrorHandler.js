import { onUnmounted, getCurrentInstance } from 'vue'
import Logger from '@/utils/logger'

let globalHandlerInstalled = false

export default function useErrorHandler(options = {}) {
  // options: { onError, onPromiseRejection, onVueError, autoReport }
  const logger = new Logger(options)

  function captureError(error, info = {}) {
    logger.error(error, info)
    if (options.onError) options.onError(error, info)
  }

  function capturePromiseRejection(event) {
    logger.error(event.reason, { type: 'unhandledrejection' })
    if (options.onPromiseRejection) options.onPromiseRejection(event)
  }

  function captureVueError(err, vm, info) {
    logger.error(err, { type: 'vue', vm, info })
    if (options.onVueError) options.onVueError(err, vm, info)
  }

  function installGlobalErrorHandler() {
    if (globalHandlerInstalled) return
    window.addEventListener('error', e => captureError(e.error || e.message, { type: 'window' }))
    window.addEventListener('unhandledrejection', capturePromiseRejection)
    if (typeof getCurrentInstance === 'function') {
      // Vue3全局错误处理
      const app = getCurrentInstance()?.appContext?.app
      if (app) {
        app.config.errorHandler = captureVueError
      }
    }
    globalHandlerInstalled = true
  }

  installGlobalErrorHandler()

  onUnmounted(() => {
    // 可选：移除监听
  })

  return {
    captureError,
    installGlobalErrorHandler,
    logger
  }
} 