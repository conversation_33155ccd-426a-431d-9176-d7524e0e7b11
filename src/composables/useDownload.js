import { computed } from 'vue'
import { useStore } from 'vuex'
import { DownloadStatus } from '@/utils/download';
import { setOrderedList } from '@/utils/historyCache';
import store2 from 'store2'

export default function useDownload() {
  const store = useStore()

  const songItem = computed(() => store.state.videoPlayerHistory.songItem)

  const getSongDownloadState = () => {
    // const res = TSDownloadQueueInstance.getSongDownloadState(payload)
    // if (res && res.length) {
    //   store.commit('UPDATE_SONG_DOWNLOAD_STATE', res)
    // }
  }

  // const getIsLocalSong = (payload) => {
  //   return TSDownloadQueueInstance.isLocalSong(payload)
  // }

  const getIsLocalSong = async (payload) => {
    return await store.dispatch('download/getIsLocalSong', payload)
  }


  const startDownload = (song) => {
    store.dispatch('download/startDownload', song)
  }

  const checkAutoDownload = () => {
    store.dispatch('download/checkAutoDownload')
  }

  const assertDownload = async (song, hasFn) => {
    if (song.downloadState === DownloadStatus.FAILED) {
      startDownload(song)
    }
  }

  const handleDownloadProgress = ({ songId, progress }) => {
    store.commit('UPDATE_SONG_DOWNLOAD_STATE', {
      songid: songId,
      downloadProgress: progress,
      downloadState: DownloadStatus.PROCESSING,
      src: '',
    });
    store.commit('download/UPDATE_CURRENT_TASK_PROGRESS', progress);
  };

  const handleDownloadStart = async ({
    tmpFilePath,
  }) => {
    // 更新当前下载任务的临时下载地址
    store.commit('download/UPDATE_CURRENT_TASK_TMPFILEPATH', tmpFilePath);
  };

  const handleDownloadDone = async ({ songId, path }) => {
    const updatedSong = {
      songid: songId,
      downloadState: DownloadStatus.SUCCESS,
      downloadProgress: 100,
      src: path,
    };
  
    store.commit('UPDATE_SONG_DOWNLOAD_STATE', updatedSong);
    console.log(
      '播放器内监听下载完成: 当前播放器歌曲-',
      songItem.value.songid,
      '下载完成歌曲:',
      songId
    );
    await store.commit('download/RESET_CURRENT_TASK');
    checkAutoDownload();
    setOrderedList();
  
    const LOCALSONGS = store2('LOCALSONGS') || [];
    store2('LOCALSONGS', [updatedSong, ...LOCALSONGS]);
  };
  

  const handleDownloadError = async({ songId, errorCode, msg }) => {
    console.log('下载失败', songId, errorCode, msg )
    await store.commit('UPDATE_SONG_DOWNLOAD_STATE', {
      songid: songId,
      downloadState: DownloadStatus.FAILED,
    });
    await store.commit('download/RESET_CURRENT_TASK');
    checkAutoDownload();
    setOrderedList();
  };

  return {
    getSongDownloadState,
    getIsLocalSong,
    startDownload,
    assertDownload,
    checkAutoDownload,
    handleDownloadProgress,
    handleDownloadStart,
    handleDownloadDone,
    handleDownloadError,
  }
}