import { computed, ref, watch } from 'vue'

export default function useSuspense() {

  const LoadingStatus = {
    READY: 'ready',
    LOADING: 'loading',
    DONE: 'done',
    EMPTY: 'empty'
  }

  const LoadingStatusTip = {
    [LoadingStatus.READY]: '加载中',
    [LoadingStatus.LOADING]: '加载中',
    [LoadingStatus.DONE]: '',
    [LoadingStatus.EMPTY]: '暂无内容',
  }

  let status = ref(LoadingStatus.READY)

  const statusTip = computed(() => {
    return LoadingStatusTip[status.value]
  })

  const setLoadingStatusTip = (payload) => {
    for(const key in payload) {
      LoadingStatusTip[key] = payload[key]
    }
  }

  const ready = () => {
    status.value = LoadingStatus.READY
  }

  const loading = () => {
    status.value = LoadingStatus.LOADING
  }

  const done = () => {
    status.value = LoadingStatus.DONE
  }

  const empty = () => {
    console.log(LoadingStatus.EMPTY)
    status.value = LoadingStatus.EMPTY
  }

  return {
    LoadingStatus,
    status,
    statusTip,
    setLoadingStatusTip,
    ready,
    loading,
    done,
    empty,
  }
}