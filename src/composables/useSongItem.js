import { computed } from 'vue'
import store from '@/store'
import Toast from '@/utils/toast'
import { get } from 'lodash'
import useLoginValid from './useLoginValid'
import useVip from './useVip'
import useDownload from './useDownload'
import { setOrderedList, setAlreadyList } from '@/utils/historyCache'
import { DownloadStatus } from '@/utils/download'
import eventBus from '@/utils/event-bus'
import useScore from '@/composables/useScore';
import { TSMediaInstance } from '@/packages/TSJsbridge';
import { sendLog } from '@/directives/v-log/log';

export default function useSongItem() {
  const { showLoginQrcode, isLogin } = useLoginValid()
  const { showVipQrcode, isVipUser } = useVip()
  const { handleScoreRelated } = useScore()

  const { getIsLocalSong, checkAutoDownload } = useDownload()

  const orderedList = computed(() => store.state.orderedList)
  const orderedListNumber = computed(() => store.state.orderedList.length)
  const paused = computed(() => store.state.videoPaused)

  const validSong = ({ is_vip, songid }, _option={}) => {
    console.log('validSong', _option)
    const { vipLog = {}, fr = '' } = _option

    // vip歌曲
    if (is_vip) {
      if (_option.from === 'searchBar' && !paused.value) return true

      if (!isVipUser.value) {
        const finalFr = Array.isArray(fr) ? (isLogin.value ? fr[1] : fr[0]) : fr
        console.log('validSong', finalFr)
        showVipQrcode({
          songid,
          isLogin: isLogin.value,
          logData: {
            ...vipLog,
            str7: songid,
          },
          fr: finalFr
        })
        return false
      }
    }
    return true
  }

  const orderOption = {
    position: '',
    isPushOrdered: true,
    immediate: false,
    from: {}, // 点歌来源 - 打点使用
    ponitActionLog: {}, // 点歌的点位信息 - 10000 ~ 50000 打点使用
    // enabledMvShow: false, // 启用MV页，默认关闭 - 当开启时自动打开mv页；immediate同为true时才生效
    // useFirstSongAutoPlay: false, // 点播的第一首歌曲是否启用自动播放，默认关闭
    isAlreadyStick: false, // 是否是已唱列表置顶歌曲
    beforePushOrdered: () => {
      if (orderedListNumber.value >= 99) {
        Toast('您已经点了很多歌曲啦，快去演唱吧')
        return false
      }
      return true
    },
    beforeImmediate: () => {
      // console.log('beforeImmediate')
    },
    afterOrder: () => {
      // console.log('afterOrder')
      setOrderedList()
      setAlreadyList()
    }
  }

  const orderSong = async (song, option) => {
    try {
      const _song = { ...song }
      const _option = { ...orderOption, ...option }

      const isLocalSong = await getIsLocalSong(_song)
      console.log('点歌:', _song.music_name, '是否本地', isLocalSong, '是否加入已点:', !!option?.isPushOrdered, '立即点歌', !!_option?.immediate)
      console.log('点歌log', _option.orderLog, 'vipLog', _option.vipLog)

      if (!_song.isAIMV && !isLocalSong && _song.downloadState === DownloadStatus.SUCCESS) {
        store.commit('UPDATE_SONG_DOWNLOAD_STATE', {
          songid: _song.songid,
          downloadProgress: 0,
          downloadState: DownloadStatus.UNSTORAGE,
          src: '',
        });
      }

      if (!isLocalSong && !validSong(_song, _option)) {
        return
      }

      if (_option.isOpenFullScreen) {
        store.commit('UPDATE_MV_ISHIDE', false)
      }

      const isExistOrdered = orderedList.value.find((orderedSong) => {
        return orderedSong.songid === _song.songid
      })

      _option.beforeImmediate.call()

      if (_option.immediate) {
        TSMediaInstance.stop()
        // 直接点播的歌曲应该直接播放
        if (orderedListNumber.value > 0) {
          const firstSong = orderedList.value[0]
          await store.commit('DELETE_SONG_ORDER_LIST', 0)
          await store.commit('PUSH_SONG_TO_ALREADY_LIST', firstSong)
        }
        await store.commit('ADD_SONG_TO_ORDERED_LIST', {
          songItem: _song,
          index: 0
        })
        await store.commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', _song)
        await store.commit('UPDATE_VIDEO_PLAYER_RECOVERY', true)
        if (!isLocalSong) {
          await store.dispatch('download/stopDownload');
        }

        console.log('0117', _song)

        // await store.commit('UPDATE_MV_ISHIDE', false)
        eventBus.emit('handle-video-play', { isLocalSong });

        _option.afterOrder.call()
        setOrderedList()

        sendLog({
          event_type: 'click',
          event_name: 121,
          event_data: {
            str4: 'click',
            str5: 121,
            str6: _song.songid,
            str8: _song.is_vip ? 1 : 2,

            ..._option.orderLog,

            str3: _option.orderLog.str3 ? _option.orderLog.str3 : '立即开唱',
          }
        })
        return
      }

      if (_option.isPushOrdered) {
        if (isExistOrdered && !isLocalSong) {
          Toast('云端歌曲不可重复加入已点')
          return
        }

        const isConditionMet = _option.beforePushOrdered.call()
        if (!isConditionMet) return

        if (orderedListNumber.value === 0 || isLocalSong) {
          Toast('歌曲已加入已点列表')
          store.dispatch('addSongToOrderedList', _song)
        } else if (!isExistOrdered) {
          Toast('歌曲已加入已点列表')
          store.dispatch('addSongToOrderedList', _song)
        }

        if (isLocalSong) {
          store.commit('UPDATE_SONG_DOWNLOAD_STATE', {
            songid: _song.songid,
            downloadState: DownloadStatus.SUCCESS,
            downloadProgress: 100,
            src: _song.src,
          })
        }
      }

      if (!_option.isAlreadyStick) {
        checkAutoDownload();
      }
      
      _option.afterOrder.call()

      setOrderedList()

      sendLog({
        event_type: 'click',
        event_name: 121,
        event_data: {
          str4: 'click',
          str5: 121,
          str6: _song.songid,
          str8: _song.is_vip ? 1 : 2,

          ..._option.orderLog,
          str3: _option.orderLog.str3 ? _option.orderLog.str3 : _option.orderArea === 'song-item' ? '点击歌曲信息' : '加入已点',
        }
      })
    } catch (error) {
      console.log('useSongItem orderSong error', error)
    }
  }

  return {
    validSong,
    orderSong,
  }
}