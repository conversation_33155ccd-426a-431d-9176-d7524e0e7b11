import { onMounted, onUnmounted, computed } from 'vue';
import eventBus from '@/utils/event-bus';
import { TSMediaInstance, TSWebEventInstance, TSBaseInfoInstance } from '@/packages/TSJsbridge';
import Toast from '@/utils/toast'
import store from '@/store'
import get from 'lodash/get'
import useM3u8 from '@/composables/useM3u8';
// import useOrder from '@/composables/useOrder';

export default function useVideoPlayer(song, emit) {
  const { addCurrSongM3u8 } = useM3u8();

  const playingSong = computed(() => get(store.state.orderedList, '[0]', {}))

  const checkAudioFocus = ()=>{
    // if ((!isAlreadyEnterMv.value || !isSingStatus.value) && mvIsHide.value) {
    //   if (orderedListNumber.value > 0) {
    //     Toast('尚未进入欢唱页K歌，暂无法执行该操作')
    //   }
    //   return
    // }
    return TSBaseInfoInstance.requestAudioFocus()
  }

  // 视频音轨切换事件
  const onVideoEventsAudioTrackSwitched = (id) => {
    try {
      console.log('AUDIO_TRACK_SWITCHED', id);
      emit('onAudioTracksSwitched', id)
    } catch (error) {
      console.error('Error in onVideoEventsAudioTrackSwitched:', error)
    }
  }

  // 错误处理
  const onVideoEventsError = (data) => {
    emit('error', data);
    store.dispatch('setVideoPaused', true);
    eventBus.emit('toggle-cover', true)
  }

  // 音轨切换
  const handleSwitchAudioTrack = (audioTrack) => {
    console.log(
      'video-player handleSwitchAudioTrack',
      JSON.stringify(audioTrack),
    );
    TSMediaInstance.selectTrack(audioTrack.id);
    store.commit('SAVE_VIDEO_PLAYER_HISTORY_ENABLED_AUDIO_TRACK', audioTrack)
  };

  // 播放
  const handleControlVideoPlay = (s, { isLocalSong = false } = {}) => {
    if (!checkAudioFocus()) return

    console.log('video-control-play', s && s.music_name, isLocalSong ? '本地播放' : '边下边播');
    if (isLocalSong) {
      TSMediaInstance.play(s);
    } else {
      TSMediaInstance.startPlay(s);
    }
  };

  const handleControlVideoResume = () => {
    if (!checkAudioFocus()) return
    console.log('handleControlVideoResume')
    
    const result = TSMediaInstance.resume();
    if (result.resultCode !== 0 && result.resultCode !== 3) {
      console.log('handleControlVideoResume failed')
      handleControlVideoReplay()
    } else {
      emit('play')
    }
  }

  // 暂停
  const handleControlVideoPause = () => {
    try {
      TSMediaInstance.pause();
    } catch (error) {
      console.error('Error in handleControlVideoPause:', error)
    }
  }

  const handleControlVideoStop = () => {
    try {
      console.log('handleControlVideoStop')
      TSMediaInstance.stop();
    } catch (error) {
      console.error('Error in handleControlVideoStop:', error)
    }
  }

  // 重播
  const handleControlVideoReplay = () => {
    console.log('handleControlVideoReplay')
    if (!checkAudioFocus()) return
    TSMediaInstance.replay(playingSong.value);
    addCurrSongM3u8()
  }

  // video canplay事件
  const handleCanPlay = () => {
    try {
      emit('canplay')
    } catch (error) {
      console.log('Error in handleCanPlay:', error)
      console.error('Error in handleCanPlay:', error)
    }
  }

  // video ended事件
  const handleVideoEnded = () => {
    eventBus.emit('toggle-cover', true)
    console.log('handleVideoEnded')
    emit('ended')
    store.dispatch('setVideoPaused', true);
  }

  // video 切换画质下一首生效
  const handleChangeQualityTag = () => {
    try {
      Toast('画质切换后，重唱或下一首歌曲生效')
    } catch (error) {
      console.error('Error in handleChangeQualityTag:', error)
    }
  }

  const handleControlVideoNext = () => {
    try {
      emit('next')
    } catch (error) {
      console.error('Error in handleControlVideoNext:', error)
    }
  }

  const attachVideoPlayerEvents = () => {
    eventBus.on('switch-audio-track', handleSwitchAudioTrack)
    eventBus.on('video-control-play', handleControlVideoPlay)
    eventBus.on('video-control-resume', handleControlVideoResume)
    eventBus.on('video-control-pause', handleControlVideoPause)
    eventBus.on('video-control-replay', handleControlVideoReplay)
    eventBus.on('video-control-next', handleControlVideoNext)
    eventBus.on('video-quality-change', handleChangeQualityTag)
    eventBus.on('video-control-stop', handleControlVideoStop)

    eventBus.on('handleDownloadStart', handleDownloadStart);
    eventBus.on('updateDownloadProgress', handleUpdateDownloadProgress);
    eventBus.on('handleDownloaded', handleDownloaded);
    eventBus.on('handleDownloadFail', handleDownloadFail);

    TSWebEventInstance.on('handlePlayStart', () => {
      eventBus.emit('toggle-cover', false)
      store.dispatch('setVideoPaused', false);
      store.commit('UPDATE_MV_INIT_AUTOPLAY', 1);
      store.commit('UPDATE_IS_SING_STATUS', true)
      emit('play');
    });
    TSWebEventInstance.on('handlePlayPause', async () => {
      // await nextTick()
        store.dispatch('setVideoPaused', true);
      emit('pause');
    });
    TSWebEventInstance.on('handlePlayStop', () => {
      eventBus.emit('toggle-cover', true)
      store.dispatch('setVideoPaused', true);
      emit('pause');
    });
    TSWebEventInstance.on('handlePlayError', onVideoEventsError);
    TSWebEventInstance.on('handlePlayCompletion', handleVideoEnded);
    TSWebEventInstance.on('handlePlayPrepared', handleCanPlay);
    TSWebEventInstance.on('handleSelectTrack', onVideoEventsAudioTrackSwitched);
    // TSWebEventInstance.on('handleTimeupdate', handleTimeupdate);
  };

  const detachVideoPlayerEvents = () => {
    try {
      console.log('detach-video-player-events')
      eventBus.off('switch-audio-track', handleSwitchAudioTrack)
      eventBus.off('video-control-play', handleControlVideoPlay)
      eventBus.off('video-control-pause', handleControlVideoPause)
      eventBus.off('video-control-replay', handleControlVideoReplay)
      eventBus.off('video-control-resume', handleControlVideoResume)
      eventBus.off('video-control-stop', handleControlVideoStop)

      eventBus.off('handleDownloadStart', handleDownloadStart);
      eventBus.off('updateDownloadProgress', handleUpdateDownloadProgress);
      eventBus.off('handleDownloaded', handleDownloaded);
      eventBus.off('handleDownloadFail', handleDownloadFail);
      eventBus.off('video-quality-change', handleChangeQualityTag)
    } catch (error) {
      console.error('Error in detachVideoPlayerEvents:', error)
    }
  }

  const handleDownloadStart = ({
    songId,
    mediaFileName,
    tmpFilePath,
    fileLength,
  }) => {
    try {
      emit('on-download-start', {
        songId,
        mediaFileName,
        tmpFilePath,
        fileLength,
      });
    } catch (error) {
      console.error('Error in handleDownloadStart:', error)
    }
  };
  const handleUpdateDownloadProgress = ({ songId, progress }) => {
    try {
      emit('on-download-progress', { songId, progress });
    } catch (error) {
      console.error('Error in handleUpdateDownloadProgress:', error)
    }
  };
  const handleDownloaded = ({ songId, path, fileName, fileLength }) => {
    try {
      console.log('下载完成', songId, path, fileName, fileLength);
      emit('on-download-done', { songId, path, fileName, fileLength });
    } catch (error) {
      console.error('Error in handleDownloaded:', error)
    }
  };
  const handleDownloadFail = ({ songId, errorCode, msg }) => {
    try {
      console.log('下载失败', songId, errorCode, msg);
      emit('on-download-error', { songId, errorCode, msg });
    } catch (error) {
      console.error('Error in handleDownloadFail:', error)
    }
  };

  onMounted(() => {
    attachVideoPlayerEvents()
  })
  onUnmounted(() => {
    try {
      console.log('onUnmounted: VideoPlayer')
      detachVideoPlayerEvents()
    } catch (error) {
      console.error('Error in onUnmounted:', error)
    }
  })

  return {
  }
}