import {computed, ref, onMounted, watch} from 'vue'
import { useStore } from 'vuex'
import Toast from '@/utils/toast'
import { TSRecordInstance, TSWebEventInstance } from '@/packages/TSJsbridge';

export default function useDownload() {
  const store = useStore()
  const isOriginal = ref(false)

  const isLogin = computed(() => !!store.state.userInfo.unionid)
  const isRecording = computed(() => store.state.isRecording)
  const isRecordingOpen = computed(() => store.state.isRecordingOpen)
  const videoPlayer = computed(() => store.state.videoPlayerHistory);
  const hasMic = computed(() => store.state.microphones.some(mic => mic.status));
  const isDisableRecord = computed(() => !isLogin.value || videoPlayer.value.enabledAudioTrack.id === 1 || !hasMic.value);

  const startRecording = () => {
    if (isDisableRecord.value) {
      let message = isLogin.value ? '开启原唱状态下无法录音哦～' : '未登录无法使用录音功能哦～';
      if (!hasMic.value) {
        message = '未连接麦克风无法使用录音功能哦～';
      }
      Toast({
        message,
        duration: 2000,
      });
  
      return;
  }  
    if (isRecording.value) {
      stopRecording()
      return
    }
    try {
      console.log('useRecord', 'startRecording')
      store.commit('UPDATE_RECORD_STATE', true)
      TSRecordInstance.startRecord()
    } catch (error) {
      console.log('startRecording error', error)
    }
  }
  
  const stopRecording = () => {
    try {
      console.log('useRecord', 'stopRecording')
      store.commit('UPDATE_RECORD_STATE', false)
      TSRecordInstance.stopRecord()
    } catch (error) {
      console.log('stopRecording error', error)
    }
  }

  const onFindRecordingStatus = () => {
    // 壳需要获取当前录音状态
    console.log('壳获取当前录音状态', isRecording.value ? '录音中' : '未录音')
    return isRecording.value
  };

  const onRecordStartState = (code, msg) => {
    console.log('onRecordStartState', code, msg)
    if (code == 1) {
      Toast(msg)
      stopRecording(code)
    }
  }

  const onRecordUploadState = (code, errCode, msg) => {
    console.log('onRecordUploadState', code, errCode, msg)
    if (code == 1) {
      if (errCode == 0) {
        Toast({
          message: '录音不足 15S，保存失败',
          duration: 2000,
        })
      } else {
        Toast('上传失败')
      }
    }
    if (code == 0) {
      Toast('录音保存成功！')
    }
  }

  watch(isDisableRecord, (val) => {
    if (val) {
      console.log('状态发生改变，停止录音')
      stopRecording()
    }
  })
  
  const attachEvents = () => {	
    TSWebEventInstance.on('findRecordingStatus', onFindRecordingStatus);
    TSWebEventInstance.on('handleRecordStartState', onRecordStartState);
    TSWebEventInstance.on('handleRecordUploadState', onRecordUploadState);
  }

  onMounted(() => {
    attachEvents()
  })
  
  return {
    isRecording,
    isOriginal,
    startRecording,
    stopRecording,
    isDisableRecord,
    isRecordingOpen,
  }
}