import { computed } from 'vue'
import { useStore } from 'vuex'
import useSongItem from './useSongItem'
import useOrder from './useOrder'
import { setOrderedList, setAlreadyList } from '@/utils/historyCache'
import { nanoid } from 'nanoid'
import eventBus from '@/utils/event-bus'

export default function useAlready() {
  const store = useStore()
  const { orderSong: normalOrderSong } = useSongItem()
  const { stickSongToTop: stickSongToOrderedTop } = useOrder()

  const orderedListNumber = computed(() => store.state.orderedList.length)

  const alreadyList = computed(() => {
    const list = store.state.alreadyList;
    list.forEach((item, index) => {
      if (!item._i)
        item._i = 'k_' + nanoid();
        item._index = index;
    });
    return list;
  });

  const addSong = async (songItem) => {
    await store.commit('PUSH_SONG_TO_ALREADY_LIST', songItem)
    console.log('alreadyList', alreadyList.value)
  }

  // 已唱列表点歌
  const orderSong = async (songItem) => {
    normalOrderSong(songItem, {
      immediate: true,
    })
    setOrderedList()
  }

  // 置顶
  const stickSongToTop = (songItem) => {
    if (orderedListNumber.value === 0) {
      normalOrderSong(songItem, {
        immediate: true,
      })
      eventBus.emit('close-order-song-control-popup')
      return
    }
    normalOrderSong(songItem, {
      isPushOrdered: true,
      afterOrder: () => {
        stickSongToOrderedTop(-999)
      }
    })
  }

  // 删除
  const deleteSong = (index) => {
    store.commit('DELETE_SONG_ALREADY_LIST', index)
    setAlreadyList()
  }


  const addSongToOrdered = (songItem) => {
    if (orderedListNumber.value === 0) {
      normalOrderSong(songItem, {
        immediate: true,
      })
      return
    }

    normalOrderSong(songItem, {
      isPushOrdered: true,
    })
    // store.commit('PUSH_SONG_TO_ORDERED_LIST', { song: songItem })
  }

  return {
    orderSong,
    stickSongToTop,
    deleteSong,
    alreadyList,
    addSong,
    addSongToOrdered,
  }
}
