import { computed } from 'vue'
import { useStore } from 'vuex'
import get from 'lodash/get'
import useSetting from './useSetting'
import { differenceInMilliseconds } from 'date-fns'
import { getSongM3U8, getSongToken, getSongLrc } from '@/service/song'
import { TSMediaInstance } from '@/packages/TSJsbridge';

export default function useM3u8() {
  const store = useStore()
  const { setting } = useSetting()
  const orderedList = computed(() => store.state.orderedList);
  const oldServerTime = computed(
    () => store.state.videoPlayerHistory.songItem.serverTime
  )
  const tokenExp = computed(
    () => store.state.videoPlayerHistory.songItem.tokenExp
  )

  let abortController = null;

  // 添加当前歌曲的歌词和画质数据
  const addCurrSongM3u8 = async () => {
    const song = orderedList.value[0];
    if (!song || song.isAIMV) return; // 如果没有歌曲，直接返回

    // 如果有未完成的请求，取消它
    if (abortController) {
      console.log('addCurrSongM3u8 abortController abort')
      abortController.abort('请求被取消，因为有新的请求发起。');
    }

    // 创建新的 AbortController
    abortController = new AbortController();

    try {
      // 获取歌词
      console.log(`开始获取歌词 [${song.music_name} (ID:${song.songid})]`);
      const songLrc = await getSongLrc(song.songid, song.lrc);
      const lrcData = get(songLrc, 'lrc.json', []);

      const isEmpty = !lrcData.length;
      store.commit('UPDATE_CURR_SONG_LRC', {
        lrc: lrcData,
        empty: isEmpty
      });
      store.commit('UPDATE_SONG_LRC', lrcData);
      store.commit('UPDATE_SONG_LRC_EMPTY', isEmpty);

      if (isEmpty) {
        console.warn(`未找到歌词内容 [${song.music_name} (ID:${song.songid})]`);
      } else {
        TSMediaInstance.setSongLrc(song.songid, lrcData);
        console.log(`歌词获取成功 [${song.music_name}]，歌词条目数: ${lrcData.length}`);
      }
    } catch (error) {
      store.commit('UPDATE_CURR_SONG_LRC', { lrc: [], empty: true });
      store.commit('UPDATE_SONG_LRC', []);
      store.commit('UPDATE_SONG_LRC_EMPTY', true);
      console.error(`歌词加载失败 [${song?.music_name || '未知歌曲'} (ID:${song?.songid || '未知ID'})]`, error);
    }

    try {
      // 发起请求时传入 signal
      const { is_m3u8 = {} } = await getSongM3U8(song.songid, {
        signal: abortController.signal,
      });

      // 处理可用画质
      const availableQualities = Object.keys(is_m3u8).filter(
        (quality) => is_m3u8[quality] === 1
      );
      console.log(`${song.music_name} 可用的画质:`, availableQualities);
      store.commit('UPDATE_SONG_AVAILABLE_QUALITIES', availableQualities);

      // 选择画质
      const quality = availableQualities.includes(setting.value.quality)
        ? setting.value.quality
        : availableQualities[0];
      store.commit('CHANGE_PLAYING_MV_QUALITY', quality);
      console.log(
        '当前在播的画质:',
        quality,
        '设置的画质',
        setting.value.quality
      );
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('请求被取消：', error.message);
      } else {
        console.error('添加当前歌曲的m3u8资源失败:', error);
        store.commit('CHANGE_PLAYING_MV_QUALITY', 480);
      }
    } finally {
      abortController = null; // 重置 AbortController
    }
  };

  const setCurrSongToken = async () => {
    const songData = await getSongToken()
    const currServerTime = get(songData, 'serverTime', 0)
    // 与exp对比前后服务器时间的差值
    const ts = differenceInMilliseconds(new Date(currServerTime), new Date(oldServerTime.value)) < 1000 * tokenExp.value
    // console.log(
    //   new Date(currServerTime),
    //   new Date(oldServerTime.value),
    //   differenceInMilliseconds(new Date(currServerTime), new Date(oldServerTime.value)),
    //   ts
    // )
    store.commit(
      'UPDATE_CURR_SONG_TOKEN',
      {
        token: get(songData, 'token', ''),
        tokenExp: get(songData, 'tokenExp', 5*60),
        serverTime: currServerTime
      }
    )
    store.dispatch('updateCurrSongToken', {
      token: get(songData, 'token', ''),
      tokenExp: get(songData, 'tokenExp', 5 * 60),
      serverTime: currServerTime
    })
    TSMediaInstance.updateToken({
      token: get(songData, 'token', ''),
      tokenExp: get(songData, 'tokenExp', 5 * 60),
      serverTime: currServerTime
    })
    return ts
  }

  return {
    addCurrSongM3u8,
    setCurrSongToken,
  }
}