// src/workers/exitWorker.js
let timer = null;

self.onmessage = function (e) {
  const { status } = e.data;

  if (status === 1 || status === 3) {
    console.log('onCheckExit: status is 1 or 3, setting timer to exit');
    if (timer !== null) {
      clearTimeout(timer);
      timer = null;
    }
    timer = setTimeout(() => {
      console.log('Executing TSNativeInstance.exit');
      self.postMessage('exit');
    }, 30 * 60 * 1000); // 30 分钟 = 30 * 60 秒 * 1000 毫秒
  } else {
    console.log('onCheckExit: status is 0, clearing timer');
    clearTimeout(timer);
    timer = null;
  }
};