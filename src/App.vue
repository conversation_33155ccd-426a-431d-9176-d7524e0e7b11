<template>
  <div>
    <div class="app-main" :class="[
      themeClass,
      {
        'is-bg-transparent': !mvIsHide || routeName === 'home',
        'app-main-lrc': !mvIsHide && currentSize === 'small'
      }
    ]">
      <div v-show="mvIsHide" class="bg-circle"></div>
      <router-view v-show="mvIsHide" v-slot="{ Component }">
        <keep-alive>
          <component
            :key="$route.meta.key"
            v-if="$route.meta.keepAlive"
            :is="Component"
          />
        </keep-alive>
      </router-view>
      <router-view v-show="mvIsHide" v-if="!$route.meta.keepAlive" />
      <!-- <SearchBarTips /> -->
      <BottomBar />
      <transition name="mv">
        <MvPageComponent v-show="!mvIsHide" />
      </transition>
      <VantStyle></VantStyle>
      <OrderSongControlPopup></OrderSongControlPopup>
      <Offline :curRoute="$route" />
      <Grade />
      <div class="lottie"><div id="lottieShowDiv"></div></div>
      <MobileOrder :canShow="isYSTipAccept" />
    </div>
    <!--  -->
    <YSTip  v-if="!isYSTipAccept" />
    <!-- <MvMiniDefault /> -->
  </div>
</template>

<script>
import {
  onBeforeMount,
  watch,
  computed,
  provide,
  onMounted,
  onBeforeUnmount,
  ref,
  nextTick,
  defineAsyncComponent,
} from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import useM3u8 from '@/composables/useM3u8';
import useActivity from '@/composables/useActivity';
import useNats from '@/composables/useNats';
import {
  getOrderedList,
  getAlreadyList,
  setLoginSendVipActivityTag,
  getLoginSendVipActivityTag,
  getMicActivityShowTag,
  getVipActivityShowTag,
  getSearchCache,
  getSearchSong,
  setVipExpireTag,
  getVipExpireTag,
} from '@/utils/historyCache';
import { Dialog } from 'vant';
import Toast from '@/utils/toast'
import { sendLog } from '@/directives/v-log/log';
import { setUid } from '@/utils/bl';
import { format, isWithinInterval, parseISO, addDays } from 'date-fns';
import formatStr from '@/constants/index';
import get from 'lodash/get';
import store2 from 'store2';
import { initNatsClient } from '@/packages/nats';
import useGlobalEvent from '@/composables/useGlobalEvent';
import useForceLogin from '@/composables/useForceLogin';
import { 
  TSMediaInstance,
  TSWebEventInstance,
  TSMicrophoneInstance,
  TSBaseInfoInstance,
  TSVehicleInstance,
  TSNativeInstance,
  TSVoiceInstance,
} from '@/packages/TSJsbridge';
import eventBus from '@/utils/event-bus'
import { checkConfig } from '@/service/base'
import { resolutionRatio } from '@/service/user'
import DownloadStatus from '@/components/download-status/index.vue'
import useLoading from '@/composables/useLoading';
import { getCarplayInfo } from '@/service/carplay-info'
import useQuality from '@/composables/useQuality'
import useTimeUpdate from '@/composables/useTimeUpdate'
import Grade from '@/components/grade/index.vue';
import { getLicence } from '@/service/base';
import useDownload from '@/composables/useDownload';
import { cacheImagesAsBase64 } from '@/utils/cacheImages';
import { cacheImages } from '@/constants'

import lottie from 'lottie-web';
import booing from '@/assets/lottie/booing/data.json';
import cheer from '@/assets/lottie/cheer/data.json';
import hands from '@/assets/lottie/hands/data.json';
import config from '@/config';

export default {
  name: 'App',
  components: {
    OrderSongControlPopup: defineAsyncComponent(() => import('@/components/order-song-control-popup/index.vue')),
    MvPageComponent: defineAsyncComponent(() => import('@/components/mv/index.vue')),
    // SearchBarTips: defineAsyncComponent(() => import('@/components/search-bar/tips.vue')),
    BottomBar: defineAsyncComponent(() => import('@/components/bottom-bar/index.vue')),
    LimitModal: defineAsyncComponent(() => import('@/components/teleport/limit-toast/index.vue')),
    Offline: defineAsyncComponent(() => import('@/components/offline/index.vue')),
    YSTip: defineAsyncComponent(() => import('@/components/ys-tip/index.vue')),
    MobileOrder: defineAsyncComponent(() => import('@/components/mobile-order/index.vue')),
    Grade,
  },
  setup() {
    let appLoading = ref(false);
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    const { addCurrSongM3u8 } = useM3u8();
    const { showLoading, hideLoading } = useLoading();
    const {
      showActivityModal,
      showActivityVip,
      showVipExpire,
      // showActivitySignIn,
    } = useActivity();
    const { showForceLogin } = useForceLogin();
    const { handleReceiveMessageFromNats } = useNats();
    const { installGlobalClickHandler, uninstallGlobalClickHandler } = useGlobalEvent()
    const { getDefaultQualityM3U8 } = useQuality()
    const { handleTimeupdate }  = useTimeUpdate()
    const { getIsLocalSong } = useDownload();

    const routeName = computed(() => route.name)
    const setting = computed(() => store.state.setting)
    const isLogin = computed(() => !!userInfo.value.unionid);
    const userInfo = computed(() => store.state.userInfo);
    const vipInfo = computed(() => store.state.vipInfo);
    const macAddress = computed(() => store.state.system.systemInfo.mac)
    const natsConfig = computed(() => store.state.base.nats);
    const icon = computed(() => store.state.base.icon);
    const launch_img = computed(() => store.state.base.launch_img);
    const net_status = computed(() => store.state.base.net_status);
    const needLoading = computed(() => store.state.base.needLoading);
    const isMvMode = computed(() => store.state.mvMode.mode === 'mv')
    const videoVolume = computed(() => store.state.videoVolume);

    const enableMedia = config.enableMedia
    const enableMediaMemory = config.enableMediaMemory
    const appStatus = computed(() => store.state.base.appStatus)
    const mediaVolume = store.state.mediaVolume

    let checkIsWatchLoginSendVip = ref(false);
    const loginSendVip = computed(() => store.state.activityInfo.loginSendVip);
    const openscreen_vip = computed(
      () => store.state.activityInfo.openscreen_vip.id
    );
    const isShowSignIn = computed(() => store.state.signin.isShowSignIn)

    const orderedSongIdMap = computed(() => store.state.orderedSongIdMap);
    provide('orderedSongIdMap', orderedSongIdMap);
    const curPlayingSongid = computed(
      () => store.state.videoPlayerHistory.songItem.songid
    );
    provide('playingSongId', curPlayingSongid);

    const videoPlayer = computed(() => store.state.videoPlayerHistory);
    const mvIsHide = computed(() => store.state.mvIsHide);
    const currentTask = computed(() => store.state.download.currentTask);
    const themeClass = computed(() => store.state.themeClass)
    const orderedList = computed(() => store.state.orderedList)

    const resolution = computed(() => store.state.carplayInfo.resolution)
    const isVip = computed(() => !!store.state.vipInfo.end_time)
    const isYSTipAccept = computed(() => store.state.storageYSTipAccept)
    const currentSize = computed(() => store.state.currentSize);
    let appStartTime = Date.now();
    let isShowLimitModal = ref(false)
    let limitParams = ref({})
    const natsInstance = ref(null)

    const loading = ref(false)
    //临时记录进来时系统的音量，用于退出时恢复
    let systemMediaVolume = -1
    let videoPaused = computed(() => store.state.videoPaused);

    const initApp = async () => {
      TSWebEventInstance.on('handleTimeupdate', handleTimeupdate);
      // let intervalTime = 8000;
      // setInterval(() => {
      //   handleTimeupdate(intervalTime);
      //   intervalTime += 500;
      // }, 500);
      await store.dispatch('system/getSystemInfo');

      store.dispatch('getSetting');
      store.dispatch('base/getBaseInfo');
      store.dispatch('base/getConfig');
      store.dispatch('base/reportSession');
      store.dispatch('abTest/setABTestVersion')
      // 初始化特斯拉top50常唱数据
      store.dispatch('oftenSing/initTop50List')
      store.dispatch('miniMv/fetchDefaultList')

      // 登录时更新云端画质到本地(非vip云端画质还为1080时，降级为720)
      let qualityNew = !isVip.value && resolution.value === '1080' ? '720' : resolution.value
      if (!qualityNew) qualityNew = '480' // 云端检测是否是新用户未设置过画质，新用户设置480
      store.commit('CHANGE_PLAYING_MV_QUALITY', qualityNew)
      store.dispatch('saveSetting', {
        ...setting.value,
        quality: qualityNew
      })
      if (qualityNew !== resolution.value  && userInfo.value.unionid) {
        resolutionRatio({ unionid: userInfo.value.unionid, quality: qualityNew})
      }

      // 启动上报
      sendLog({
        event_type: 'custom',
        event_name: 241,
        event_data: {
          _car_language: navigator.language,
          _car_time: Date.now(),
          memory_max: get(performance, 'memory.jsHeapSizeLimit', 0),
          memory_used: get(performance, 'memory.usedJSHeapSize', 0),
        },
      });
      // app打开上报
      sendLog({
        event_type: 'custom',
        event_name: 221,
        event_data: {
          start_time: format(appStartTime, formatStr),
        },
      });
      appLoading.value = true;
      store.commit('UPDATE_APP_START_TIME', appStartTime);
      // 查看运营活动
      store.dispatch('setActivityInfo');
      setTimeout(() => {
        console.log('拉取一次是否麦克风连接')
        TSMicrophoneInstance.hasThunderMic()
      }, 3000)

      setTimeout(() => {
        const skeleton = document.getElementById('Skeleton');
        if (skeleton) {
          skeleton.parentNode.removeChild(skeleton);
        }
      }, 5000)

      const { data = {} } = await getLicence();
      store.commit('SAVE_SERVICE_INFO', data);
    };

    const onUnload = () => {
      //方案一：只在这里存储一次 貌似车机不太行 换即时存储
      // setOrderedList()
      // setAlreadyList()

      if (appStartTime) {
        const unloadTime = Date.now();
        const stayTime = Math.round((unloadTime - appStartTime) / 1000);
        sendLog({
          event_type: 'custom',
          event_name: 222,
          event_data: {
            start_time: format(appStartTime, formatStr),
            end_time: format(unloadTime, formatStr),
            stay_time: stayTime,
          },
        });
      }
    };

    // 监听网络状态
    const onNetChange = (status) => {
      // console.log('onNetChange', status)
      // Toast('当前网络状态差，请检查网络状态');
      // store.commit('base/SET_NET_STATUS', status.type === 'online');
    };

    watch(isMvMode, (val) => {
      if (!val && !mvIsHide.value) {
        sendLog({
          event_type: 'show',
          event_name: '6007',
          event_data: {
          str1: '欢唱页',
          str2: '歌词模式',
          str3: '歌词模式展示',
          str4: 'show',
          }
        })
      }
    })

    watch(isLogin, async (val) => {
      if (val) {
        store.dispatch('download/checkAutoDownload', !!vipInfo.value.end_time)
      } else {
        if (currentTask.value.songid && currentTask.value.is_vip) {
          await store.dispatch('download/stopDownload')
          store.dispatch('download/checkAutoDownload', !!vipInfo.value.end_time)
        }
      }
      
      if (!val) {
        const quality = getDefaultQualityM3U8(setting.value.quality)
        console.log('getDefaultQualityM3U8', quality, TSMediaInstance)
        TSMediaInstance.setResolution(quality)
        store.dispatch('saveSetting', {
          ...setting.value,
          quality,
        })
        store.dispatch('collect/resetUserCollectList')
        // 重置0元购活动状态
        store.dispatch('zerobuy/resetZerobuyStatus')
        if (isShowSignIn.value) store.dispatch('signin/resetSignInData')
        Dialog.confirm({
          confirmButtonText: '我知道了',
          showCancelButton: false,
          title: '监测到您的账号在当前车机已下线',
          beforeClose: () => {
            showForceLogin()
            return true
          },
        }).then(() => {
          router.push({
            name: 'home',
          });

          sendLog({
            event_type: 'click',
            event_name: '6001',
            event_data: {
              str1: '首页',
              str2: '下线弹窗',
              str3: '确定按钮',
              str4: 'click',
            },
          });
        });

        setUid();
        // 退出登录时重置常唱数据
        store.dispatch('oftenSing/resetOftenSingList')
        TSBaseInfoInstance.notifyLoginInfoStatus(null)
      } else {
        store.dispatch('singTime/initSingTaskTime', userInfo.value.unionid)
        // 0元购活动任务及签到状态
        store.dispatch('zerobuy/updateZerobuyStatus', userInfo.value.unionid)
        store.dispatch('zerobuy/updateSigninStatus', userInfo.value.unionid)
        // 收藏功能下线
        // store.dispatch('collect/getUserCollectList', userInfo.value.unionid)
        if (isShowSignIn.value) store.dispatch('signin/setSignInData', userInfo.value.unionid)

        // 过期会员提醒 - 检测到登录用户会员过期时弹运营弹框 - 每天只弹一次
        if (vipInfo.value.expire && getVipExpireTag()) {
          setVipExpireTag()
          showVipExpire({
            closeEvent: () => {
              store.dispatch('getCarplayInfo')
            },
            onCancel: () => {
              store.dispatch('getCarplayInfo')
            },
          })
          sendLog({
            event_type: 'show',
            event_name: 1752,
          })
        }

        // 登录时更新常唱数据
        store.dispatch('oftenSing/initOftenSingList', userInfo.value.unionid)
        TSBaseInfoInstance.notifyLoginInfoStatus({
          unionid: userInfo.value.unionid
        })
      }

      // 检测到已登录时正常开启活动领取状态检测
      setTimeout(() => {
        checkIsWatchLoginSendVip.value = val;
      }, 500);
    });

    // 已登录用户初始uid赋值
    watch(
      userInfo,
      (val) => {
        if (val.unionid) {
          setUid(val.unionid);
        }
      },
      {
        deep: true,
        immediate: true,
      }
    );

   const handleNatsConfigChange = async (val) => {
      if (val) {
        const { eip } = val;
        if (eip && macAddress.value) {
          natsInstance.value = await initNatsClient({ ...val, mac: macAddress.value }, handleReceiveMessageFromNats);
          console.log('handleNatsConfigChange', natsInstance.value)
        }
      }
    };

    // 监听natsConfig的变化
    watch(
      natsConfig,
      handleNatsConfigChange,
      {
        deep: true,
        immediate: true,
      }
    );

    // 监听net_status的变化
    watch(
      net_status,
      async (val) => {
        loading.value = false
        if (val) {
          handleNatsConfigChange(natsConfig.value);

          try {
            if (!videoPlayer.value.songItemLrc?.length && videoPlayer.value.songItem.lrc != 0) {
              await addCurrSongM3u8(videoPlayer.value.songItem);
            }
          } catch (error) {
            console.log('app.vue net_status',error);
          }
        }
      },
      {
        deep: true,
      }
    );

    // 开屏麦克风商城弹窗逻辑
    const openMicMallModal = (activityShowTag) => {
      // 每天第一次打开弹 一天只弹一次 云控执行活动规则
      const isShow = getMicActivityShowTag();
      if (isShow) {
        console.log('首次启动~')

        // // 开屏签到弹框
        // if (isShowSignIn.value) {
        //   // showActivitySignIn()
        //   sendLog({
        //     event_type: 'show',
        //     event_name: 1728,
        //     event_data: {
        //       str1: '弹窗展示次数'
        //     }
        //   })
        // }
      }
      else {
        const isShowVipActivityModal = getVipActivityShowTag()
        if (isShowVipActivityModal && activityShowTag) {
          showActivityVip();
          sendLog({
            event_type: 'show',
            event_name: 1723,
            event_data: {
              start_time: format(appStartTime, formatStr),
              end_time: format(appStartTime, formatStr),
              stay_time: 0,
              key_words: '销售VIP',
              type: 3,
            },
          });
        }
      }
    };

    // 设置活动标记 超过三次不显示领取弹框
    const setActivityTag = () => {
      // 初始未登录并默认打开了活动弹框时 关闭弹框后取消检测失败的场景 避免错误场景提示
      checkIsWatchLoginSendVip.value = false;
      const currActTag = getLoginSendVipActivityTag();
      if (currActTag < 3) setLoginSendVipActivityTag(currActTag + 1);
    };

    // 检测车机是否已参与领取会员活动
    watch(loginSendVip, (val, old) => {
      // 会员领取关闭应用前只主动弹一次
      if (val === 1 && old === undefined) {
        if (getLoginSendVipActivityTag() > 2) {
          return;
        }
        checkIsWatchLoginSendVip.value = true; // 打开弹框 检测活动领取状态
        showActivityModal({
          closeEvent: setActivityTag,
        });
      }

      // 检测到领取成功
      if (val === 4 && old === 1) {
        Toast({
          message: `会员领取成功！有效期至${
            vipInfo.value?.end_time.split(' ')[0]
          }`,
          duration: 5000,
        });
      }

      if (!checkIsWatchLoginSendVip.value) return;

      // 检测到账号或者车机已领取过
      if ([2, 3].includes(val) && old === 1) {
        Toast({
          message: '对不起，您的账号已经领取过了，不可以再领取了！',
          duration: 5000,
        });
      }
    });

    // 检测车机是否是持麦账号 没有则显示开屏麦克风活动弹窗
    // watch(hasMic, (val) => {
    //   // console.log('hasMic', val)
    //   openMicMallModal(val)
    // })

    // 开屏弹窗活动云控
    watch(openscreen_vip, (val) => {
      console.log('openscreen_vip', val);
      openMicMallModal(val === 0 ? true : !!val);
    });

    watch(mvIsHide, (v) => {
      console.log('watch: mvIsHide changed to', v); // 新增的console信息

      // 进入mv全屏页面
      if (!v) {
        sendLog({
          event_type: 'show',
          event_name: 6007,
          event_data: {
            str1: '欢唱页',
            str2: '欢唱页',
            str3: '进入欢唱页',
            str4: 'show',
          },
        });
        console.log('watch: mvIsHide is false, initializing auto play if needed'); // 新增的console信息
        if (!store.state.videoInitAutoPlay) {
          store.commit('UPDATE_MV_INIT_AUTOPLAY', 1);
          console.log('watch: Updated MV_INIT_AUTOPLAY to 1'); // 新增的console信息
        }
        console.log('watch: Notifying web page change from mv to', route.name); // 新增的console信息
        TSWebEventInstance.onWebPageChanged('mv', route.name)
        store.commit('miniMv/SET_DEFAULT_SONG', {
          song: store.state.miniMv.defaultList[0],
          index: 0,
        });
      } else {
        // 退出mv全屏页面
        console.log('watch: mvIsHide is true, delaying web page change notification'); // 新增的console信息
        console.log('watch: Notifying web page change from', route.name, 'to mv'); // 新增的console信息
        TSWebEventInstance.onWebPageChanged(route.name, 'mv')
      }

      console.log('watch: Hiding loading'); // 新增的console信息
      hideLoading();
    });

    watch(icon, (val) => {
      const icons = get(val, 'schedule', [])
      console.log('ICON DATA:', icons)
      let logoData = icons.length ? icons[0] : { id: 0 }
      if (icons.length) {
        const currentDate = new Date()
        icons.find(v => {
          if (v.id == 0) return false
          if (isWithinInterval(currentDate, { start: parseISO(v.start, 'yyyyMMdd'), end: addDays(parseISO(v.end, 'yyyyMMdd'), 1)})) {
            logoData = v
            return true
          }
            return false

        })
      }
      setTimeout(() => {
        TSBaseInfoInstance.notifyAppLogo(logoData)
        TSBaseInfoInstance.notifyAppLauncherImg(launch_img.value)
      }, 3000)
    }, {
      deep: true
    })

    watch(isVip, async (val) => {
      const isPlaying = TSMediaInstance.getIsPlaying()
      const songItem = videoPlayer.value.songItem
      if (!val && isPlaying && songItem.is_vip) {
        const isLocal = await store.dispatch('download/getIsLocalSong', songItem)
        if (!isLocal) {
          console.log('watch isVip', '视频停止播放')
          eventBus.emit('video-control-stop')
          store.commit('UPDATE_MV_INIT_AUTOPLAY', 0);
          store.dispatch('download/stopDownload')
        }
      }
    })

    watch(needLoading, async (val) => {
      if (val) {
        await nextTick();
        const mvSide = document.querySelector('.mv-side');
        const mvUnlogin = document.querySelector('.mv-user-vip-unlogin')
        if (mvUnlogin) {
          return
        }
        if (!mvIsHide.value && mvSide) {
          const pos = mvSide.dataset.pos;
          if (pos === '0') return
          showLoading(pos === '1' ? 'right' : 'left');
        } else {
          showLoading();
        }
      } else {
        hideLoading();
      }
    });

    onBeforeMount(initApp);

    // 强制登录
    const forceLoginLogic = async () => {
      await store.dispatch('getCarplayInfo')
      setTimeout(() => {
        showForceLogin()
      }, 600)
    }

    const openLimitModal = (params) => {
      isShowLimitModal.value = true
      limitParams.value = params
    }

    const handleExit = () => {
      TSNativeInstance.exit()
    }

    const onMicphoneNotification = (type, action) => {
      console.log('onMicphoneNotification', type, action)
      TSMicrophoneInstance.handleMicphoneNotification({ type, action })
    }

    const onMicphoneDongleEvent = (type, arg1, arg2) => {
      console.log('onMicphoneDongleEvent', type, arg1, arg2)
      TSMicrophoneInstance.handleMicphoneDongleEvent({ type, arg1, arg2 })
    }

    const onAudioEffectParams = async (type,json) => {
      console.log('onAudioEffectParams', json)
      TSMicrophoneInstance.handleAudioEffectParams(type,json)
    }

    const onDongleInfoChanged = (type) => {
      store.commit('UPDATE_USE_EFFECT', type)
    }
    
    const onHandleAudioEffectSuccess = (result, effect) => {
      TSMicrophoneInstance.handleAudioEffectSuccess(result, effect)
    }

    // 生成模式切换提示消息
    const generateStatusMessage = (isSafe) => 
      isSafe 
        ? '为了您的驾驶安全，自动为您切换为歌词模式'
        : '为了您拥有更好的K歌体验，自动为您切换为MV模式'

    // 执行模式切换操作
    const performModeSwitch = async (isSafe) => {
      const newMode = isSafe ? '歌词' : 'mv'
      await store.dispatch('mvMode/saveMvMode', {
        ...setting.value,
        mode: newMode,
      })
      if (currentSize.value === 'full') {
        Toast(generateStatusMessage(isSafe))
      }
    }
    
    const onCarSpeed = (number) => {
      // if (currentSize.value !== 'full') {
      //   return
      // }
      console.log('onCarSpeed', parseFloat(number) > 12)
      performModeSwitch(parseFloat(number) > 12)
      // eventBus.emit('car-speed-change', number)
      store.dispatch('carMonitor/setCarSpeed', number)
    }

    // 1 P  2 R 3 N  4 D
    const onCarGear = (number) => {
      console.log('onCarGear', number)
      store.dispatch('carMonitor/setCarGear', number)
    }

    // 1 playpause  2 play 3 Pause 4 next 5 replay
    const onCarKey = (key) => {
      try {
        console.log('onCarKey', key)
        store.dispatch('carMonitor/setCarKey', key)

        if (mvIsHide.value) return
        if (['playPause', 'play', 'pause'].includes(key)) {
          eventBus.emit('show-plugins', true)
        }
      } catch (error) {
        console.log('onCarKey error', error)
      }
    }

    const onChangeRoute = () => {
      let name = 'home'
      // if (['home', 'search'].includes(val)) name = val
      router.push({
        name,
      })
    }

    const onMediaVolume = (val) => {
      if (!appStatus.value) {
        return;
      }
      if (!enableMedia) {
        return
      }
      store.commit('UPDATE_MV_VIDEO_MEDIA', val);
      console.log('onMediaVolume', val)
    }

    const onAppStatus = (status, extras)=>{
      console.log('onAppStatus', format(Date.now(), 'yyyy-MM-dd HH:mm:ss'), status, extras, window.innerWidth, window.innerHeight)
      store.commit('base/SET_APP_STATUS', !(status == 1 || status == 3))
      console.log('onAppStatus', status, extras, window.innerWidth, window.innerHeight)
      // onCheckExit(status)
      onCheckPlayStatus(status)
      onCheckInput(status)
    }

    // const onCheckExit = (status) => {
    //   exitWorker.postMessage({ status });
    // };
    
    const onCheckPlayStatus = async (status) => {
      if (status == 0 || status == 2) {
        if (exitTimer) {
          clearTimeout(exitTimer)
          exitTimer = null;
        }
        setTimeout(async () => {
          await nextTick()
          const isPlaying = TSMediaInstance.getIsPlaying()
          console.log(TSMediaInstance.getIsPlaying(), '0829')
          store.dispatch('setVideoPaused', !isPlaying)
        }, 1000)
      }
    }

    const onCheckInput = async (status) => {
      await nextTick()
      const inputElements = document.querySelectorAll('input')
      if (inputElements) {
        console.log('onAppStatus', inputElements)
        setTimeout(() => {
          inputElements.forEach(inputElement => {
            inputElement.blur()
          })
        }, 0)
      }
      store.commit('base/SET_APP_STATUS', !(status == 1 || status == 3))
      if (status === 1) {
        recoverMediaVolume()
      }
    }

    /**
     * 恢复系统音量
     * */
    const recoverMediaVolume = () => {
      if (enableMediaMemory && systemMediaVolume !== -1) {
        console.log('恢复系统音量: ' + systemMediaVolume)
        window.TSVehicle.setMediaVolume(systemMediaVolume)
        systemMediaVolume = -1
      }
    }

    /**
     * 获取系统音量
     * */
    const getSystemMediaVolume = () => {
      if (!TSVehicleInstance) {
        return
      }
      if (enableMediaMemory) {
        if (systemMediaVolume === -1) {
          systemMediaVolume = TSVehicleInstance.getMediaVolume();
          console.log('当前系统音量: ' + systemMediaVolume)

          // TSVehicleInstance.setMediaVolume(10);
        }
      } else {
        onMediaVolume(TSVehicleInstance.getMediaVolume())
      }
    }

    /**
     * 播放时设置记忆的音量
     * */
    watch(videoPaused, (val) => {
      if (!val) {
        if (enableMediaMemory){
          getSystemMediaVolume()
          const storageMediaVolume = mediaVolume.value ? mediaVolume.value : store2.get('storageMediaVolume')
          if (typeof storageMediaVolume === 'number' && TSVehicleInstance && storageMediaVolume != TSVehicleInstance.getMediaVolume()) {
            store.commit('UPDATE_MV_VIDEO_MEDIA', storageMediaVolume);
            TSVehicleInstance.setMediaVolume(storageMediaVolume);
          }
        }
      }
    })
    // 监听屏幕变化 重新设置已点数据
    watch(currentSize, async(newSize) => {
      if (newSize) {
        let orderedList = await getOrderedList();
        console.log('orderedList', orderedList)
        store.commit('SAVE_ORDERED_LIST', orderedList);
      }
    }, {
      deep: true,
      immediate: true,
    });
    /**
     * 失去音频焦点，恢复系统音量
     * */
    const onAudioFocus = (value) => {
      console.log('onAudioFocus:' + value)
      const numericValue = Number(value);
      if (numericValue === -1) {
        recoverMediaVolume()
      }
    }

    // 公版默认配置 {}
    const onDefaultCarModels = () => {
      let m = {
      }
      return JSON.stringify(m)
    }

    const switchTheme = (theme) => {
      console.log('切换主题', theme)
      const isDarkTheme = theme.includes('Dark');
      document.body.classList.toggle('dark-theme', isDarkTheme);
      document.body.classList.toggle('light-theme', !isDarkTheme);
    }

    const onUiModeChanged = async (type) => {
      const theme = type === 1 ? 'themeDark' : 'themeLight';
      await store.commit('SET_THEME', theme);
      await nextTick();

      switchTheme(theme)
    };

    const onIsScreen = () => {
      // console.log('TS - handleDisplaysVisibilityChanged:')
      // console.log('TS - handleDisplaysVisibilityChanged:', JSON.parse(val))
      // store.dispatch('tsScreen/updateScreenInfo', JSON.parse(val))
    }

    const onNetworkChanged = (val) => {
      if (val == 1) {
        // 有网络
        store.dispatch('download/checkAutoDownload', !!vipInfo.value.end_time)
      } else {
        // 无网络
        store.dispatch('download/stopDownload')
      }
    }

    // local server change - 预留
    const onCommServerFound = (ip, port) => {
      console.log(ip, port)
    }

    const onSystemKeyWords = async (data) => {
      console.log('onSystemKeyWords', data);
      try {
        const info = JSON.parse(data);
        const params = {
          vin: info.vin || '',
          mac_id: info.mac_id || '',
          androidid: info.androidid || '',
          sys_func: info.sys_func || '',
          car_sdk: info.car_sdk || '',
          car_name: info.car_name || '',
        };

        let bol = true;

        // 当mac_id为020000000000时，不调用checkConfig方法，并且bol为false
        if (params.mac_id === '020000000000') {
          bol = false;
        } else {
          const res = await checkConfig(params);
          if (res && res.data && res.data.id) {
            if (res.data.id.charAt(6) === '1') {
              bol = false;
            }
          } else {
            bol = false;
          }
        }

        if (bol) {
          openLimitModal(params);
        }
      } catch (error) {
        console.log('onSystemKeyWords error', error);
      }
    };

    const onShowToast = (val) => {
      val && Toast(val);
    }

    const onSystemMute = (val) => {
      console.log('onSystemMute', val, val == 1)
      store.commit('UPDATE_SYSTEM_MUTED', val == 1)
    }

    const onDeleteFile = (songId) => {
      store.commit('UPDATE_SONG_DOWNLOAD_STATE', {
        songid: songId,
        downloadProgress: 0,
        downloadState: DownloadStatus.PROCESSING,
        src: '',
        isDeleted: true,
      });
    }

    const fetchCarplayInfo = () => {
      store.dispatch('getCarplayInfo');
    }

    const onBackPressed = () => {
      try {
        console.log('onBackPressed', 'mvIsHide.value:', mvIsHide.value);
        const controlPopup = document.querySelector('.control-popup-plugin')
        const thunderModal = document.querySelector('.thunder-modal-container')
        const dialog = document.querySelector('.van-dialog')
        const mvVue = document.querySelector('#mvVue')
        const isMvHidden = mvVue ? mvVue.style.display === 'none' : true
        console.log('onBackPressed', 'isMvHidden:', isMvHidden, 'mvIsHide:', mvIsHide.value, 'route:', route.name, 'controlPopup:', !!controlPopup, 'thunderModal:', !!thunderModal, 'dialog:', !!dialog);
        
        if (dialog && dialog.style.display !== 'none'){
          Dialog.close()
          console.log('onBackPressed dialog');
          return true
        }

        if (controlPopup && controlPopup.style.display !== 'none') {
          eventBus.emit('close-order-song-control-popup')
          console.log('close-order-song-control-popup');
          return true
        }

        if (thunderModal && thunderModal.style.display !== 'none') {
          eventBus.emit('close-thunder-modal')
          console.log('close-thunder-modal');
          return true
        }

        if (!isMvHidden || !mvIsHide.value) {
          store.commit('UPDATE_MV_ISHIDE', true)
          return true
        }
        console.log('onBackPressed back before');
        if (route.name === 'home' && isMvHidden && currentSize.value === 'full') {
          // Dialog.confirm({
          //   title: '确定退出应用吗？',
          //   confirmButtonText: '退出应用',
          //   cancelButtonText: '取消',
          //   onConfirm: () => {
          //     TSNativeInstance.exit()
          //   }
          // });
          TSNativeInstance.finish()
          return true
        }
        console.log('onBackPressed back after');
        router.back()
        store.commit('base/SET_IS_ROUTER_BACK', true)
        return true
      } catch (error) {
        console.log('onBackPressed error', error);
      }
    }

    // let mTimeCount = 0;
    // let nextTickCheck = 0;

    // const resetTimeCount = () => {
    //   mTimeCount = 0;
    // };

    // const handleNextTickError = () => {
    //   if (appStatus.value) {
    //     if (!mvIsHide.value) {
    //       TSWebEventInstance.onWebPageChanged(route.name, 'mv');
    //     }
    //     if (net_status.value) {
    //       if (document.querySelector('.control-popup-plugin')) {
    //         localStorage.setItem('webvieActiveYidian', true)
    //       }
    //       window.location.reload();
    //     } else {
    //       TSNativeInstance.showDialog('当前无网络连接，请连接网络后重试')
    //     }
    //   } else {
    //     TSNativeInstance.exit();
    //   }
    // };
    let exitTimer = null

    const attachEvents = () => {
      eventBus.on('getCarplayInfo', fetchCarplayInfo)

      TSWebEventInstance.on('handleMicphoneNotification', onMicphoneNotification)
      TSWebEventInstance.on('handleMicphoneDongleEvent', onMicphoneDongleEvent)
      TSWebEventInstance.on('handleAudioEffectParams', onAudioEffectParams)
      TSWebEventInstance.on('handleSpeed', onCarSpeed)
      TSWebEventInstance.on('handleGear', onCarGear)
      TSWebEventInstance.on('handleKey', onCarKey)
      TSWebEventInstance.on('handleChangeRoute', onChangeRoute)
      TSWebEventInstance.on('handleMediaVolume', onMediaVolume)
      TSWebEventInstance.on('handleDisplaysVisibilityChanged', onIsScreen)
      TSWebEventInstance.on('handleCommServerFound', onCommServerFound)
      TSWebEventInstance.on('handleDefaultCarModels', onDefaultCarModels)
      TSWebEventInstance.on('handleNetworkChanged', onNetworkChanged)
      TSWebEventInstance.on('handleSystemKeyWords', onSystemKeyWords)
      TSWebEventInstance.on('handleUiModeChanged', onUiModeChanged)
      TSWebEventInstance.on('handleShowToast', onShowToast)
      TSWebEventInstance.on('handleSystemMute', onSystemMute) 
      TSWebEventInstance.on('handleAppStatus',onAppStatus)
      TSWebEventInstance.on('handleDeleteFile',onDeleteFile) // 超过4G自动触发删除
      TSWebEventInstance.on('handleBackPressed', onBackPressed);
      TSWebEventInstance.on('handleWebViewActive', () => {});
      TSWebEventInstance.on('handleAudioFocus', onAudioFocus)
      TSWebEventInstance.on('handleVoice', () => {});
      TSWebEventInstance.on('handleDongleInfoChanged', onDongleInfoChanged)
      TSWebEventInstance.on('handleAudioEffectSuccess', onHandleAudioEffectSuccess)

      // 全局异常捕获器
      window.onerror = function(message, source, lineno, colno, error) {
        console.error('javaScript onerror: ' + message + ' at ' + source + ':' + lineno + ':' + colno + ':' + error);
        return true;
      };

      // 捕获全局未处理的 Promise 拒绝
      window.addEventListener('unhandledrejection', function (event) {
        console.error('javaScript onerror promise rejection:', event.reason);
        if (event.reason.type === 'timeout') {
          console.log('Unhandled promise rejection event: timeout');
          Dialog({
            title: '提示',
            className: 'net-error-dialog',
            confirmButtonText: '我知道了',
            showCancelButton: false,
            message: '当前网络状态不佳，请检查网络', // 歌曲下载出现了点问题，请重新加载
            zIndex: 10000,
            transition: false // 禁用动画效果
          })
          .then(() => {
            console.log('Dialog confirmed');
            store.commit('base/SET_NET_LOADING', false);
          });
          if (event.reason.name === 'ChunkLoadError') {
            console.log('Unhandled promise rejection event: ChunkLoadError');
            console.log('ChunkLoadError detected, replacing route');
            router.replace(router.history?.pending?.fullPath);
          }
        }
        event.preventDefault();
      });

      window.addEventListener('error', (event) => {
        console.error('javaScript error captured:', event.message);
      });
    }

    const preloadLottieAnimation = (animationData) => {
      const container = document.createElement('div');
      container.style.display = 'none';
      document.body.appendChild(container);

      lottie.loadAnimation({
        container,
        animationData,
        renderer: 'svg',
        loop: false,
        autoplay: false,
      });
    };

    onMounted(() => {
      const _vc = TSNativeInstance.getParams('_vc') || 0
      console.log('app_vc=', _vc)
      if (_vc > 100023 && isYSTipAccept.value) {
        showLoading()
      }
      preloadLottieAnimation(booing)
      preloadLottieAnimation(cheer)
      preloadLottieAnimation(hands)

      store.dispatch('base/fetchMallInfo')
      forceLoginLogic()
      let timeout = 2000;

      setTimeout(async () => {
        hideLoading()
        store.dispatch('updateMicrophones', { reset: true })
        store.dispatch('storage/getSpaceInfo')
        //初始化挂载本地历史已点已唱
        let orderedList = await getOrderedList();
        let alreadyList = await getAlreadyList();
        let searchCache = getSearchCache();
        let searchSong = getSearchSong();

        // eventBus.emit('init-orderedList', !orderedList.length)
        store.commit('SAVE_INIT_ORDERED');
        store.commit('SET_VIDEO_MUTE', !orderedList.length)

        // // 设置伴奏音量
        // TSMediaInstance.setStreamVolume(videoVolume.value);

        TSMediaInstance.setMute(!orderedList.length)

        if (orderedList.length) {
          store.commit('SAVE_ORDERED_LIST', orderedList);
          // 此处只挂载歌曲id 歌曲不在此处挂载 车机上的浏览器和实际浏览器在- mv自动播放表现会不一致
          store.commit('SHIFT_SONG_ORDERED_LIST');
          store.dispatch('download/checkAutoDownload', !!vipInfo.value.end_time)

          // store2.remove('orderedData')

          const isLocalSong = await getIsLocalSong(orderedList[0])
          console.log('1230', orderedList[0], isLocalSong)

          await new Promise(resolve => setTimeout(resolve, 1000));
          eventBus.emit('video-control-resume')
        }
        if (alreadyList.length) {
          store.commit('SAVE_ALREADY_LIST', alreadyList.filter(item => !!item?.songid));

          store2.remove('alreadyData')
        }
        if (searchCache.length) {
          store.dispatch('search/updateSearchCache', searchCache)
        }
        if (searchSong.length) {
          store.dispatch('search/initSearchSong', searchSong)
        }

        if (store.state.enableMoreEffect) {
          store.commit('UPDATE_EFFECT_LIST_DATA', TSMicrophoneInstance.getEffectList())
          store.commit('UPDATE_USE_EFFECT', TSMicrophoneInstance.getCurEffectType())
        }
        
      }, timeout);

      switchTheme(themeClass.value)

      if (localStorage.getItem('webvieActiveYidian')) {
        setTimeout(() => {
          eventBus.emit('show-order-song-control-popup');
        }, 3000)
        localStorage.setItem('webvieActiveYidian', '')
      }
    });

    // 监听 orderedList 第一个元素的 songid 变化
    watch(
      () => orderedList.value[0]?.songid,
      async (newSongId, oldSongId) => {
        if (newSongId !== oldSongId) {
          await store.commit('RESET_CURR_SONG_LRC')
          // 如果已点列表第一个的 songid 有更新，才执行 addCurrSongM3u8
          addCurrSongM3u8();
          store.commit('SAVE_VIDEO_PLAYER_HISTORY_SONG_ITEM', orderedList.value[0])
          eventBus.emit('chang-playsong')
        }
          
        TSMediaInstance.setMute(false)
      }
    );

    watch(themeClass, () => {
      switchTheme(themeClass.value)
    })

    watch(appStatus, (val) => {
      console.log(`App status changed to: ${val}`);
      if (val && natsConfig.value && natsConfig.value.eip && macAddress.value) {
        console.log('Initializing NATS client...');
        natsInstance.value = initNatsClient({ ...natsConfig.value, mac: macAddress.value }, handleReceiveMessageFromNats);
      } else {
        // console.log('Closing NATS client...');
        // natsInstance.value?.close();
      }
    })

    const onVisibilitychange = async () => {
      if (!document.hidden) {
        getCarplayInfo()
        getSystemMediaVolume()
      } else {
        const inputElement = document.querySelector('input')
        if (inputElement) {
          inputElement.blur()
        }
      }
    }

    const onRefresh = async () => {
      try {
        if (!mvIsHide.value) {
          loading.value = false
          return
        }
        await router.replace({ path: route.path, query: { ...route.query, t: Date.now() } });
        loading.value = false
      } catch {
        loading.value = false
      }
    }

    onMounted(() => {
      window.addEventListener('beforeunload', onUnload);
      window.addEventListener('online', onNetChange)
      window.addEventListener('offline', onNetChange);
      installGlobalClickHandler()
      document.addEventListener('visibilitychange', onVisibilitychange)
      attachEvents()
      getSystemMediaVolume()

      window.addEventListener('resize', updateSize);
      updateSize()

      cacheImagesAsBase64(cacheImages)
    });

    onBeforeUnmount(() => {
      window.removeEventListener('beforeunload', onUnload);
      window.removeEventListener('online', onNetChange)
      window.removeEventListener('offline', onNetChange);
      uninstallGlobalClickHandler()
      document.removeEventListener('visibilitychange', onVisibilitychange)
      eventBus.off('getCarplayInfo', fetchCarplayInfo)
      window.removeEventListener('resize', updateSize);
    });

    const updateSize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      let size = 'full';
      if (width <= 700) {
        size = 'small';
      } else if (width <= 900) {
        size = 'medium';
      }
      console.log('当前窗口尺寸:', `宽 ${width}px × 高 ${height}px`, '分类:', size)
      store.dispatch('updateCurrentSize', size);
      document.body.className = document.body.className.replace(/\b(SCREEN_full|SCREEN_small|SCREEN_medium)\b/g, '');
      document.body.classList.add('SCREEN_' + size);
      // setScaleVariable()
    };

    return {
      mvIsHide,
      isShowLimitModal,
      handleExit,
      themeClass,
      limitParams,
      net_status,
      onRefresh,
      loading,
      routeName,
      isYSTipAccept,
      currentSize,
    };
  },
};
</script>

<style lang="stylus">
.app-main
  position relative
  &.is-bg-transparent
    background none
.vld-background
  display none
[aria-label="loading"]
  touch-action none
.lottie {
  width: 100vw;
  position: fixed;
  height: 100vh;
  z-index: -1;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
#lottieShowDiv {
  width: 600px;
  height: 600px;
}
// 新增动画样式
.mv-enter-active, .mv-leave-active {
  transition: transform 0.5s ease;
}
.mv-enter {
  transform: translateY(-100%);
}
.mv-leave-to {
  transform: translateY(100%);
}
</style>