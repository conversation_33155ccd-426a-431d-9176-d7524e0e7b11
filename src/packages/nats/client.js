import { connect, StringCodec } from 'nats.ws/lib/src/mod';
import eventBus from '@/utils/event-bus';

export class NatsClient {
  nc = null;
  sub = null;
  sc = StringCodec();

  options = {
    servers: '',
    pingInterval: 15 * 1000,
    reconnectTimeWait: 3 * 1000,
    subs: '',
    token: '',
    messageHander: () => {},
  };

  constructor(options) {
    if (typeof queueMicrotask === 'undefined') {
      window.queueMicrotask = (callback) =>
        Promise.resolve().then(callback).catch((err) => setTimeout(() => { throw err; }));
    }

    this.nc = null;
    this.options = {
      ...this.options,
      ...options,
    };
  }

  async connect() {
    try {
      this.nc = await connect({
        servers: this.options.servers,
        token: this.options.token,
        pingInterval: this.options.pingInterval,
        reconnectTimeWait: this.options.reconnectTimeWait,
      });

      console.log('Connected to NATS server:', this.options.servers);

      // 访问 WebSocket 实例并添加错误处理
      const ws = this.nc.protocol.transport.socket;
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

      ws.onclose = async (event) => {
        console.warn('WebSocket closed:', event);
        if (event.code === 1006) {
          console.error('Connection closed unexpectedly. Possible reason: Received a broken close frame containing a reserved status code.');
          // 这里可以处理连接意外关闭的情况
        } else {
          console.log('Connection closed with code:', event.code);
        }
        // 尝试重连
        this.reconnect();
      };

      this.subscribe();
      eventBus.emit('getCarplayInfo');
      
      this.nc.closed().then(() => {
        console.log('NATS connection closed');
      }).catch((error) => {
        console.error('Error closing NATS connection:', error);
      });
    } catch (error) {
      console.error('Connection error:', error);
      setTimeout(() => {
        console.log('Reconnecting...', error);
        this.connect();
      }, this.options.reconnectTimeWait);
    }
  }

  async reconnect() {
    console.log('Attempting to reconnect to NATS server...');
    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('Reconnection failed:', error);
        await this.reconnect(); // 继续尝试重连
      }
    }, this.options.reconnectTimeWait);
  }

  async request() {
    if (this.nc) {
      await this.nc.request('time', this.sc.encode(''), { timeout: 1000 })
        .then((m) => {
          console.log(`got response: ${this.sc.decode(m.data)}`);
        })
        .catch((err) => {
          console.log(`problem with request: ${err.message}`);
        });
    }
  }

  async subscribeAPI() {
    if (this.nc) {
      const sub = this.nc.subscribe('list');
      const subj = sub.getSubject();
      console.log(`listening for ${subj}`);
      for await (const m of sub) {
        const subject = m.subject;
        const data = m.data ? this.sc.decode(m.data) : '';
        console.log('nats', 'request', subject, data);
      }
    }
  }

  async subscribe() {
    if (this.nc) {
      try {
        this.sub = this.nc.subscribe(this.options.subs);
      } catch (error) {
        console.error('订阅过程中发生错误:', error);
      }
      const subj = this.sub.getSubject();
      console.log(`listening for ${subj}`);
      for await (const m of this.sub) {
        const subject = m.subject;
        const data = m.data ? this.sc.decode(m.data) : '';
        console.log('nats', 'request', subject, data);
        const resBody = this.options.messageHander(data);
        console.log('nats', 'response', subject, resBody);
        m.respond(this.sc.encode(JSON.stringify({ result: JSON.stringify(resBody), err: '' })));
      }
    }
  }

  async close() {
    if (this.nc) {
      try {
        const done = this.nc.closed();
        await this.nc.close();
        const err = await done;
        if (err) {
          console.log('nats error closing:', err);
          return;
        }
        console.log('nats client', 'close');
      } catch (error) {
        console.error('Error during closing NATS client:', error);
      }
    }
  }
}
