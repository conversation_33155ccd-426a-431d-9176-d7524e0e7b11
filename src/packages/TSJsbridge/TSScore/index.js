import store2 from 'store2'

const noopMethodList = [
  'start',
  'stop',
  'getScoreLevel',
  'setScoreLevel',
];

export class TSScoreBridge {
  constructor() {
    if (!window.TSScore) {
      console.error('TSScore interface not found!');
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSScore fun:', method);
          console.log('call payload', JSON.stringify(payload));
        };
      });
    } else {
      console.log('TSScore', 'interface', 'installed');
      TSScoreBridge.init();
    }
  }

  static init() {
    // 执行初始化逻辑
  }

  start(payload) {
    try {
      console.log('开启打分', payload);
      window.TSScore?.startScore(JSON.stringify(payload))
      let results = store2.get('score-results') || []
      results.unshift(payload)
      store2.set('score-results', results)
    } catch (error) {
      console.error('开启打分出错', error);
    }
  }

  stop() {
    try {
      console.log('关闭打分');
      window.TSScore?.stopScore();
    } catch (error) {
      console.error('关闭打分出错', error);
    }
  }

  getScoreLevel() {
    try {
      console.log('获取当前的打分评级');
      return window.TSScore?.getScoreLevel();
    } catch (error) {
      console.error('获取当前的打分评级出错', error);
      return null;
    }
  }

  setScoreLevel(level) {
    try {
      console.log('设置当前的打分评级');
      window.TSScore?.setScoreLevel(level);
    } catch (error) {
      console.error('设置当前的打分评级出错', error);
    }
  }
}

export const TSScoreInstance = new TSScoreBridge();
