import Toast from '@/utils/toast'

export class TSVoiceBridge {
  constructor() {
    if (!window.TSVoice) {
      console.log('TSVoice interface not found!')
      Toast({
        message: 'TSVoice interface not found!',
        className: 'not-support',
        position: 'top',
      })
      window.tsVoice = this
    } else {
      console.log('TSVoice', 'interface', 'installed')
    }
  }

  sendTtsMessage(status, code, data) {
    try {
      console.log('Sending TTS message with status:', status, 'code:', code, 'data:', data)
      window.TSVoice.sendTtsMessage(status, code, data)
    } catch (error) {
      console.log('sendTtsMessage error', error)
    }
  }
}

export const TSVoiceInstance = new TSVoiceBridge()