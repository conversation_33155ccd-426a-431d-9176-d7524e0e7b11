import Toast from '@/utils/toast'
import store2 from 'store2'

const noopMethodList = [
  'getUniqueId',
  'getMediaMaxVolume',
  'getMediaVolume',
  'setMediaVolume'
]

class TSVehicleBridge {
  maxMediaVolume = 0

  constructor() {
    if (!window.TSVehicle) {
      console.log('TSVehicle interface not found!')
      Toast({
        message: 'TSVehicle interface not found!',
        className: 'not-support',
        position: 'top'
      })
      noopMethodList.forEach((method) => (this[method] = () => {}))
      window.TSVehicle = this
    } else {
      console.log('TSVehicle interface installed')
      this.init()
    }
  }

  init() {
    this.maxMediaVolume = this.getMediaMaxVolume()
    const mediaVolume = this.getMediaVolume()
    console.log('Media volume:', this.maxMediaVolume, mediaVolume)

  }

  getUniqueId() {
    try {
      return window.TSVehicle.getUniqueId()
    } catch (error) {
      console.log('Error in getUniqueId:', error)
      return null
    }
  }

  getMediaMaxVolume() {
    try {
      const res = window.TSVehicle.getMediaMaxVolume()
      if (typeof res === 'number' && !isNaN(res)) {
        store2.set('maxMediaVolume', res)
        console.log('getMediaMaxVolume', res)
        return res
      } 
      console.log('Invalid response in getMediaMaxVolume:', res)
      return 20
    } catch (error) {
      console.log('Error in getMediaMaxVolume:', error)
      return 20
    }
  }

  getMediaVolume() {
    try {
      const res = window.TSVehicle.getMediaVolume()
      if (typeof res === 'number' && !isNaN(res)) {
        console.log('getMediaVolume', res)
        return res
      } 
      console.log('Invalid response in getMediaVolume:', res)
      return 0
    } catch (error) {
      console.log('Error in getMediaVolume:', error)
      return 0
    }
  }

  setMediaVolume(volume) {
    try {
      window.TSVehicle.setMediaVolume(volume)
      console.log('setMediaVolume', volume)
      store2('storageMediaVolume', volume)
    } catch (error) {
      console.log('setMediaVolume error', error)
    }
  }

  getCarName() {
    try {
      return window.TSVehicle.getVehicleModel()
    } catch (error) {
      console.log('Error in getCarName:', error)
      return null
    }
  }
}

export const TSVehicleInstance = new TSVehicleBridge()