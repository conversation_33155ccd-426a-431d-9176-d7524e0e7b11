import Toast from '@/utils/toast'

const noopMethodList = [
  'getScreens',
  'startScreen',
  'stopScreen',
  'getScreenStatus',
  'getScreens',
  'checkIsExistDisplayId',
  'getCurrScreenId',
  'getScreenWSUrl',
]

export class TSScreenBridge {
  screenId;

  constructor() {
    this.screenId = '';
    if (!window.TSDisplay) {
      console.log('TSDisplay interface not found!')
      Toast({
        message: 'TSDisplay interface not found!',
        className: 'not-support',
        position: 'top',
      })
      noopMethodList.forEach(method => {
        this[method] = () => { };
      });
      window.TSDisplay = this
    } else {
      console.log('TSDisplay', 'interface', 'installed')
      this.init()
    }
  }

  init() {
    console.log('TSDisplay init')
    this.getCurrScreenId()
  }

  startScreen() {
    try {
      let id = this.screenId || this.getCurrScreenId()
      if (id == 0) {
        this.getScreens().find(v => {
          if (v.displayId != 0 && !v.visible) {
            console.log('startScreen:', v.displayId)
            id = v.displayId
            window.TSDisplay.showPresentation(v.displayId)
            return true
          }
          return false
        })
      } else {
        const status = this.getScreenStatus(id)
        console.log('startScreen:', status)
        if (status) {
          Toast({
            message: `屏幕 ${id} 已在投屏中`,
            className: 'not-support',
            position: 'top',
          })
          return
        }
        window.TSDisplay.showPresentation(id)
      }
    } catch (error) {
      console.log('startScreen error', error)
    }
  }

  stopScreen() {
    try {
      let id = this.screenId || this.getCurrScreenId()
      if (id == 0) {
        this.getScreens().forEach(v => {
          if (v.displayId != 0) window.TSDisplay.dismissPresentation(v.displayId)
        })
        return
      }
      const status = this.getScreenStatus(id)
      console.log('stopScreen:', status)
      if (!status) {
        Toast({
          message: `当前屏幕${id}未存在投屏`,
          className: 'not-support',
          position: 'top',
        })
        return
      }
      window.TSDisplay.dismissPresentation(id)
    } catch (error) {
      console.log('stopScreen error', error)
    }
  }

  getScreenStatus(val) {
    try {
      const isExist = this.checkIsExistDisplayId(val)
      if (!isExist && isExist != 0) {
        Toast({
          message: `未检测到已开启该屏幕 ${val}`,
          className: 'not-support',
          position: 'top',
        })
        return false
      }

      // true 投屏中 false 未投屏
      console.log('+++ getScreenStatus +++')
      let status = window.TSDisplay.isVisible(val)
      console.log('window.TSDisplay.isVisible:', status)
      return status
    } catch (error) {
      console.log('getScreenStatus error', error)
      return false
    }
  }

  getScreens() {
    try {
      let screenArr = window.TSDisplay?.getDisplays() || []
      console.log('window.TSDisplay.getDisplays():', JSON.parse(screenArr))
      return JSON.parse(screenArr)
    } catch (error) {
      console.log('getScreens error', error)
      return []
    }
  }

  getCurrScreenId() {
    try {
      this.screenId = JSON.parse(window.TSDisplay?.getDisplayInfo()).displayId
      console.log('getCurrScreenId:', this.screenId)
      return this.screenId
    } catch (error) {
      console.log('getCurrScreenId error', error)
      return ''
    }
  }

  checkInitIsScreen() {
    try {
      const info = JSON.parse(window.TSDisplay?.getDisplayInfo())
      this.screenId = info.displayId
      let visible = info.visible
      console.log('checkInitIsScreen:', visible)
      return visible && this.screenId != 0
    } catch (error) {
      console.log('checkInitIsScreen error', error)
      return false
    }
  }

  checkIsExistDisplayId(val) {
    try {
      const isExistDisplayId = this.getScreens().find(v => v.displayId === val)
      console.log('isExistDisplayId:', val, isExistDisplayId)
      return isExistDisplayId
    } catch (error) {
      console.log('checkIsExistDisplayId error', error)
      return false
    }
  }

  getScreenWSUrl() {
    try {
      let str = `ws://${window.TSCommServer?.getServerIP()}:${window.TSCommServer?.getServerPort()}/group/carplayh5` || null
      console.log('getScreenWSUrl:', str)
      return str || ''
    } catch (error) {
      console.log('getScreenWSUrl error', error)
      return ''
    }
  }
}

export const TSScreenInstance = new TSScreenBridge()