const noopMethodList = [
  'startRecord',
  'stopRecord',
];

export class TSRecordBridge {
  constructor() {
    if (!window.TSRecord) {
      console.error('TSRecord interface not found!');
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSRecord fun:', method);
          console.log('call payload', JSON.stringify(payload));
        };
      });
    } else {
      console.log('TSRecord', 'interface', 'installed');
      this.init();
    }
  }

  init() {
  }

  startRecord() {
    try {
      // 执行开始录音的逻辑
      console.log('开始录音');
      window.TSRecord.startRecord();
    } catch (error) {
      console.error('开始录音出错', error);
    }
  }

  stopRecord() {
    try {
      // 执行关闭录音的逻辑
      console.log('关闭录音');
      window.TSRecord.stopRecord();
    } catch (error) {
      console.error('关闭录音出错', error);
    }
  }

}

export const TSRecordInstance = new TSRecordBridge();
