import { TSScreenInstance } from '@/packages/TSJsbridge/TSScreen'
import store from '@/store'
import { Toast } from 'vant'

const noopMethodList = [
  'notifyHomePageLoadCompleted',
  'notifyAppLogo',
  'notifyAppLauncherImg',
  'notifyLoginInfoStatus',
  'controlMVScaling',
  'getConfigurationUIMode',
  'playAtmosphereAudio',
  'setHomeVideoParams',
  'scollHomeVideo',
]

export class TSBaseInfoBridge {
  event = {}

  constructor() {
    if (!window.TSBaseInfo) {
      console.log('TSBaseInfo interface not found!')
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          // console.log('call TSBaseInfo fun:', method)
          // console.log('call payload', JSON.stringify(payload))
        };
      });
      window.tsTSBaseInfo = this
    } else {
      console.log('TSBaseInfo', 'interface', 'installed')
      this.init()
    }
  }

  init() {
    console.log('TSBaseInfo', 'init')
    this.getConfigurationUIMode()
  }

  on(name, handler) {
    window[name] = handler
  }

  notifyHomePageLoadCompleted() {
    try {
      console.log('notifyHomePageLoadCompleted')
      // Android接口重新释放
      window.TSBaseInfo.notifyHomePageLoadCompleted()
    } catch (error) {
      console.log('notifyHomePageLoadCompleted error', error)
    }
  }

  notifyAppLogo(data) {
    try {
      console.log('notifyAppLogo', data)
      window.TSBaseInfo.notifyAppLogo(JSON.stringify(data))
    } catch (error) {
      console.log('notifyAppLogo error', error)
    }
  }

  notifyAppLauncherImg(data) {
    try {
      console.log('notifyAppLauncherImg', data)
      window.TSBaseInfo.notifyAppLauncherImg(JSON.stringify(data))
    } catch (error) {
      console.log('notifyAppLauncherImg error', error)
    }
  }
  
  notifyLoginInfoStatus(payload) {
    try {
      console.log('notifyLoginInfoStatus')
      // Android接口重新释放
      window.TSBaseInfo.notifyLoginInfoStatus(JSON.stringify(payload))
    } catch (error) {
      console.log('notifyLoginInfoStatus error', error)
    }
  }

  controlMVScaling(number) {
    try {
      const screenid =  TSScreenInstance.getCurrScreenId()
      console.log(`controlMVScaling: number - ${number}, screenid - ${screenid}`)
      window.TSBaseInfo.controlMVScaling(screenid, number, true)
    } catch (error) {
      console.log('controlMVScaling error', error)
    }
  }
  
  goToExternalPage(str) {
    try {
      console.log('goToExternalPage:', str)
      if (window.TSBaseInfo) {
        window.TSBaseInfo.goToExternalPage(str)
      } else {
        window.location.href = str
      }
    } catch (error) {
      console.log('goToExternalPage error', error)
    }
  }
  
  getConfigurationUIMode() {
    try {
      const res = window.TSBaseInfo.getConfigurationUIMode()
      const theme = res == 1 ? 'themeDark' : 'themeLight'
      store.commit('SET_THEME', theme)
      console.log('getConfigurationUIMode', theme)
    } catch (error) {
      console.log('getConfigurationUIMode error', error)
      store.commit('SET_THEME', 'themeDark')
    }
  }

  requestAudioFocus() {
    try {
      let res = false
      if(window.TSAudioFocus){
        if(window.TSAudioFocus.hasAudioFocus()  || window.TSAudioFocus.requestAudioFocus()){
          res = true
        }
      }else{
        res = true
      }
      if (res) {
        return true
      } 

      Toast({
        message: '其他应用正在使用音频，暂时无法播放',
        position: 'center'
      });
      return false
    } catch (error) {
      console.log('requestAudioFocus error', error)
      return false
    }
  }

  playAtmosphereAudio(index) {//1-喝彩，2-嘘声，3-掌声
    try {
      console.log('playAtmosphereAudio' + index);
      window.TSBaseInfo.playAtmosphereAudio(index);
    } catch (error) {
      console.log(error)
    }
  }

  setHomeVideoParams({width, height, top, left}) {
    try {
      window.TSBaseInfo.setHomeVideoParams(width, height, top, left)
      console.log('setHomeVideoParams', width, height, top, left)
    } catch (error) {
      console.log('setHomeVideoParams error', error)
    }
  }
  scollHomeVideo(top) {
    try {
      window.TSBaseInfo.scollHomeVideo(top)
    } catch (error) {
      console.log('scollHomeVideo error', error)
    }
  }
}

export const TSBaseInfoInstance = new TSBaseInfoBridge()