export const MIN_USABLE_SPACE = 300

export const noopMethodList = [
  'setDownloadDir',
  'getDownloadDir',
]

export class TSConfigBridge {

  constructor() {
    if (!window.TSConfig) {
      console.log('TSConfig interface not found!')
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSConfig fun:', method)
          console.log('call payload', JSON.stringify(payload))
        };
      });
    } else {
      console.log('TSConfig', 'interface', 'installed')
      this.init()
    }
  }

  init() {
  }

  // 设置下载目录
  setDownloadDir(path) {
    try {
      const res = window.TSConfig.setDownloadDir(path)
      return res
    } catch (error) {
      console.log('setDownloadDir error', error)
    }
  }

  getDownloadDir() {
    try {
      const res = window.TSConfig.getDownloadDir()
      return res
    } catch (error) {
      console.log('getDownloadDir error', error)
    }
  }

  getDefaultVideo() {
    let res = null
    try {
      res = JSON.parse(window.TSConfig.getDefaultVideo())
    } catch (error) {
      console.log('getDefaultVideo error', error)
    }
    return res
  }
  
  setPreference(key, value) {
    if (typeof window.TSConfig.setPreference === 'function') {
      console.log('1122',  window.TSConfig.getPreference(key))
      window.TSConfig.setPreference(key, JSON.stringify(value));
    } else {
      localStorage.setItem(key, JSON.stringify(value));
    }
  }

  getPreference(key) {
    if (typeof window.TSConfig.getPreference === 'function') {
      console.log('1122',  window.TSConfig.getPreference(key))
      return JSON.parse(window.TSConfig.getPreference(key));
    }

    return JSON.parse(localStorage.getItem(key));
  }
}

export const TSConfigInstance = new TSConfigBridge()