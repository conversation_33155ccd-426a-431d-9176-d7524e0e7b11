import { TSConfigInstance } from '../TSConfig'
import { formatFileSize } from './utils'

export const MIN_USABLE_SPACE = 300

const noopMethodList = [
  'getStorageSize',
  'clearCache',
  'getCanonicalPath',
  'getIsExist',
  'getFileLength',
  'isDirectory',
  'isFile',
  'getLastModified',
  'createNewFile',
  'delete',
  'mkdir',
  'mkdirs',
  'renameTo',
  'getTotalSpace',
  'getFreeSpace',
  'getUsableSpace',
  'getFiles',
]

export class TSFileBridge {
  downloadDirPath = ''

  constructor() {
    if (!window.TSFile) {
      console.log('TSFile interface not found!')
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSFile fun:', method)
          console.log('call payload', JSON.stringify(payload))
        };
      });
      window.tsTSFile = this
    } else {
      console.log('TSFile', 'interface', 'installed')
      this.init()
    }
  }

  init() {
    console.log('TSFile', 'init')
    if (TSConfigInstance) {
      this.downloadDirPath = TSConfigInstance.getDownloadDir()
    }
  }

  on(name, handler) {
    window[name] = handler
  }
  
  getCacheSize() {
    try {
      const res = window.TSFile.getCacheSize()
      return res
    } catch (error) {
      console.log('getCacheSize', error)
    }
  }

  clearCache() {
    try {
      const res = window.TSFile.clearCache()
      return res
    } catch (error) {
      console.log('clearCache', error)
    }
  }
  
  getDataSize() {
    try {
      const res = window.TSFile.getDataSize()
      return res
    } catch (error) {
      console.log('getDataSize', error)
    }
  }

  clearData() {
    try {
      const res = window.TSFile.clearData()
      return res
    } catch (error) {
      console.log('clearData', error)
    }
  }
  // input:歌曲列表信息
  // output:歌曲列表信息{含下载状态}
  getCanonicalPath(filePath) {
    const res = window.TSFile.getCanonicalPath(filePath)
    return res
  }
  getIsExist(filePath) {
    const res = window.TSFile.exists(filePath)
    return res
  }
  getFileLength(filePath) {
    const res = window.TSFile.length(filePath)
    return res
  }
  isDirectory(filePath) {
    const res = window.TSFile.stopDownload(filePath)
    return res
  }
  isFile(filePath) {
    const res = window.TSFile.isFile(filePath)
    return res
  }
  getLastModified(filePath) {
    const res = window.TSFile.lastModified(filePath)
    return res
  }
  create(filePath) {
    const res = window.TSFile.create(filePath)
    return res
  }
  async delete(path) {
    const res = await window.TSFile.delete(path)
    console.log('TSFile', 'delete', res)
    return res
  }
  mkdir(filePath) {
    const res = window.TSFile.mkdir(filePath)
    return res;
  }
  mkdirs(filePath) {
    const res = window.TSFile.mkdirs(filePath)
    return res;
  }
  renameTo(filePath) {
    const res = window.TSFile.renameTo(filePath)
    return res
  }
  getTotalSpace(filePath) {
    const res = window.TSFile.getTotalSpace(filePath || this.downloadDirPath)
    return formatFileSize(res)
  }
  getFreeSpace(filePath) {
    const res = window.TSFile.getFreeSpace(filePath || this.downloadDirPath)
    return formatFileSize(res)
  }
  getUsableSpace(filePath) {
    const res = window.TSFile.getUsableSpace(filePath || this.downloadDirPath)
    return formatFileSize(res)
  }
  getFiles(filePath) {
    let res = []
    try {
      res = JSON.parse(window.TSFile.getFilesInfoOrderByLastModified(filePath || this.downloadDirPath))
    } catch (error) {
      console.log('getFilesInfoOrderByLastModified')
      console.log(error)
    }
    return res
  }
}

export const TSFileInstance = new TSFileBridge()
