import Toast from '@/utils/toast'
import store from '@/store'
import { DownloadStatus } from '@/utils/download'
import store2 from 'store2'

const noopMethodList = [
  'startPlay',
  'playOnline',
  'replay',
  'playNext',
  'pause',
  'resume',
  'getIsPlaying',
  'getSelectedTrack',
  'selectTrack',
  'getDuration',
  'getCurrentPosition',
  'getStreamMaxVolume',
  'getStreamVolume',
  'setStreamVolume',
  'stop',
  'updateToken',
  'setResolution',
  'playSmallVideo',
  'getIsPlayingFinish',
  'setMute',
  'getMute',
  'setSongLrc',
]

export class TSMediaBridge {
  song = null
  songJsonString = JSON.stringify({})
  maxStreamVolume = 0

  constructor() {
    if (!window.TSMedia) {
      console.log('TSMedia interface not found!')
      Toast({
        message: 'TSMedia interface not found!',
        className: 'not-support',
        position: 'top',
      })
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSMedia fun:', method)
          console.log('call payload', JSON.stringify(payload))
        };
      });
    } else {
      console.log('TSMedia', 'interface', 'installed')
      this.init()
    }
  }

  init() {
    this.maxStreamVolume = this.getStreamMaxVolume()
    store2.set('maxVideoVolume', this.maxStreamVolume);
    const initStreamVolume = store2('videoVolume') || this.maxStreamVolume * 0.75
    const resolution = this.getResolution()
    store.commit('UPDATE_MV_VIDEO_VOLUME', initStreamVolume)
    store.commit('CHANGE_PLAYING_MV_QUALITY', resolution)
    
    console.log('TSMedia', 'init', initStreamVolume, this.maxStreamVolume)
  }

  setSong(song) {
    this.song = song
    this.songJsonString = JSON.stringify(song)
    return this.songJsonString
  }

  startPlay(song) { // 播放（边下边播/在线播放）
    if (!song) {
      console.log('TSMedia', 'startPlay', '未传入歌曲', song)
      return
    }

    if (song.isAIMV) {
      this.playAi(song)
      return
    }
    if (song.downloadState === DownloadStatus.SUCCESS) {
      this.play(song)
      return
    }
    try {
      console.log('TSMedia', 'startPlay', song.music_name, '边下边播', JSON.stringify(song))
      song.acc = song.acc || 2
      song.org = song.org || 1
      song.resolution = store.state.playingMvQuality
      song.resolutions = store.state.availableQualities
      this.songJsonString = JSON.stringify(song)
      window.TSMedia.playOnline(this.songJsonString, true)
    } catch (error) {
      console.log(error)
    }
  }

  play(song) {
    if (!song) {
      console.log('TSMedia', 'play', '未传入歌曲')
      return
    }
    try {
      console.log('TSMedia', 'play', song.music_name, '打分', song.is_score, '本地播放', song)
      song.acc = song.acc || 2
      song.org = song.org || 1
      if (!song.score_source_url && store.state.config.score_url_format) {
        song.score_source_url = store.state.config.score_url_format.replace('{songid}', song.songid);
      }
      song.resolution = store.state.playingMvQuality
      song.resolutions = store.state.availableQualities
      this.songJsonString = JSON.stringify(song)
      window.TSMedia.play(this.songJsonString)
    } catch (error) {
      console.log(error)
    }
  }

  playAi(song) { // 播放AI换脸视频
    if (!song) {
      console.log('TSMedia', 'playAi', '未传入歌曲', song)
      return
    }

    try {
      console.log('TSMedia', 'playAi', song.music_name, JSON.stringify(song))
      const songJsonString = JSON.stringify(song)

      window.TSMedia.playOnline(songJsonString, true)
    } catch (error) {
      console.log(error)
    }
  }

  replay(song) {
    if (!song) {
      console.log('TSMedia', 'replay', '未传入歌曲')
      return
    }

    if (song.isAIMV) {
      this.playAi(song)
      return
    }
    
    try {
      console.log('TSMedia', 'replay', song.music_name, JSON.stringify(song))
      song.acc = song.acc || 2
      song.org = song.org || 1
      song.resolution = store.state.playingMvQuality
      song.resolutions = store.state.availableQualities
      this.songJsonString = JSON.stringify(song)
      window.TSMedia.replay(this.songJsonString)
      store.commit('RESET_CURR_SONG_LRC_INFO')
    } catch (error) {
      console.log(error)
    }
  }

  stop() {
    try {
      console.log('TSMedia.stop')
      window.TSMedia.stop()
    } catch (error) {
      console.log(error)
    }
  }

  playNext(song, isLocal) {
    if (!song) {
      console.log('TSMedia', 'playNext', '未传入歌曲')
      return
    }
    try {
      console.log('TSMedia', 'playNext', song.music_name, song.downloadState)
      song.acc = song.acc || 2
      song.org = song.org || 1
      song.resolution = store.state.playingMvQuality
      song.resolutions = store.state.availableQualities
      this.songJsonString = JSON.stringify(song)
      if (isLocal || song.downloadState === DownloadStatus.SUCCESS) {
        this.play(song)
      } else {
        this.startPlay(song)
      }
    } catch (error) {
      console.log(error)
    }
  }
  pause() {
    console.log('pause')
    try {
      window.TSMedia.pause()
    } catch (error) {
      console.log('pause error', error)
    }
  }
  resume() {
    try {
      const result = window.TSMedia.resume()
      console.log('resume', result)
      return JSON.parse(result)
    } catch (error) {
      return {
        resultCode: 1,
      }
    }
  }
  getIsPlaying() {
    try {
      const res = window.TSMedia.isPlaying();
      return res;
    } catch (error) {
      console.log('getIsPlaying error', error);
      
    }
  }

  getIsPlayingFinish() {
    try {
      const res = window.TSMedia.isPlayFinish();
      return res;
    } catch (error) {
      console.log('isPlayFinish error', error);
      
    }
  }
  
  getSelectedTrack() {
    try {
      const res = window.TSMedia.getSelectedTrack();
      console.log('getSelectedTrack', res);
      return res;
    } catch (error) {
      console.log('getSelectedTrack error', error);
      
    }
  }
  
  // 1原唱 2伴唱
  selectTrack(index) {
    try {
      window.TSMedia.selectTrack(index);
    } catch (error) {
      console.log('selectTrack error', error);
      
    }
  }
  
  getDuration() {
    try {
      const res = window.TSMedia.getDuration();
      console.log(res);
      return res;
    } catch (error) {
      console.log('getDuration error', error);
      
    }
  }
  
  getCurrentPosition() {
    try {
      const res = window.TSMedia.getCurrentPosition();
      console.log(res);
      return res;
    } catch (error) {
      console.log('getCurrentPosition error', error);
      
    }
  }
  
  getStreamMaxVolume() {
    try {
      const res = window.TSMedia.getMaxVolume();
      console.log('getStreamMaxVolume', res);
      return res;
    } catch (error) {
      console.log('getStreamMaxVolume error', error);
      
    }
  }
  
  getStreamVolume() {
    try {
      const res = window.TSMedia.getVolume();
      console.log('getStreamVolume', res);
      return res;
    } catch (error) {
      console.log('getStreamVolume error', error);
      
    }
  }
  
  setStreamVolume(volume) {
    try {
      console.log('setStreamVolume', volume);
      const _volume = Math.floor(volume / 100 * this.maxStreamVolume);
      console.log('setStreamVolume _volume', _volume);
      window.TSMedia.setVolume(_volume);
    } catch (error) {
      console.log('setStreamVolume error', error);
      
    }
  }
  
  updateToken() {
    try {
      // 根据需要实现具体的逻辑
    } catch (error) {
      console.log('updateToken error', error);
      
    }
  }
  
  setResolution(resolution) {
    try {
      console.log('setResolution', resolution);
      window.TSConfig.setResolution(Number(resolution));
    } catch (error) {
      console.log('setResolution error', error);
      
    }
  }
  
  getResolution() {
    try {
      const res = window.TSConfig.getResolution();
      console.log('getResolution', res);
      return res;
    } catch (error) {
      console.log('getResolution error', error);
      
    }
  }

  playSmallVideo(videoInfo) {
    if (store.state.orderedList.length > 0) {
      return
    }
    try {
      console.log('playSmallVideo', JSON.stringify(videoInfo));
      window.TSMedia.playSmallVideo(JSON.stringify(videoInfo));
    } catch (error) {
      console.log('playSmallVideo error', error);
    }
  }

  setMute(mute) {
    try {
      console.log('setMute', mute);
      window.TSMedia.setMute(mute);
    } catch (error) {
      console.log('setMute error', error);
    }
  }

  getMute() {
    try {
      const isMute = window.TSMedia.getMute();
      console.log('getMute', isMute);
      return isMute;
    } catch (error) {
      console.log('getMute error', error);
      return false;
    }
  }

  setSongLrc(songId, json) {
    try {
      console.log('TSMedia.setSongLrc', songId, json);
      window.TSMedia.setSongLrc(songId, JSON.stringify(json));
    } catch (error) {
      console.error('Error in setSongLrc method:', error);
    }
  }
}

export const TSMediaInstance = new TSMediaBridge()