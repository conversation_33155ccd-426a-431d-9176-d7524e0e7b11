import eventBus from "@/utils/event-bus";

const noopMethodList = [
  'start',
  'stop',
  'getSongDownloadState'
]

export class TSDownloaderBridge {

  constructor() {
    if (!window.TSSongDownload) {
      console.log('TSSongDownload interface not found!')
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSSongDownload fun:', method)
          console.log('call payload', JSON.stringify(payload))
        };
      });
    } else {
      console.log('TSSongDownload', 'interface', 'installed')
      this.init()
    }
  }

  init() {
  }
  attachTSWebEvent(webEventInstance) {
    webEventInstance.on('handleDownloadStart', (songId, mediaFileName, tmpFilePath, fileLength) => {
      eventBus.emit('handleDownloadStart', { songId, mediaFileName, tmpFilePath, fileLength })
    })
    webEventInstance.on('updateDownloadProgress', (songId, progress) => {
      eventBus.emit('updateDownloadProgress', { songId, progress })
    })
    webEventInstance.on('handleDownloaded', (songId, path, fileName, fileLength) => {
      eventBus.emit('handleDownloaded', { songId, path, fileName, fileLength })
    })
    webEventInstance.on('handleDownloadFail', (songId, errorCode, msg) => {
      eventBus.emit('handleDownloadFail', { songId, errorCode, msg })
    })
  }
  start(song) {
    try {
      console.log('TSSongDownload', 'start', song.songid, JSON.stringify(song), window.TSSongDownload.download);
      window.TSSongDownload.download(JSON.stringify(song));
    } catch (error) {
      console.log('TSSongDownload start', error);
    }
  }
  stop(songId) {
    try {
      window.TSSongDownload.stopDownload(songId);
    } catch (error) {
      console.log('TSSongDownload stop', error);
    }
  }
}

export const TSDownloaderInstance = new TSDownloaderBridge()