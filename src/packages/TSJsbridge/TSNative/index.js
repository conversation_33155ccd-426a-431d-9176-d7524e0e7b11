import Toast from '@/utils/toast'

const noopMethodList = [
  'getParams',
  'exit',
  'showDialog',
]

export class TSNativeBridge {
  app_version = ''

  constructor() {
    if (!window.TSNative) {
      console.log('TSNative interface not found!')
      Toast({
        message: 'TSNative interface not found!',
        className: 'not-support',
        position: 'top',
      })
      noopMethodList.forEach(method => {
        this[method] = () => {
          console.log('call TSNative fun:', method)
        };
      });
      window.tsNative = this
    } else {
      console.log('TSNative', 'interface', 'installed')
      this.init()
    }
  }

  init() {
  }

  getParams(key) {
    try {
      if (!key) {
        return ''
      }
      return window.TSNative.getValue(key)
    } catch (error) {
      console.log('getParams error', error)
      
    }
  }

  exit() {
    try {
      console.log('exit app!')
      window.TSNative.exit()
    } catch (error) {
      console.log('exit error', error)
      
    }
  }
  finish() {
    try {
      console.log('finish app!')
      window.TSNative.finish()
    } catch (error) {
      console.log('exit error', error)
    }
  }
  exitAndPmClear() {
    try {
      console.log('exitAndPmClear app!')
      window.TSNative.exitAndPmClear()
    } catch (error) {
      console.log('exitAndPmClear error', error)
    }
  }

  showRefreshBtn() {
    try {
      // window.TSNative.showRefreshBtn()
    } catch (error) {
      console.log('exit error', error)
    }
  }

  showDialog(msg) {
    try {
      if (!msg) return
      window.TSNative.showDialog(msg)
    } catch (error) {
      console.log('showDialog error', error)
    }
  }
}

export const TSNativeInstance = new TSNativeBridge()