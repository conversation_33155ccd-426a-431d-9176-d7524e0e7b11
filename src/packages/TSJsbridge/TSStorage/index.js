import { Toast } from '@vant/compat';
import { TSWebEventInstance } from '../TSWebEvent'

export const MIN_USABLE_SPACE = 300

export const noopMethodList = [
  'getUDiskPaths',
  'getStoragePaths',
  'getAllKindOfDevices',
  'getTotalSpace',
  'getFreeSpace',
]

export class TSStorageBridge {

  constructor() {
    if (!window.TSStorage) {
      console.log('TSStorage interface not found!')
      noopMethodList.forEach(method => {
        this[method] = (payload) => {
          console.log('call TSStorage fun:', method)
          console.log('call payload', JSON.stringify(payload))
        };
      });
    } else {
      console.log('TSStorage', 'interface', 'installed')
      this.init()
    }
  }

  init() {
    if (TSWebEventInstance) {
      // 插入外接存储设备
      TSWebEventInstance.on('handleStorageMounted', (dataJson) => {
        const data = JSON.parse(dataJson)
        console.log('handleStorageMounted', data)
        Toast(`挂载设备: ${data.type} ${data.id} ${data.path}`)
      })
      // 拔出外接存储设备
      TSWebEventInstance.on('handleStorageUnmounted', (dataJson) => {
        const data = JSON.parse(dataJson)
        console.log('handleStorageMounted', data)
        Toast(`卸载设备: ${data.type} ${data.id} ${data.path}`)
      })
    }
  }

  // 获取硬盘路径
  getStoragePaths() {
    let res = []
    try {
      res = JSON.parse(window.TSStorage.getStoragePaths())
    } catch (error) {
      console.log('getStoragePaths error', error)
    }
    return res
  }

  getUDiskPaths() {
    let res = []
    try {
      res = JSON.parse(window.TSStorage.getUDiskPaths())
    } catch (error) {
      console.log('getUDiskPaths error', error)
    }
    return res
  }

  getAllKindOfDevices() {
    try {
      const uDiskPaths = this.getUDiskPaths()
      const storagePaths = this.getStoragePaths()
      return [...uDiskPaths, ...storagePaths]
    } catch (error) {
      console.log('getAllKindOfDevices error', error)
      return []
    }
  }

  getTotalSpace(filePath) {
    try {
      const res = window.TSStorage.getTotalSpace(filePath)
      return res
    } catch (error) {
      console.log('getTotalSpace error', error)
      return 0
    }
  }

  getFreeSpace(filePath) {
    try {
      const res = window.TSStorage.getFreeSpace(filePath)
      return res
    } catch (error) {
      console.log('getFreeSpace error', error)
      return 0
    }
  }
}

export const TSStorageInstance = new TSStorageBridge()