const M = 1024 * 1024
const G = 1024 * M
const T = 1024 * G

export function isInteger(num) {
  return num % 1 === 0;
}

export function formatSpaceSize(spaceSize) {
  console.log('formatSpaceSize', spaceSize)
  if (spaceSize >= T) {
    return `${Math.floor(spaceSize / (T))}T`
  }
  if (spaceSize >= G) {
    const num = parseFloat(spaceSize / (G)).toFixed(2)
    if (isInteger(num)) {
      return `${Math.floor(num)}G`
    }
    return `${parseFloat(spaceSize / (G)).toFixed(2)}G`
  }
  if (spaceSize >= M) {
    return `${Math.floor(spaceSize / (M))}M`
  }
  return `${spaceSize}M`
}