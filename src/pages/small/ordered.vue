<template>
  <div class="small-ordered page">
    <SearchBar
      title="已点歌曲"
      :isShowOrder="false"
    />
    <div class="scroll-area">
      <div class="header flex-center">
        <div class="tab flex-between">
          <div
            class="tab-item flex-center"
            v-for="(tab, index) in tabList"
            :key="index"
            :class="{ actived: curTab.name === tab.name }"
            @click="handleChangeTab(tab)"
          >
            {{ tab.text }}
          </div>
        </div>
      </div>
      <component
        :is="currentComponent"
        @singer-click="handleClickSinger"
      ></component>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import AlreadySongList from '@/components/song-list/already/index.vue'
import OrderSongList from '@/components/song-list/order/index.vue'
import eventBus from '@/utils/event-bus'

const store = useStore()
const router = useRouter()

const orderedListNum = computed(() => store.state.orderedList.length)
const alreadyListNum = computed(() => store.state.alreadyList.length)
const currentSize = computed(() => store.state.currentSize);
const tabList = computed(() => {
  return [
    {
      name: 'ordered',
      text: `已点(${orderedListNum.value > 99 ? '99' : orderedListNum.value})`,
      keyword: '已点',
    },
    {
      name: 'already',
      text: `已唱(${alreadyListNum.value > 99 ? '99' : alreadyListNum.value})`,
      keyword: '已唱',
    },
  ]
})

let curTab = ref(tabList.value[0])

const currentComponent = computed(() => {
  return curTab.value.name === 'ordered' ? OrderSongList : AlreadySongList;
});

const handleChangeTab = (tab) => {
  if (curTab.value.name === tab.name) return
  curTab.value = tab
}

const handleClickSinger = ({ singer, singerhead, singerid }) => {
  router.push({
    name: 'songList',
    query: {
      name: singer,
      image: singerhead,
      singerid: singerid,
    },
  })
}

watch(currentSize, (newSize) => {
  console.log('0328', newSize)
  if (newSize !== 'small') {
    router.go(-1); // 返回上一页
    eventBus.emit('show-order-song-control-popup'); // 触发事件
  }
});

</script>

<style lang="stylus" scoped>
.dark-theme
  .small-ordered
    --tab-background rgba(255, 255, 255, 0.1)

.small-ordered
  --tab-background #1D1D1F14
  padding-bottom calc(83px * 3)
  .header
    position relative
    padding 0px
    height calc(58px * 3)
    margin-bottom calc(10px * 3)
    .tab
      width calc(296px * 3)
      height calc(58px * 3)
      background: var(--tab-background)
      border-radius calc(12px * 3)
      padding 0 calc(4px * 3)
      &-item
        width calc(145px * 3)
        height calc(50px * 3)
        color var(--text-tertiary-color)
        &.actived
          color var(--capsule-highlight-color)
          background var(--capsule-highlight-background)
          border-radius calc(10px * 3)

  .scroll-area
    overflow hidden
    .empty
      margin-top 30%
    .song-list
      height 94%
      :deep(.vue-recycle-scroller)
        display flex
        padding-bottom 150px
</style>
