<template>
  <div class="page">
    <SearchBar 
      :isShowBack="true"
      :title="currentSize !== 'small'  ? '唱过的歌' : ''"
      :centerTitle="currentSize === 'small'  ? '唱过的歌' : ''" />
    <div class="scroll-area">
      <div v-if="isLoading" class="loading-indicator">
      </div>
      <div v-else-if="isLogin && dataListNumber" class="list singing-list">
        <div class="list-left">
          <LoadMore
            class="song-list"
            ref="loadMoreRef"
            v-if="dataListNumber"
            @load-more="LoadMoreData"
            safeAreaHeight="11.6991vw"
          >
            <div v-if="currentSize === 'small'" class="songlist-small-cover-gedan">
              <img :src="listSheet.image" v-img-fallback="imgs[themeClass].imgFallback" :data-update="themeClass" />
              <p>{{ listSheet.name }}</p>
            </div>
            <!-- <div class="inner-cover">
              <img :src="listSheet.image" v-img-fallback="imgFallback" />
              <p>{{ listSheet.name }}</p>
            </div> -->
            <div v-for="(songItem, index) in dataList" :key="index">
              <SongItem
                v-if="index < 100"
                :songItem="songItem"
                
                :index="index" 
                :order-log="{
                  str1: '唱过的歌',
                  str2: '唱过的歌',
                }"
                :logData="logData"
              ></SongItem>
            </div>
          </LoadMore>
          <div v-else-if="!isRequest" class="list-right-empty">
            {{ hintTxt }}
          </div>
        </div>
        <div v-if="currentSize !== 'small'" class="list-right">
          <img :src="listSheet.image" v-img-fallback="imgFallback" />
          <p>{{ listSheet.name }}</p>
        </div>
      </div>
      <div v-else class="guess-nodata">
        <div class="empty">
          <DynamicIcon name="empty" useTheme="true" />
          <p>近一年没有点歌记录，快去点歌吧～</p>
        </div>
        <GuessSonglist 
          :pageSize="pageSize" 
          showAllData
          :renderType="currentSize == 'small' ? 'list' : 'block'"
          :class="`guess-${currentSize}`"
          from="singing"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import SongItem from '@/components/song-item/index.vue'
import GuessSonglist from '@/components/guess-song/songlist.vue'
import useVip from '@/composables/useVip'
import { getSingsingList } from '@/service/singing'
import { useStore } from 'vuex'
import { checkLandscapeOrPortrait } from '@/utils/device'
import { sendLog } from '@/directives/v-log/log'
import useLoading from '@/composables/useLoading';

const store = useStore()
const { showVipQrcode } = useVip()
const unionid = computed(() => store.state.userInfo.unionid)
const isLogin = computed(() => !!unionid.value)
const { showLoading, hideLoading } = useLoading();
const currentSize = computed(() => store.state.currentSize);
// 最多100条 - 需求紧 - 接口暂无分页逻辑
let loadMoreRef = ref(null)
const hintTxt = ref('')
const dataList = ref([])
const dataListNumber = computed(() => dataList.value.length)
const themeClass = computed(() => store.state.themeClass)

const imgs = {
  themeDark: {
    imgFallback: {
      loading: require('@/assets/images/singer-dark.png'),
      error: require('@/assets/images/singer-dark.png')
    },
  },
  themeLight: {
    imgFallback: {
      loading: require('@/assets/images/singer-light.png'),
      error: require('@/assets/images/singer-light.png'),
    },
  },
  themeSystem: {
    imgFallback: {
      loading: require('@/assets/images/singer-dark.png'),
      error: require('@/assets/images/singer-dark.png')
    },
  },
}
const imgFallback = {
  loading:
    'https://qncweb.ktvsky.com/20231212/vadd/d5d0736cbbe6b173599584f72c6b27f4.png',
  error:
    'https://qncweb.ktvsky.com/20231212/vadd/d5d0736cbbe6b173599584f72c6b27f4.png',
}
let listSheet = ref({
  name: '唱过的歌',
  image:
    'https://qncweb.ktvsky.com/20250212/vadd/02ca5f2f2eb347e9543abd011595b7ea.jpg',
})
const logData = {
  vipLog: {
    str1: '首页',
    str2: '唱过的歌',
    str3: '点击 VIP 歌曲',
  },
  fr: [1817, 1818],
}

let isRequest = false
let pageSize = ref(6)
const isLoading = ref(true)

const requestSingingData = async () => {
  let responseData = []
  responseData = await getSingsingList({
    unionid: unionid.value,
  })
  return responseData
}

const fetchData = async () => {
  if (!unionid.value) {
    hideLoading();
    isLoading.value = false;
    return;
  }

  if (isRequest) {
    return;
  }

  isRequest = true;
  isLoading.value = true;

  try {
    const singingResponseData = await requestSingingData();
    dataList.value = singingResponseData;
    // dataList.value = []
  } catch (error) {
    console.error(error);
  } finally {
    isRequest = false;
    isLoading.value = false;
    hideLoading();
  }
};

// 此处暂无分页
const LoadMoreData = () => {
  console.log('没有更多数据了~')
}

// const handleShowVip = () => {
//   if (!isLogin.value) {
//     showVipQrcode()
//     sendLog({
//       event_type: '10000~50000',
//       event_name: 10007,
//       event_data: {
//         str1: '首页',
//         str2: '常唱',
//         str3: '进入常唱',
//         str4: 'click',
//         str9: '点击登录',
//       },
//     })
//   }
// }

watch(unionid, (val) => {
  if (val) {
    fetchData()
  }
})

onMounted(() => {
  showLoading()
  fetchData()
  setTimeout(() => {
    hintTxt.value = '近一年没有点歌记录，快去点歌吧～'
  }, 800)
  pageSize.value = checkLandscapeOrPortrait() === 'landscape' ? 6 : 9
})
</script>

<style lang="stylus" scoped>
.page
  .guess-nodata
    width 100%
    height 100%
    padding-bottom 200px
    overflow-y scroll
    padding 0 70px
    .empty
      p
        font-size var(--font-size-large)
    
.infinite-loading
  display flex
  justify-content center
.singing-list
  padding 0 165px 0 0
  height 100%
  overflow hidden
  // 三分之一屏
  @media (max-width: 700px)
    padding 0
    overflow-y scroll
  @media (max-width: 900px) and (min-width: 701px)  
    padding 0 15px 0 0!important
.list
  // padding 0 230px 0 80px
  display flex
  justify-content space-between
  &-right
    width 400px
    margin-top calc(50px * var(--scale))
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(220px * 1.48) !important
    img
      width 400px
      height 400px
      margin-top 0!important
      border-radius 24px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(220px * 1.48) !important
        height calc(220px * 1.48) !important
    p
      width 100%
      font-size 52px
      text-align center
      margin-top calc(36px * var(--scale))
  &-left
    width 1090px
    margin 0 !important
    padding 0 !important
    // 三分之一屏
    @media (max-width: 700px)
      width 1090px
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width 1160px
    .song-list
      width 100% !important
      padding 0 20px 110px 20px !important
    &-empty
      margin-top 30vh
      font-size 28px
      color rgba(255, 255, 255, 0.5)
      text-align center
      
  .songlist-small-cover-gedan
    margin-top 20px
    margin-bottom calc(20px * 3)
    text-align center
    img
      width calc(180px * 3)
      height calc(180px * 3)
      border-radius calc(16px * 3)
      margin 0 auto
    p
      margin-top calc(12px * 3) 

</style>

