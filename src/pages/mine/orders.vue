<template>
  <div class="page order">
    <SearchBar title="购买记录" :isShowOrder="false" />
    <div class="scroll-area">
      <div class="order-list">
        <LoadMore
          v-if="orders.length"
          @load-more="handleGetUserOrdersList"
          safeAreaHeight="12.6991vw"
        >
          <div class="order-item" v-for="(item, index) in orders" :key="index">
            <div class="left">
              <h3>{{ item.vip_desc }}</h3>
              <p>{{ item.vip_datetime }}</p>
            </div>
            <div class="right">¥<span>{{ formatPrice(item.vip_price) }}</span></div>
          </div>
          <div class="order-bottom">
            <p>已加载全部</p>
          </div>
        </LoadMore>
        <div v-else-if="!isRequest" class="order-empty">
          <DynamicIcon name="empty1" />
          <p>暂无购买记录</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeMount, computed, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { getUserOrders } from '@/service/vip'
import eventBus from '@/utils/event-bus'

const store = useStore()
const userInfo = computed(() => store.state.userInfo)

let orders = ref([])
let p = 1
let isRequest = ref(false)

const handleGetUserOrdersList = async (payload) => {
  if (payload === 'nats') {
    isRequest = false
    p = 1
    orders.value = []
  }
  if (isRequest.value) {
    return
  }
  isRequest.value = true

  const historyResponseData = await getUserOrders(
    userInfo.value.unionid,
    p
  )
  if (historyResponseData.length) {
    orders.value = [...orders.value, ...historyResponseData]
    p++
  }
  isRequest.value = false
}

const formatPrice = (val) => {
  return val.replace('￥', '');
};

onBeforeMount(() => {
  handleGetUserOrdersList()
  eventBus.on('nats-vip-pay', handleGetUserOrdersList.bind(null, 'nats'))
})

onUnmounted(() => {
  eventBus.off('nats-vip-pay', handleGetUserOrdersList)
})
</script>

<style lang="stylus" scoped>
.order
  margin-top 0px

  // 三分之一屏
  @media (max-width: 700px)
    .scroll-area
      padding 0 calc(32px * 3)

  &-list
    padding 0 160px

    // 三分之一屏
    @media (max-width: 700px)
      padding 0 0px
      
  &-title
    padding 36px 0 25px
    margin-bottom 0px
    opacity 0.6
    border-bottom 2px solid var(--border-color)
    font-size var(--font-size-large)

  .order-item
    display flex
    justify-content space-between
    align-items center
    height 141px
    border-bottom 1px solid var(--border-color)
  
    // 三分之一屏
    @media (max-width: 700px)
      height calc(98px * 3)
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      height calc(97px * 1.48) !important
    h3
      margin-bottom 10px
    p
      font-size var(--font-size-small)
      opacity 0.4
    .right
      span
        font-size var(--font-size-extra-large)
  &-bottom
    margin-top 22px
    text-align center
    opacity 0.4
    p
      font-size var(--font-size-tiny)

      // 三分之一屏
      @media (max-width: 700px)
        font-size var(--font-size-small)
        margin-top calc(70px * 3)
  &-empty
    width 100%
    padding calc(186px * var(--scale))  0 188px
    margin 0px auto 0
    text-align center
    min-height 30vh
    opacity 1
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      margin 5vh auto 0 !important
    .svg-icon
      width 90px
      height 90px
      margin-bottom 4px
      margin-left auto
      margin-right auto
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(50px * var(--scale)) !important
        height calc(50px * var(--scale)) !important
    p
      opacity 0.3
      font-size var(--font-size-extra-small)
</style>
