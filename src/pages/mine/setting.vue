<template>
  <div class="page setting">
    <SearchBar title="设置" :isShowOrder="false" />
    <div class="setting-content scroll-area">
      <ul>
        <li v-if="enableScoring" class="score-control">
          <div>
            <div class="left">打分模式</div>
            <div class="right">
              <van-switch
                v-model="isEnabledScore"
                @click="handleClick"
                active-color="#DBAE6A"
                inactive-color="transparent"
              />
            </div>
          </div>
          <div class="controls">
            <div class="tabs">
              <button
                v-for="item in scoreTabs"
                :key="item.id" 
                :class="{ active: activeScore === item.id }"
                @click="handleTabClick(item)"
                :style="{
                  opacity: isEnabledScore ? 1 : 0.5
                }"
              >{{ item.name }}</button>
            </div>
            <button class="button list" @click="handleButtonClick">评分记录</button>
          </div>
        </li>
        <li>
          <div>清理缓存</div>
          <div>
            <p
              v-show="cacheSize"
              @click="handleClickStorage"
              v-log="{
                event_type: 'click',
                event_name: '6009',
                event_data: {
                  str1: '设置页',
                  str2: '清理缓存',
                  str3: '点击清理缓存',
                  str4: 'click',
                }
              }"
            >清理缓存 {{ cacheSize }}</p>
            <p v-show="!cacheSize">暂无缓存</p>
          </div>
        </li>
        <li>
          <div>清除数据</div>
          <div>
            <p
              @click="handleClickData"
              v-log="{
                event_type: 'click',
                event_name: '6009',
                event_data: {
                  str1: '设置页',
                  str2: '清除数据',
                  str3: '点击清除数据',
                  str4: 'click',
                }
              }"
            >清除数据 {{ dataSize }}</p>
          </div>
        </li>
        <li class="agreement-wrapper">
          <div>相关协议</div>
          <div class="agreement">
            <p
              @click="handleCheckAgreement('agreementUser')"
              v-log="{
                event_type: 'click',
                event_name: '6009',
                event_data: {
                  str1: '设置页',
                  str2: '用户协议',
                  str3: '点击用户协议',
                  str4: 'click',
                }
              }"
            >《用户服务协议》</p>
            <p
              @click="handleCheckAgreement('agreementPrivacy')"
              v-log="{
                event_type: 'click',
                event_name: '6009',
                event_data: {
                  str1: '设置页',
                  str2: '隐私协议',
                  str3: '点击隐私协议',
                  str4: 'click',
                }
              }"
            >《隐私权政策》</p>
          </div>
        </li>
        <li>
          <div>切换账号</div>
          <div class="login-out">
            <p
              @click="handleLoginout"
              v-log="{
                event_type: 'click',
                event_name: '6009',
                event_data: {
                  str1: '设置页',
                  str2: '退出账号',
                  str3: '点击退出账号',
                  str4: 'click',
                }
              }"
            >退出当前账号</p>
          </div>
        </li>
        <li>
          <div>注销账号</div>
          <div class="login-out">
            <p
              @click="handleLoginoff"
              v-log="{
                event_type: 'click',
                event_name: '6009',
                event_data: {
                  str1: '设置页',
                  str2: '注销账号',
                  str3: '点击注销账号',
                  str4: 'click',
                }
              }"
            >去注销</p>
          </div>
        </li>
      </ul>
      <ServiceLicence />
    </div>
  </div>
</template>


<script>
import { computed, onBeforeMount, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import useLoading from '@/composables/useLoading';
import useScore from '@/composables/useScore';
import { sendLog } from '@/directives/v-log/log'
import { Dialog, Switch } from 'vant'
import { TSFileInstance, TSNativeInstance, TSScoreInstance } from '@/packages/TSJsbridge';
import Toast from '@/utils/toast'
import store2 from 'store2'
import config from '@/config'
import { accountCancel } from '@/service/user'
import { parseISO, format } from 'date-fns'

export default {
  name: 'Setting',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    'van-switch': Switch
  },
  setup() {
    const store = useStore()
    const router = useRouter()

    const { showLoading, hideLoading } = useLoading();
    const { handleToggleEnabledScore } = useScore();

    const macAddress = computed(() => store.state.system.systemInfo.mac)
    const userInfo = computed(() => store.state.userInfo)
    const themeClass = computed(() => store.state.themeClass)
    const vipInfo = computed(() => store.state.vipInfo)
    const isVip = computed(() => !!vipInfo.value.end_time)
    const end_time = computed(() => (isVip.value ? vipInfo.value.end_time.split(' ')[0] : ''))

    const cacheSize = ref('')
    const dataSize = ref('')
    const isEnabledScore = computed(() => {
      return store.state.score.enabled;
    });
    const scoreTabs = [
      { name: '入选新秀', id: 1, event: 30062 },
      { name: '业余选手', id: 2, event: 30063 },
      { name: '实力唱将', id: 3, event: 30064 },
    ]
    const activeScore = ref(0)

    const handleTabClick = ({ id, name, event }) => {
      if (!isEnabledScore.value || id === activeScore.value) return
      sendLog({
        event_type: '10000~50000',
        event_name: event,
        event_data: {
          str1: '个人中心',
          str2: '设置',
          str3: name,
          str4: 'click',
        },
      })

      activeScore.value = id
      TSScoreInstance.setScoreLevel(id)
    }
    
    const handleCheckAgreement = (name) => {
      router.push({
        name
      })
    }

    const handleLoginout = () => {
      Dialog.confirm({
        // className: 'global-force-login',
        confirmButtonText: '退出账号',
        cancelButtonText: '取消',
        title: '确定退出当前账号？'
      }).then(() => {
        store.dispatch('loginout', {
          mac_id: macAddress.value,
          unionid: userInfo.value.unionid,
          cleardata: false
        })
        store.dispatch('singTime/resetSingTimeData')
        // 收藏功能下线
        // store.dispatch('collect/resetUserCollectList')
        sendLog({
          event_type: '10000~50000',
          event_name: 10031,
          event_data: {
            str1: '我的',
            str2: '退出登录',
            str3: '点击退出',
            str4: 'click',
          },
        })
        router.push({
          name: 'home'
        })
      })
    }

    const handleClickStorage = () => {
      Dialog.confirm({
        // className: 'global-force-login',
        confirmButtonText: '清除',
        cancelButtonText: '取消',
        title: '删除应用数据',
        message: '确认要清除缓存吗？清除缓存不会对使用有任何影响。'
      }).then(() => {
        showLoading()
        const res = TSFileInstance.clearCache()
        hideLoading()
        if (res) {
          cacheSize.value = ''
        } else {
          Toast('删除缓存失败')
        }
      })
    }

    const clearLocalStorage = () => {
      showLoading()
      return new Promise((resolve, reject) => {
        store.dispatch('loginout', {
          mac_id: macAddress.value,
          unionid: userInfo.value.unionid,
          cleardata: true
        }).then(() => {
          store2.clearAll()
          setTimeout(() => {
            resolve();
          }, 1000); // 确保登出操作完成后再清除本地存储
        }).catch((error) => {
          reject(error);
        });
      });
    }

    const handleClickData = () => {
      Dialog.confirm({
        className: 'text-align-left',
        confirmButtonText: '删除并退出应用',
        cancelButtonText: '取消',
        title: '删除应用数据',
        message: '应用的全部数据删除后不可恢复，包括所有伴奏、使用记录以及其他数据？'
      }).then(async () => {
        showLoading()
        await clearLocalStorage()
        if (window.TSFile) {
          const res = TSFileInstance.clearData()
          hideLoading()
          if (res) {
            dataSize.value = ''
            TSNativeInstance.exit()
          } else {
            Toast('删除数据失败')
          }
        } else {
          hideLoading()
          window.location.href = '#/'
          window.location.reload()
        }
      })
    }

    const handleClick = () => {
      store2.set('isEnabledScore', !isEnabledScore.value)
      handleToggleEnabledScore()
      sendLog({
        event_type: '10000~50000',
        event_name: 30061,
        event_data: {
          str1: '个人中心',
          str2: '设置',
          str3: '打分开关',
          str4: 'click',
        },
      })
      if (isEnabledScore.value) {
        activeScore.value = TSScoreInstance.getScoreLevel()
      }
    }
    
    const handleButtonClick = () => {
      router.push({
        name: 'score-results'
      })
    }

    const handleChangeTheme = (theme) => {
      store.commit('SET_THEME', theme)
    }

    const handleLoginoff = () => {
      sendLog({
        event_type: '30000～35000',
        event_name: 6008,
        event_data: {
          str1: '设置页',
          str2: '注销账号',
          str3: '点击注销账号',
          str4: 'click',
        },
      })
      const date = parseISO(end_time.value)
      const formattedDate = end_time.value ? format(date, 'yyyy/MM/dd') : ''

      const confirmMessage = isVip.value
        ? `您确认注销账号，并删除所有账号信息及应用数据，同时放弃VIP权益吗？\nVIP到期时间：${formattedDate}`
        : '您确认注销账号，并删除所有账号信息及应用数据吗？'

      Dialog.confirm({
        className: 'global-force-login loginoff',
        title: '注销账号',
        confirmButtonText: '确认并注销',
        cancelButtonText: '取消注销',
        messageAlign: 'left',
        message: confirmMessage,
        beforeClose: () => {
          return true
        },
      }).then(async () => {
        await accountCancel(userInfo.value.unionid)

        clearLocalStorage().then(async () => {
          // await store.dispatch('getCarplayInfo')
          // store.commit('YS_TIP_ACCEPT', false)
          // router.push({ name: 'home' })
          const res = TSFileInstance.clearData()
          hideLoading()
          if (res) {
            dataSize.value = ''
            TSNativeInstance.exit()
          } else {
            Toast('删除数据失败')
          }
        })
      })
    }

    onBeforeMount(() => {
      cacheSize.value = TSFileInstance.getCacheSize()
      dataSize.value= TSFileInstance.getDataSize()
      if (isEnabledScore.value) {
        activeScore.value = TSScoreInstance.getScoreLevel()
      }
    })
    
    return {
      cacheSize,
      dataSize,
      isEnabledScore,
      scoreTabs,
      activeScore,
      handleLoginout,
      handleCheckAgreement,
      handleClickStorage,
      handleClickData,
      handleTabClick,
      handleClick,
      handleButtonClick,
      themeClass,
      handleChangeTheme,
      enableScoring: config.enableScoring,
      handleLoginoff,
    }
  }
}
</script>

<style lang="stylus" scoped>
.setting
  height 100vh
  display flex
  overflow hidden
  padding-bottom 40px
  .setting-content
    width 100%
    // padding-left 335px
    // padding-right 335px
    padding 0 250px
    margin 0 auto
    flex 1
    overflow-y scroll
    li
      display flex
      justify-content space-between
      align-items center
      padding 55px 0 57px
      border-bottom 2px solid var(--border-color)
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        padding calc(29px * var(--scale)) 0 calc(29px * var(--scale)) !important

      // 三分之一屏
      @media (max-width: 700px)
        border-bottom-width 1px
        padding-top calc(22px * 3) 
        padding-bottom calc(22px * 3) 

      .themes
        display flex
        p
          margin-left 40px
        .active
          border: 2px solid #DBAE6ACC
          color #DBAE6A
      .agreement
        display flex
        p:first-child
          margin-right 40px
      p
        border-radius: 100px;
        border: 2px solid var(--border-color2)
        background: rgba(255, 255, 255, 0.08);
        padding 0 calc(36px * var(--scale))
        height calc(50px * var(--scale))
        line-height calc(50px * var(--scale))
        color var(--text-tertiary-color)
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(184px * var(--scale))
          height  calc(50px * var(--scale)) !important
          line-height calc(50px * var(--scale)) !important
          text-align center !important
        white-space nowrap
      .login-out
        p
          color: rgba(225, 61, 61, 1)
      &.score-control
        display block
        :deep(.van-switch)
          --van-switch-width 110px !important
          --van-switch-height 60px !important
          --van-switch-size	40px !important
          --van-switch-node-size 40px !important
          border: 2px solid rgba(255,255,255,0.2)
          box-sizing border-box
          &__node
            left 0
            background rgba(255,255,255,0.4)
            margin-top 0px
            left 10px
            top 50%
            transform translateY(-50%)
        :deep(.van-switch--on)
          border-color #DBAE6A
          .van-switch__node
            background #fff!important
            left unset!important
            right 10px
            margin-top 0!important
        & > div
          display flex
          justify-content space-between
          align-items center
        .controls
          margin-top 40px
          .tabs
            display flex
          button
            width 200px
            height 80px
            display flex
            justify-content center
            align-items center
            font-size 28px
            border: 2px solid #FFFFFF33
            background #FFFFFF14
            border-radius 100px
            margin-right 40px
            &.active
              border-color rgba(219, 174, 106, 0.8)
              color rgba(219, 174, 106, 1)
              background rgba(219, 174, 106, 0.1)
            &.list
              margin-right 0

    // 三分之一屏
    @media (max-width: 700px)
      padding-left calc(32px * 3) !important
      padding-right calc(32px * 3) !important
      .agreement-wrapper
        display block
        .agreement
          display flex
          justify-content space-between
        p
          padding 0 calc(20px * 3)
          margin-top calc(20px * 3)
</style>