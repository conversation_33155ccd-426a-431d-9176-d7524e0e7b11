<template>
  <div class="page mine">
    <SearchBar
      :isShowMic="true"
      title="我的"
    />
    <div class="scroll-area">
      <MineVip />
      <div class="mine-singed" :class="!dataList.length && 'singed-empty'">
        <div class="mine-singed-title" :class="!enableRecording && 'flex-start'">
          <p :class="selectIndex === 0 && 'active'" @click="handleClickTab(0)">唱过的歌</p>
          <p v-if="enableRecording" :class="selectIndex === 1 && 'active'" @click="handleClickTab(1)">我的录音</p>
        </div>
        <div v-show="selectIndex === 0" class="mine-singed-list" @click.stop ref="singedList">
          <LoadMore
            class="song-list"
            v-if="dataList.length"
            @load-more="fetchData"
          >
            <SongItem
              className="sheet-list-song-item"
              v-for="(songItem, index) in dataList"
              :key="index"
              :songItem="songItem"
              :index="index"
              :order-log="{
                str1: '我的',
                str2: '唱过的歌',
              }"
              :logData="logData"
            />
          </LoadMore>
          <div v-else class="mine-empty">
            <DynamicIcon name="empty1" />
            <h3>近一年没有点歌记录，快去点歌吧～</h3>
            <GuessSonglist
              :pageSize="currentSize == 'small' ? 5 : currentSize == 'medium' ? 6 : 9"
              :renderType="currentSize == 'small' ? 'list' : 'block'"
              :class="`guess-${currentSize}`"
              from="search"
            />
          </div>
        </div>
        <div v-show="enableRecording && selectIndex === 1" class="mine-record-list" @click.stop>
          <LoadMore
            class="song-list"
            v-if="recordList.length"
            @load-more="fetchData"
          >
            <RecordItem
              v-for="(recording, index) in recordList"
              :key="index"
              :recording="recording"
              :isPlaying="currentAudio.id === recording.id"
              :playRecordStatus="playRecord" 
            />
          </LoadMore>
          <div v-else-if="!isRequest" class="mine-empty">
            <h3>近一年没有录音记录，快去点歌吧～</h3>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { sendLog } from '@/directives/v-log/log'
import MineVip from '@/components/vip/index.vue'
import { getSingsingList } from '@/service/singing'
import { getRecords, reportPlayback, deleteRecording } from '@/service/record'
import SongItem from '@/components/song-item/index.vue'
import RecordItem from '@/components/record-item/index.vue'
import GuessSonglist from '@/components/guess-song/songlist.vue'
import eventBus from '@/utils/event-bus'
import useRecordShare from '@/components/modal/global/record-share/create';
import useLoading from '@/composables/useLoading';
import { TSMicrophoneInstance } from '@/packages/TSJsbridge';
import { TSWebEventInstance } from '@/packages/TSJsbridge';
import config from '@/config'

const store = useStore()
const unionid = computed(() => store.state.userInfo.unionid)
const mediaVolume = computed(() => store.state.mediaVolume)
const mvIsHide = computed(() => store.state.mvIsHide);
const currentSize = computed(() => store.state.currentSize);
const audioPlayer = ref(null)
const recordShare = useRecordShare()
const { showLoading, hideLoading } = useLoading();

const enableRecording = config.enableRecording
const dataList = ref([])
const recordList = ref([])
const selectIndex = ref(0)
const currentAudio = ref({
  id: 0
})
let isRequest = ref(false)
let p = 1
let isEmpty = false

const logData = {
  vipLog: {
    str1: '我的',
    str2: '唱过的歌',
    str3: '点击 VIP 歌曲',
  },
  fr: 1848,
}

const requestSingingData = async () => {
  let responseData = []
  responseData = await getSingsingList({
    unionid: unionid.value,
  })
  return responseData
}

const fetchData = async () => {
  if (!unionid.value) return
  if (isRequest.value) {
    return
  }
  isRequest.value = true
  if (selectIndex.value === 0) {
    const singingResponseData = await requestSingingData()
    dataList.value = [...dataList.value, ...singingResponseData].slice(0, 50)
  } else {
    if (isEmpty) {
      isRequest.value = false
      return
    }
    const data = await getRecords({
      unionid: unionid.value,
      p,
    })
    if (p == 1) {
      recordList.value = data
    } else {
      recordList.value = [...recordList.value, ...data]
    }
    p++
    isEmpty = data.length < 30
  }
  isRequest.value = false
}

const handleClickTab = (index) => {
  if (index === selectIndex.value) return
  isRequest.value = false
  selectIndex.value = index
  p = 1
  fetchData()
  if (index === 1) {
    isEmpty = false
    sendLog({
      event_type: '10000~50000',
      event_name: 30047,
      event_data: {
        str1: '个人中心',
        str2: '我的录音',
        str3: '我的录音teb',
        str4: 'click',
      },
    })
  }
}

const onPauseRecord = () => {
  if (currentAudio.value.id) {
    console.log('录音 暂停播放')
    currentAudio.value = {
      id: 0
    }
  }
}

const onPlayRecord = async (payload) => {
  console.log('录音 开始播放', payload.id, mediaVolume.value)
  currentAudio.value = payload
  nextTick(async () => {
    eventBus.emit('handle-video-pause')
    sendLog({
      event_type: '10000~50000',
      event_name: 30048,
      event_data: {
        str1: '个人中心',
        str2: '我的录音',
        str3: '播放任意录音',
        str4: 'click',
      },
    })
    await reportPlayback({
      unionid: unionid.value,
      id: payload.id,
    })
  })
}

const handleAudioEnded = () => {
  console.log('音频播放完成');
  TSMicrophoneInstance.pauseLoopback(0)
  currentAudio.value = {
    id: 0
  }
}

const handleAudioError = (event) => {
  console.error('音频播放错误', event.target.error);
  TSMicrophoneInstance.pauseLoopback(0)
  currentAudio.value = {
    id: 0
  }
}

const onShareRecord = ({ share_url, music_name, singer }) => {
  recordShare.show({
    info: {
      qr: share_url,
      music_name,
      singer,
    }
  })
}

const onDeleteRecord = async({ id }) => {
  try {
    showLoading()
    await deleteRecording({
      id,
      unionid: unionid.value
    })
    hideLoading()
    audioPlayer.value.pause()
    recordList.value = recordList.value.filter(record => record.id !== id);
    sendLog({
      event_type: '10000~50000',
      event_name: 30049,
      event_data: {
        str1: '个人中心',
        str2: '我的录音',
        str3: '删除任意录音',
        str4: 'click',
      },
    })
  } catch (error) {
    hideLoading()
  }
}

const handleControlVideoResume = () => {
  onPauseRecord()
}

watch(unionid, (val) => {
  if (val) {
    fetchData()
  }
})

watch(mvIsHide, (val) => {
  if (val && selectIndex.value == 1) {
    p = 1
    isEmpty = false
    recordList.value = []
    isRequest.value = false
    fetchData()
  }
}, {
  immediate: true,
  deep: true,
})

const playRecord = ref(3)
const handleAudioUploadState = (status) => {
  playRecord.value = Number(status)
}

const singedList = ref(null);

onMounted(async () => {
  sendLog({
    event_type: 'show',
    event_name: '6008',
    event_data: {
    str1: '我的页',
    str2: '我的页',
    str3: '进入我的页',
    str4: 'show',
    }
  })
  TSWebEventInstance.on('handleAudioUploadState', handleAudioUploadState)
  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: 10026,
  //   event_data: {
  //     str1: '我的',
  //     str2: '我的页',
  //     str3: '进入我的页',
  //     str4: 'show',
  //   },
  // })
  fetchData()
  eventBus.on('handle-pause-record', onPauseRecord)
  eventBus.on('handle-play-record', onPlayRecord)
  eventBus.on('handle-share-record', onShareRecord)
  eventBus.on('handle-delete-record', onDeleteRecord)
  eventBus.on('video-control-resume', handleControlVideoResume)

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        sendLog({
          event_type: 'show',
          event_name: '6008',
          event_data: {
            str1: '我的页',
            str2: '唱过的歌',
            str3: '歌单展示',
            str4: 'show',
          }
        })
      }
    });
  }, {
    root: null, // 使用视口作为根
    threshold: 0.1 // 触发可见性的阈值，0.1表示10%可见时触发
  });

  await nextTick()
  if (singedList.value) {
    observer.observe(singedList.value);
  }
})

onUnmounted(() => {
  eventBus.off('handle-pause-record', onPauseRecord)
  eventBus.off('handle-play-record', onPlayRecord)
  eventBus.off('handle-share-record', onShareRecord)
  eventBus.off('handle-delete-record', onDeleteRecord)
  eventBus.off('video-control-resume', handleControlVideoResume)
  if (audioPlayer.value) {
    audioPlayer.value.removeEventListener('ended', handleAudioEnded);
    audioPlayer.value.removeEventListener('error', handleAudioError);
  }
})
</script>

<style lang="stylus" scoped>
.mine
  .scroll-area
    padding 0 180px
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      padding 0 70px !important
  &-singed
    margin-top 64px
    position sticky
    top 0px
    padding 0 0px 100px
    &-title
      display flex
      justify-content space-around
      padding-left 20px
      &.flex-start
        justify-content flex-start
      p
        position relative
        text-align center
        color: var(--text-secondary-color)
        font-size var(--font-size-large)
      span
        font-size 32px
    &-list
      height 100%
      padding-bottom 100px
    .song-list
      padding 20px 0 0

      // 三分之一屏
      @media (max-width: 700px)
        padding-top 0px
        
      .song-item
        --list-offset 20px
        padding 0 20px
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          height calc(90px * 1.48) !important
  &-empty
    padding-top 90px
    .svg-icon
      width 75px
      height 75px
      margin 0 auto

      // 三分之一屏
      @media (max-width: 700px)
        width calc(50px * 3) !important
        height calc(50px * 3) !important
        margin-bottom calc(20px * 3)
        
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(50px * var(--scale)) !important
        height calc(50px * var(--scale)) !important
        margin-bottom calc(20px * var(--scale))
    h3
      padding-top 0px
      width auto
      text-align center
      opacity 0.6
      // color rgba(255, 255, 255, 0.4)
      font-size: var(--font-size-medium) !important
      // background url(https://qncweb.ktvsky.com/20231208/other/8735391456ed4d23857d801ce6c5482b.svg) no-repeat top center
      // background-size 90px auto
      margin 30px auto 80px
      white-space nowrap
    // :deep(.sec-gusse-sing)
    //   padding 0!important
    //   &-list
    //     display grid
    //     grid-template-columns repeat(3, 493px)
    //     justify-content space-between
    //     .song-block
    //       width 100%
</style>
