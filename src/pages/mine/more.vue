<template>
  <div class="more">
    <SearchBar title="更多" :isShowOrder="false" />
    <ul>
      <li class="flex-between" @click="handleExchange">
        <span>兑换VIP</span>
        <DynamicIcon name="right-icon" />
      </li>
      <li class="flex-between" @click="handleClickBuyOrders">
        <span>购买记录</span>
        <DynamicIcon name="right-icon" />
      </li>
      <li class="flex-between" @click="handleSetting">
        <span>设置</span>
        <DynamicIcon name="right-icon" />
      </li>
    </ul>
  </div>

</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const handleExchange = () => {
  router.push({ name: 'vipExchange' })
}

const handleClickBuyOrders = () => {
  router.push({ name: 'orders' })
}

const handleSetting = () => {
  router.push({ name: 'setting' })
}
</script>

<style lang="stylus" scoped>
.more
  padding 0 0
  ul
    margin 0 calc(28px * 3)
  li
    height calc(81px * 3)
    border-bottom 4px solid var(--border-color)
    .svg-icon
      width calc(48px * 3)
      height calc(48px * 3)
</style>
