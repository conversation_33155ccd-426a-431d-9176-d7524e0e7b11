<template>
  <div class="page search">
    <SearchBar
      placeholder="搜索歌曲、歌手"
      @search="handleOnSearch"
      :isShowSearch="true"
      title=""
      :isShowVipActEnter="isShowVipActEnter"
      :enableBack="!!keyword"
      ref="searchRef"
      @back="handleBack"
    ></SearchBar>
    <div class="scroll-area">
      <div class="song-list flex-column" v-if="keyword">
        <div class="tabs global-tab flex-center">
          <div
            class="item"
            @click="handleSwitchSearchTab('song')"
          >
            <div class="item-txt" :class="{ active: curSearchTab === 'song' }">歌曲</div>
          </div>
          <div
            class="tab"
            @click="handleSwitchSearchTab('singer')"
            v-log="{
              event_type: 'click',
              event_name: '6002',
              event_data: {
                str1: '搜索页',
                str2: '搜索结果页',
                str3: '歌手teb',
                str4: 'click',
              }
            }"
          >
            <div class="item-txt" :class="{ active: curSearchTab == 'singer' }">歌手</div>
          </div>
        </div>
        <div v-if="curSearchTab === 'song'" class="song-list-content">
          <LoadMore
            v-if="resultData.song.length"
            ref="loadMoreRef"
            @load-more="getSearchResult"
            safeAreaHeight="16.6991vw"
          >
            <SongItem
              v-for="(songItem, index) in resultData.song"
              :key="index"
              :songItem="songItem"
              :index="index"
              :order-log="{
                str1: '搜索页',
                str2: '搜索结果页',
              }"
              from="2"
              :logData="logData"
            ></SongItem>
          </LoadMore>
          <div v-else-if="!isInit" class="empty-wrapper">
            <div class="empty">
              <DynamicIcon name="empty" useTheme="true" />
              <p>抱歉，暂无“{{ keyword }}”结果</p>
            </div>
            <!-- <GuessSonglist showAllData="true" renderType="block" /> -->
            <GuessSonglist
              :pageSize="currentSize == 'small' ? 5 : currentSize == 'medium' ? 6 : 9"
              :renderType="currentSize == 'small' ? 'list' : 'block'"
              :class="`guess-${currentSize}`"
              from="search"
            />
          </div>
        </div>
        <div v-if="curSearchTab !== 'song'" class="song-list-content">
          <LoadMore
            class="singer-list"
            ref="loadMoreRef"
            v-if="resultData.singer.length"
            @load-more="getSearchResult"
            safeAreaHeight="16.6991vw"
          >
            <SingerItem
              v-for="(item, index) in resultData.singer"
              :key="index"
              :singer="item"
              @click="handleClickSinger(item)"
              v-log="{
                event_type: 'click',
                event_name: '6002',
                event_data: {
                  str1: '搜索页',
                  str2: '搜索结果页',
                  str3: '点击歌手',
                  str4: 'click',
                }
              }"
            ></SingerItem>
          </LoadMore>
          <div v-else-if="!isInit" class="empty-wrapper">
            <div class="empty">
              <DynamicIcon name="empty" useTheme="true" />
              <p>抱歉，暂无“{{ keyword }}”结果</p>
            </div>
            <GuessSonglist
              :pageSize="currentSize == 'small' ? 5 : currentSize == 'medium' ? 6 : 9"
              :renderType="currentSize == 'small' ? 'list' : 'block'"
              :class="`guess-${currentSize}`"
              from="search"
            />
          </div>
        </div>
      </div>
      <div v-else class="init">
        <SearchHistory @clickWord="handleChangeInput" from="search" />
        <GuessSonglist
          :showAllData="true"
          :pageSize="currentSize == 'small' ? 5 : currentSize == 'medium' ? 6 : 9"
          :renderType="currentSize == 'small' ? 'list' : 'block'"
          :class="`guess-${currentSize}`"
          from="search"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch, computed, onMounted, nextTick, onActivated } from 'vue'
import { useStore } from 'vuex'
import SongItem from '@/components/song-item/index.vue'
import SingerItem from '@/components/singer-item/index.vue'
import SearchHistory from '@/components/search-history/index.vue'
import GuessSonglist from '@/components/guess-song/songlist.vue'
import { search } from '@/service/search'
import { useRouter, onBeforeRouteLeave, useRoute } from 'vue-router'
import { sendLog } from '@/directives/v-log/log'
import { setSearchCache } from '@/utils/historyCache'
import { checkLandscapeOrPortrait } from '@/utils/device'
import Toast from '@/utils/toast'
import { TSVoiceInstance } from '@/packages/TSJsbridge';
import eventBus from '@/utils/event-bus'
import useSongItem from '@/composables/useSongItem'

export default {
  name: 'Search',
  components: {
    SongItem,
    SingerItem,
    GuessSonglist,
    SearchHistory,
  },
  activated() {
    const store = useStore()
    const route = useRoute()
    const { isUsed, position } = store.state.pageCacheData.search

    if (!isUsed) {
      this.handleInitData()
      this.handleClearInput()
      
      if (route.query.keyword) return
      console.log('search activated')
      this.handleFocusInput() //只要进入就锁定
    } else {
      if (this.$refs.loadMoreRef) {
        this.$refs.loadMoreRef.root.scrollTop = position
      }
      this.handleUpdateCachePosition(false, 0)
    }

    sendLog({
      event_type: '10000~50000',
      event_name: 10019,
      event_data: {
        str1: '搜索页',
        str2: '搜索页',
        str3: '进入搜索页',
        str4: 'show',
      },
    })
  },
  methods: {
    handleClearInput() {
      this.$refs.searchRef.handleClearInput()
    },
    handleFocusInput() {
      this.$refs.searchRef.handleFocusInput()
    },
    handleChangeInput(e) {
      console.log('search word')
      sendLog({
        event_type: '10000~50000',
        event_name: 10020,
        event_data: {
          str1: '搜索页',
          str2: '搜索历史',
          str3: '点击歌曲',
          str4: 'click',
        },
      })
      this.handleChangeKeyword(e)
      this.$refs.searchRef.handleChangeKeyword(e)
    },
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const { orderSong } = useSongItem()

    const logData = ref({
      vipLog: {
        str1: '搜索',
        str2: '搜索结果-歌曲',
        str3: '点击 VIP 歌曲',
      },
      fr: [1824, 1825],
    })

    const isLogin = computed(() => !!store.state.userInfo.unionid)
    const searchCacheList = computed(() => store.state.search.searchCache)
    const currentSize = computed(() => store.state.currentSize);

    const searchRef = ref(null)
    let curSearchTab = ref('song')
    let loadMoreRef = ref(null)
    let keyword = ref('')
    let isEmpty = ref(false)
    let resultData = ref({
      singer: [],
      song: [],
    })
    let paginationNumberRecord = {
      singer: 1,
      song: 1,
    }
    let isRequest = false
    let isShowVipActEnter = ref(false)
    let isInit = ref(true)
    let from = ref('')

    const themeClass = computed(() => store.state.themeClass)

    const handleInitData = () => {
      curSearchTab.value = 'song'
      keyword.value = ''
      resultData.value.singer = []
      resultData.value.song = []
    }

    const handleSwitchSearchTab = (tab) => {
      curSearchTab.value = tab
      isInit.value = true
      // TODO
      if (!resultData.value[tab].length) {
        searchHandler[tab].call()
      }
    }
    const handleOnSearch = (k) => {
      sendLog({
        event_type: 'click',
        event_name: '6002',
        event_data: {
          str1: '搜索页',
          str2: isInit.value ? '搜索栏' : '搜索结果页',
          str3: '搜索button',
          str4: 'click',
        }
      })
      if (!k.trim()) {
        Toast('请输入搜索内容')
        sendLog({
          event_type: 'click',
          event_name: '6002',
          event_data: {
            str1: '搜索页',
            str2: '搜索栏',
            str3: '空词搜索弹窗',
            str4: 'click',
          }
        })
        return
      }
      keyword.value = k
      searchRef.value.handleBlurInput()
      console.log('handleOnSearch', k)
    }

    const handleClickSinger = ({ singer, singerhead, singerid }) => {
      router.push({
        name: 'songList',
        query: {
          name: singer,
          image: singerhead,
          singerid,
          from: '2',
        },
      })
    }

    const getSearchReportStatus = (res) => {
      if (res.errcode !== 200) {
        return 12
      }
      return res.singer.length || res.song.length ? 10 : 11
    }

    const searchHandler = {
      async singer() {
        await handleSearch('singer');
      },
      async song() {
        await handleSearch('song');
      }
    };

    async function handleSearch(type) {
      try {
        if (paginationNumberRecord[type] === 1 && resultData.value[type].length) {
          paginationNumberRecord[type]++;
        }

        const responseData = await search(keyword.value, paginationNumberRecord[type], type);
        const status = getSearchReportStatus(responseData);
        reportSearchStatus('initial', status, responseData);

        handleDataMerge(type, responseData);

        if (type === 'song' && from.value === 'voiceControl' && paginationNumberRecord.song <= 2) {
          if (resultData.value.song.length === 1) {
            orderSong(resultData.value.song[0], {
              immediate: false,
              from: '语音',
            })
            TSVoiceInstance.sendTtsMessage(0, '', '');
            return
          }
          const message = resultData.value.song.length ? '选择一个你想唱的吧' : '未找到相关内容';
          TSVoiceInstance.sendTtsMessage(0, 1001, message);
        }

        resetFlags();

        reportSearchStatus('final', 10021);
      } catch (error) {
        console.log(`Error in ${type} function:`, error);
        reportSearchStatus('error', 12);
      }
    }

    function handleDataMerge(type, responseData) {
      if (paginationNumberRecord[type] === 1 && !resultData.value[otherTypeMap[type]].length) {
        resultData.value[otherTypeMap[type]] = [
          ...resultData.value[otherTypeMap[type]],
          ...responseData[otherTypeMap[type]]
        ];
      }

      if (responseData[type].length) {
        resultData.value[type] = [...resultData.value[type], ...responseData[type]];
        paginationNumberRecord[type]++;
      }
    }

    function reportSearchStatus(phase, status, responseData) {
      const eventData = {
        key_words: keyword.value,
        status: status || getSearchReportStatus(responseData)
      };

      sendLog({
        event_type: phase === 'error' ? 'click' : '10000~50000',
        event_name: phase === 'error' ? 122 : 10021,
        event_data: phase === 'error' ? eventData : {
          str1: '搜索页',
          str2: '搜索栏',
          str3: '点击搜索',
          str4: 'click'
        }
      });
    }

    function resetFlags() {
      from.value = '';
      isRequest = false;
      isInit.value = false;
    }

    const otherTypeMap = { singer: 'song', song: 'singer' };

    const getSearchResult = async () => {
      if (isRequest) {
        return
      }
      isRequest = true
      console.log('getSearchResult', curSearchTab.value)
      searchHandler[curSearchTab.value].call()
    }

    const handleChangeKeyword = (e) => {
      if (e) keyword.value = e
    }

    const setSearchCacheList = (k) => {
      // 存储搜索历史
      const keyword = k.trim()
      let newSearchCacheList = [keyword, ...searchCacheList.value.filter(item => item !== keyword)]
      newSearchCacheList = newSearchCacheList.slice(0, 10)
      store.dispatch('search/updateSearchCache', newSearchCacheList)
      setSearchCache(newSearchCacheList)
    }

    const handleUpdateCachePosition = (u, v) => {
      store.commit('UPDATE_PAGE_CACHEDATA', {
        data: {
          isUsed: u,
          position: v,
        },
        type: 'search',
      })
    }
    
    const fetchInitData = () => {
      isInit.value = true
      resultData.value = {
        singer: [],
        song: [],
      }
      paginationNumberRecord = {
        singer: 1,
        song: 1,
      }
      isRequest = false
      getSearchResult()
    }

    // const onVoiceSearchSame = () => {
    //   if (resultData.value.song.length === 1) {
    //     orderSong(resultData.value.song[0], {
    //       immediate: false,
    //       from: '语音',
    //     })
    //     TSVoiceInstance.sendTtsMessage(0, '', '');
    //     return
    //   }
    //   const message = resultData.value.song.length ? '选择一个你想唱的吧' : '未找到相关内容';
    //   TSVoiceInstance.sendTtsMessage(0, 1001, message);
    //   console.log('语音搜索内容一2致' + new Date(), 0, 1001, message)
    // }

    const handleBack = () => {
      keyword.value = ''
    }

    onMounted(() => {
      // eventBus.on('voiceSearchSame', onVoiceSearchSame)
      if (checkLandscapeOrPortrait() === 'landscape') {
        isShowVipActEnter.value = true
      }

      from.value = route.query.from
    })

    onActivated(() => {
      sendLog({
        event_type: 'show',
        event_name: '6002',
        event_data: {
          str1: '搜索页',
          str2: '搜索页',
          str3: '进入搜索页',
          str4: 'show',
        }
      })
    })

    watch(keyword, (k) => {
      console.log('watch keyword', k)
      if (k) {
        fetchInitData()
        setSearchCacheList(k)
      }
    })

    watch(() => route.query.keyword, async (k) => {
      await nextTick()
      if (k) {
        keyword.value = k
        searchRef.value.keyword = k
        from.value = route.query.from
        await nextTick()
        searchRef.value.handleBlurInput()
      }
    }, {
      immediate: true,
      deep: true, 
    })

    onBeforeRouteLeave((to, from, next) => {
      if (to.name === 'songList') {
        const position = loadMoreRef.value
          ? loadMoreRef.value.root.scrollTop
          : 0
        handleUpdateCachePosition(true, position)
      }
      next()
    })

    return {
      isLogin,
      isEmpty,
      curSearchTab,
      resultData,
      keyword,
      loadMoreRef,
      isShowVipActEnter,
      handleSwitchSearchTab,
      handleOnSearch,
      handleClickSinger,
      handleChangeKeyword,
      setSearchCacheList,
      getSearchResult,
      handleInitData,
      handleUpdateCachePosition,
      // imgs,
      themeClass,
      searchRef,
      isInit,
      currentSize,
      handleBack,
      logData,
    }
  },
}
</script>

<style lang="stylus" scoped>
.search
  height 100vh
  overflow hidden
  .scroll-area
    padding 0 calc(120px * var(--scale))

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      padding 0 calc(64px * var(--scale)) !important

  .init
    width 100%
    flex 1
    overflow-y scroll
    padding-bottom 200px
    :deep(.left)
      &:active
        &:after
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)   
            height calc(85px * var(--scale)) !important
  .song-list
    padding 0px
    height 100%
    .loadmore
      padding-top 0px
    .tabs
      padding 0
      
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        margin-top calc(24px * var(--scale)) !important
      .item:first-child
        margin-right calc(213px * var(--scale))

        // 三分之一屏
        @media (max-width: 700px)
          margin-right calc(64px * var(--scale))


    .song-list-content
      flex 1
      overflow hidden
      padding-top 0px
      padding-bottom 0px
      .loadmore
        padding-bottom 200px !important
    .empty
      overflow-y scroll
  .singer-list
    flex 1
    margin 0 auto
    text-align center
    display grid
    grid-template-columns repeat(6, 216px)
    justify-content space-between
    padding-top 56px !important

    // 三分之一屏
    @media (max-width: 700px)
      grid-template-columns repeat(3, calc(100px * 3))
      padding-left calc(5px * 3)
      padding-right calc(5px * 3)
      padding-top calc(26px * 3) !important

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      grid-template-columns repeat(4, calc(144px * var(--scale))) !important
      

  .empty-wrapper
    overflow-y scroll
    height 100%
    padding-bottom 200px
  .empty
    padding 152px 0
  .hint
    text-align center
    color #555555
  
  .guess-medium
    :deep(.sec-gusse-sing-list)
      gap: calc(20px * var(--scale))
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        gap: calc(20px * var(--scale)) !important
</style>

