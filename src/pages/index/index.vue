<template>
  <div
    class="refresh-container"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    id="home"
  >
    <div
      class="page home"
      :style="{
        'pointer-events': pageCanpoint ? 'auto' : 'none'
      }"
    >
      <div class="header-container">
        <SearchBar
          :isShowBack="false"
          :isShowLogo="true"
          :isShowToSearch="true"
          v-log="{
            event_type: 'click',
            event_name: '6001',
            event_data: {
              str1: '首页',
              str2: '搜索栏',
              str3: '搜索栏',
              str4: 'click',
            }
          }"
        ></SearchBar>

        <!-- 在大屏幕上，Tips 组件位于 SearchBar 右侧 -->
        <TipsMessage 
          v-if="currentSize === 'full'"
          :user-status="userLoginStatus"
          :screen-size="currentSize"
          @show-login-modal="showLoginModal"
          @show-payment-modal="showPaymentModal"
          @show-toast="showToast"
          class="tips-large"
          :enable-scroll="true"
        />
      </div>

      <!-- 在中小屏幕上，Tips 组件位于顶栏下方 -->
      
      
      <div
        class="scroll-area"
        ref="scrollArea"
      >

        <div class="tips-container">
          <TipsMessage 
            v-if="currentSize !== 'full'"
            :user-status="userLoginStatus"
            :screen-size="currentSize"
            @show-login-modal="showLoginModal"
            @show-payment-modal="showPaymentModal"
            @show-toast="showToast"
            class="tips-small"
            :enable-scroll="true"
          />
        </div>

        <NavList
          @click-nav="handleClickNav"
          :key="refreshKey"
        ></NavList>
        
        <template v-if="!isShowOffline">
          <GuessSonglist
            :pageSize="currentSize == 'small' ? 5 : currentSize == 'medium' ? 6 : 9"
            :renderType="currentSize == 'small' ? 'list' : 'block2'"
            :class="currentSize == 'small' ? 'guess-small' : 'home-guess'"
            from="home"
          />
          <div ref="guessList"></div>

          <!-- 麦克风运营位 -->
          <!-- <div class="mic-operation" @click="handleClickOperation">
            <img v-show="currentSize != 'small'" src="https://qncweb.ktvsky.com/20241204/vadd/3a7590b3339c956291e0802ecc6db6a8.png" alt="">
            <img v-show="currentSize == 'small'" src="https://qncweb.ktvsky.com/20250108/other/1945f6881b5a26dca27ea188b6855a97.png" alt="">
          </div> -->
          <div class="mic-operation"></div>

          <div class="sheet-list flex-column" id="sheet-scroll">
            <div class="sheet-list-tab global-tab" id="refTab" ref="refTab">
              <div class="sheet-list-tab-item item"
                v-for="(item, index) in rankList"
                :key="item.id"
                @click="handleChangeTab(item, index)"
                :id="'tab_' + item.id"
              >
                <span v-if="index != 0"></span>
                <div class="sheet-list-tab-item-txt item-txt" :class="{'active':curTab.id == item.id}">{{ item.name }}</div>
              </div>
            </div>
            <div class="sheet-list-content" ref="sheetListContent">
              <div v-if="curTab.image && currentSize != 'small'" class="sheet-list-content-left">
                <img :src="curTab.image" v-img-fallback="imgFallback">
                <p>{{ curTab.name }}</p>
              </div>
              <div class="sheet-list-content-right" @click.stop>
                <LoadMore
                  v-if="dataList.length"
                  class="song-list"
                  @load-more="fetchData"
                  :isEnd="isEnd"
                  :style="scrollStyle"
                >
                  <div
                    v-if="curTab.image && currentSize == 'small'"
                    class="sheet-list-content-left flex-column"
                  >
                    <div class="cover">
                      <img :src="curTab.image" v-img-fallback="imgFallback">
                    </div>
                    
                    <p>{{ curTab.name }}</p>
                  </div>
                  <SongItem
                    className="sheet-list-song-item"
                    v-for="(songItem, index) in dataList"
                    :key="index"
                    :songItem="songItem"
                    :index="index"
                    :order-log="orderLog"
                    :logData="gedanLogData"
                  />
                  <!-- <p class="bot-hint" v-if="isEnd">到底啦～</p> -->
                </LoadMore>
                <!-- <div v-if="!isRequest && !dataList.length"  class="empty">
                  暂无歌曲
                </div> -->
              </div>
            </div>
          </div>
        </template>
        
        <Offline v-if="isShowOffline" @click-retry="handleRetry"/>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, computed, onActivated, watch, defineAsyncComponent, nextTick, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import Toast from '@/utils/toast'
import useLoginValidComposables from '@/composables/useLoginValid'
import eventBus from '@/utils/event-bus'
import { getHotSongList } from '@/service/hotSongList'
import { getPlaylist, getPlaylistDetail } from '@/service/playlist'
import { sendLog } from '@/directives/v-log/log'
import _ from 'lodash'
import { TSBaseInfoInstance, TSNativeInstance } from '@/packages/TSJsbridge'
import NavList from '@/components/nav-list/index.vue'
import TipsMessage from '@/components/message-tips/index.vue'
import useLoginValid from '@/composables/useLoginValid'
import useVip from '@/composables/useVip'
import store2 from 'store2'
// import { days520Songs } from '@/constants/songs/days520Songs'
import { graduteSongs } from '@/constants/songs/biye'
import { cantoneseSongs } from '@/constants/songs/cantoneseSongs'

// 组件定义
const SongItem = defineAsyncComponent(() => import('@/components/song-item/index.vue'))
const GuessSonglist = defineAsyncComponent(() => import('@/components/guess-song/songlist.vue'))
const SplashScreenModal = defineAsyncComponent(() => import('@/components/modal/global/splash-screen/index.vue'))
// const YSTip = defineAsyncComponent(() => import('@/components/ys-tip/index.vue'))

let splashScreenConstance = null

// 使用 Vue 3 的组合式 API
const router = useRouter()
const store = useStore()
const { checkUserLoginStatus } = useLoginValidComposables()
const {showLoginQrcode, isLogin } = useLoginValid()
const { showVipQrcode } = useVip()

// 计算属性
const unionid = computed(() => store.state.userInfo.unionid)
const themeClass = computed(() => store.state.themeClass)
const isYSTipAccept = computed(() => store.state.storageYSTipAccept)
const net_status = computed(() => store.state.base.net_status)
const currentSize = computed(() => store.state.currentSize);
const scrollStyle = computed(() => {
  return {
    'overflow-y': isCanScrollSheet.value ? 'scroll' : 'hidden',
  }
});

// const local520Songs = days520Songs()

// 响应式状态
// const fixSongSheet = ref(false)
const isShowOffline = ref(false)
const pageCanpoint = ref(isYSTipAccept.value)
const splashScreenRef = ref(null)
const gedanLogData = {
  vipLog: {
    str1: '首页',
    str2: '歌单',
    str3: '点击 VIP 歌曲',
  },
  fr: [1822, 1823],
}

let startY = 0
let startScrollTop = 0
let observerMV = null
const scrollArea = ref(null)

const curTab = ref({ id: 0, name: '', img: '' })
let p = 1
const version = ref({ current: '', latest: '' })
const isRequest = ref(false)
const isEnd = ref(false)
const rankList = ref([])
const dataList = ref([])
const refTab = ref(null)
const refreshKey = ref(0)
const isCanScrollSheet = ref(false)
const sheetListContent  = ref(null)
const guessList = ref(null)

const imgs = {
  themeDark: { imgFallback: { loading: require('@/assets/cover-dark.png'), error: require('@/assets/cover-dark.png') } },
  themeLight: { imgFallback: { loading: require('@/assets/cover-light.png'), error: require('@/assets/cover-light.png') } },
  themeSystem: { imgFallback: { loading: require('@/assets/cover-dark.png'), error: require('@/assets/cover-dark.png') } },
}

const imgFallback = computed(() => imgs[themeClass.value].imgFallback)
const orderLog = computed(() => {
  return {
    'str1': '首页',
    'str2': '歌单',
    'str7': curTab.value.id,
  }
})

// 用户登录状态
const userLoginStatus = computed(() => {
  const isLoggedIn = store.state.userInfo && store.state.userInfo.unionid
  const isVIP = store.state.userInfo && store.state.vipInfo.end_time
  const isExpiredVIP = store.state.userInfo && store.state.vipInfo.expire
  
  if (!isLoggedIn) {
    return 'notLoggedIn'
  } else if (isVIP) {
    return 'VIP'
  } else if (isExpiredVIP) {
    return 'expiredVIP'
  }
  return 'nonVIP'
})

// Tips 组件相关方法
const showLoginModal = () => {
  showLoginQrcode({
    log: '首页-顶栏-tips'
  });
  sendLog({
    event_type: 'click',
    event_name: '1021',
    event_data: {
      str1: '首页',
      str2: '顶部小喇叭',
      str3: '点击',
      str4: 'click',
      str5: !isLogin.value ? '未登录' : '已登录',
      str9: !isLogin.value ? '手机端' : '车机端',
      str10:!isLogin.value? '1856' : '1857',
    },
  })
}

const showPaymentModal = () => {
  showVipQrcode({
    fr: 1857,
    logData: {
      str1: '首页',
      str2: '顶部小喇叭',
      str3: '点击',
      str4: 'click',
    },
    isLogin: true
  })
  sendLog({
    event_type: 'click',
    event_name: '6001',
    event_data: {
      str1: '首页',
      str2: '顶部小喇叭',
      str3: '点击',
      str4: 'click',
      str5: !isLogin.value ? '未登录' : '已登录',
      str9: !isLogin.value ? '手机端' : '车机端',
      str10:!isLogin.value? '1856' : '1857',
    },
  })
}

const showToast = (message) => {
  Toast(message)
  sendLog({
    event_type: 'click',
    event_name: '6001',
    event_data: {
      str1: '首页',
      str2: '顶部小喇叭',
      str3: '点击',
      str4: 'click',
    },
  })
}

// 方法定义
const handleClickNav = (nav) => {
  if (nav.isCheckLogin && !checkUserLoginStatus()) return
  if (!nav.isSupport) {
    Toast('功能尚未完成迁移')
    return
  }
  if (nav.type === 'page') {
    if (nav.pathName === 'mine') store.dispatch('getCarplayInfo')
    router.push({ name: nav.pathName })
    return
  }
  if (nav.type === 'emit') eventBus.emit(nav.emit)
}

const handleChangeTab = (tab, index) => {
  if (curTab.value.id === tab.id) return

  sendLog({
    event_type: 'click',
    event_name: '6001',
    event_data: {
      str1: '首页',
      str2: '歌单',
      str3: '点击任意歌单列表',
      str4: 'click',
      str5: tab.id,
      str6: index + 1 || '',
    },
  });

  sendLog({
    event_type: 'show',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: '歌单',
      str3: '任意歌单列表展现',
      str4: 'show',
      str5: tab.id || '',
      str6: index + 1 || '',
    },
  })

  // sendLog({
  //   event_type: '10000~50000',
  //   event_name: 10065,
  //   event_data: { str1: '首页', str2: '歌单tab', str3: '切换歌单', str4: 'click', str5: tab.name || '', str7: tab.id || '' },
  // })
  curTab.value = tab
  dataList.value = []
  p = 1
  version.value = { current: '', latest: '' }
  isRequest.value = false
  isEnd.value = false
  fetchData()

  const element = document.getElementById(`tab_${tab.id}`)
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'center'
    });
  }
}

const insertPlayList = (list) => {
  let newList = [
    // {
    //   "id": -1,
    //   "name": "520情歌对唱", // 💓
    //   "image": "https://qncweb.ktvsky.com/20250519/vadd/49c510a5f75cb69218675e068f58380b.jpg"
    // },
    {
      "id": -2,
      "name": "毕业季｜青春主打歌",
      "image": "https://qncweb.ktvsky.com/20250616/vadd/8ca12735ed4222b37397ca136de1695a.jpg"
    },
    {
      "id": -1,
      "name": "粤唱粤倾心",
      "image": "https://qncweb.ktvsky.com/20250616/vadd/8bd510ce9c62d6cf5e97c61519c11e6d.png"
    },
  ]
  
  return newList.concat(list)
}

const initRankList = async () => {
  try {
    const res = await getPlaylist()
    if (res.length) {
      rankList.value = res.filter((v, i) => i < 8)

      rankList.value = insertPlayList(rankList.value)

      curTab.value = rankList.value[0]
      fetchData()
    }
  } catch (error) {
    console.error('initRankList error', error)
  }
}

const requestBussinessData = async () => {
  const responseData = await getPlaylistDetail({ p, type: curTab.value.name, version: version.value.latest })
  return responseData
}

const requestHotSongData = async () => {
  const res = await getHotSongList({ p, unionid: unionid.value, version: version.value.latest })
  if (res.data.length) {
    if (p === 1 && res.version) version.value = res.version
    dataList.value = dataList.value.concat(res.data).filter((v, i) => i < 100)
    p++
  }
}

const fetchData = async () => {
  if (isRequest.value) return
  isRequest.value = true

  try {
    // if (curTab.value.name === '520情歌对唱') {
    //     if (!dataList.value.length) {
    //       dataList.value = local520Songs
    //     }
    //     isEnd.value = true
    //     isRequest.value = false
    //     return
    //   }

    if (curTab.value.name === '粤唱粤倾心') {
      if (!dataList.value.length) {
        dataList.value = cantoneseSongs
      }
      isRequest.value = false
      isEnd.value = true
      return
    }

    if (curTab.value.name === '毕业季｜青春主打歌') {
      if (!dataList.value.length) {
        dataList.value = graduteSongs
      }
      isRequest.value = false
      isEnd.value = true
      return
    }

    if (curTab.value.name === '热门歌曲，总有一首你会唱') {
      if (!dataList.value.length) await requestHotSongData()
    } else {
      const bussinessResponseData = await requestBussinessData()
      if (bussinessResponseData.data.length === 0) {
        isEnd.value = true
        console.log(bussinessResponseData.data)
      } else {
        isEnd.value = false
        if (p === 1 && bussinessResponseData.version) version.value = bussinessResponseData.version
        dataList.value = dataList.value.concat(bussinessResponseData.data)
        p++
      }
    }
  } catch (error) {
    console.error(error)
  } finally {
    isRequest.value = false
  }
}

// const handleClickOperation = () => {
//   $useMicOnline.show({ info: micMallInfo.value, title: micMallInfo.value.title })
//   sendLog({
//     event_type: '10000~50000',
//     event_name: 30010,
//     event_data: { str1: '运营横条', str2: '运营横条', str3: 'click' },
//   })
// }

const handleTouchStart = (event) => {
  startY = event.touches[0].clientY
  startScrollTop = scrollArea.value.scrollTop
}

const handleTouchEnd = (event) => {
  handleRefresh(event)
}

const handleRefresh = _.debounce(async (event) => {
  // const endY = event.changedTouches[0].clientY
  // const distance = endY - startY
  // if (distance > 300 && startScrollTop === 0) {
  //   try {
  //     console.log('开始刷新')
  //     await router.replace({ path: route.path, query: { ...route.query, t: Date.now() } })
  //     handleRetry()
  //     refreshKey.value++
  //     console.log('下拉刷新成功')
  //     Toast('刷新页面成功')
  //   } catch (error) {
  //     console.log('下拉刷新error', error)
  //   }
  // }
}, 300)

const handleRetry = async () => {
  try {
    store.commit('base/SET_NET_LOADING', true)

    await Promise.all([
      initRankList(),
      requestHotSongData()
    ])
    await store.dispatch('oftenSing/initOftenSingList', unionid.value)

    store.commit('base/SET_NET_LOADING', false)
  } catch (error) {
    store.commit('base/SET_NET_LOADING', false)
  } finally {
    store.commit('base/SET_NET_LOADING', false)
  }
}

// 检查是否需要显示开屏弹窗（每个弹窗每天仅展示一次）
const checkSplashScreen = () => {
  if (!isYSTipAccept.value) return
  const today = new Date().toDateString()
  const lastShowFirstDate = store2.get('splashScreenFirstLastShow')
  const lastShowSecondDate = store2.get('splashScreenSecondLastShow')
  
  // 导入弹窗创建模块
  const showSplashScreen = (type) => {
    nextTick(() => {
      import('@/components/modal/global/splash-screen/create.js').then(module => {
        const useSplashScreen = module.default
        
        // 创建弹窗实例并传入关闭事件回调
        const splashScreen = useSplashScreen({
          onClose: (closedType) => {
            console.log(`${closedType || type} splash screen closed`)
          }
        })
        
        // 显示指定类型的弹窗
        splashScreenConstance = splashScreen.show({
          splashType: type
        })
        
        // 记录当前弹窗类型已在今天展示过
        if (type === 'first') {
          store2.set('splashScreenFirstLastShow', today)
        } else {
          store2.set('splashScreenSecondLastShow', today)
        }
      })
    })
  }
  
  // 判断展示逻辑：
  // 1. 如果第一个弹窗今天没展示过，则展示第一个
  // 2. 如果第一个已展示但第二个没展示过，则展示第二个
  // 3. 如果两个都展示过，则不展示
  if (lastShowFirstDate !== today) {
    showSplashScreen('first')
  } else if (lastShowSecondDate !== today) {
    showSplashScreen('second')
  }
}

// 生命周期钩子
onMounted(async () => {
  initRankList()
  setTimeout(() => {
    TSBaseInfoInstance.notifyHomePageLoadCompleted()
    TSNativeInstance.showRefreshBtn()
  }, 200)
  
  // 检查是否需要显示开屏弹窗
  checkSplashScreen()

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        sendLog({
          event_type: 'show',
          event_name: '6001',
          event_data: {
            str1: '首页',
            str2: '歌单',
            str3: '任意歌单列表展现',
            str4: 'show',
          },
        });
      }
    });
  });

  if (sheetListContent.value) {
    observer.observe(sheetListContent.value);
  }

  await nextTick()

  eventBus.emit('show-mobile-order')
  
  const mvMiniElement = document.getElementById('mvMini');
  observerMV = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        console.log('mv-mini is visible');
        store.commit('SET_MINI_VISIBLE', true)
      } else {
        console.log('mv-mini is not visible');
        store.commit('SET_MINI_VISIBLE', false)
      }
    });
  });
  if (mvMiniElement) {
    observerMV.observe(mvMiniElement);
  }

  const observerGuess = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      console.log(entry.isIntersecting, '0120')
      isCanScrollSheet.value = !entry.isIntersecting

      console.log(isCanScrollSheet.value ? '可以滑动' : '禁止滑动')
    });
  });

  if (guessList.value) {
    observerGuess.observe(guessList.value);
  }

  const initObserver = () => {
    const mvMiniElement = document.getElementById('mvMini');
    if (mvMiniElement && observerMV) {
      observerMV.disconnect();
      observerMV.observe(mvMiniElement);
    }
  }

  initObserver();

  // 清理观察者
  return () => {
    if (sheetListContent.value) {
      observer.unobserve(sheetListContent.value);
    }
    if (mvMiniElement) {
      observerMV.unobserve(mvMiniElement);
    }
    if (observerGuess) {
      observerMV.unobserve(observerGuess);
    }
  };
})

onActivated(() => {
  const mvMiniElement = document.getElementById('mvMini');
  if (mvMiniElement && observerMV) {
    observerMV.disconnect();
    observerMV.observe(mvMiniElement);
  }

  sendLog({
    event_type: 'show',
    event_name: 6001,
    event_data: {
      str1: '首页',
      str2: '首页',
      str3: '进入首页',
      str4: 'show'
    }
  })
})

watch([net_status, rankList, dataList], () => {
  if (!net_status.value && rankList.value.length === 0 && dataList.value.length === 0) {
    isShowOffline.value = true
  } else {
    isShowOffline.value = false
  }
}, { deep: true, immediate: true })

watch(isYSTipAccept, (newValue) => {
  if (newValue) {
    setTimeout(() => {
      pageCanpoint.value = true
      checkSplashScreen()
    }, 500)
  } else {
    pageCanpoint.value = false
  }
})

watch(isLogin, (newValue) => {
  if (newValue && splashScreenConstance) {
    splashScreenConstance.hide()
    splashScreenConstance = null
  }
})

watch(currentSize, async (newValue) => {
  if (newValue !== 'small') {
    await nextTick()
    const mvMiniElement = document.getElementById('mvMini');
    if (mvMiniElement) {
      observerMV.observe(mvMiniElement);
    }
  }
});

onBeforeUnmount(() => {
  if (observerMV) {
    observerMV.disconnect();
  }
});
</script>

<style lang="stylus" scoped>
.home
  display flex
  flex-direction column
  height 100vh
  overflow hidden
  height 100vh
  overflow-y scroll
  padding-top 0px !important
  padding-bottom 0px !important
  padding 0px !important

  .header-container
    // display flex
    // align-items center
    width 100%
    background var(--background-primary)
    position relative
    
    @media (max-width: 900px)
      flex-direction column
      align-items flex-start
    
    .tips-large
      max-width 30vw
      position absolute
      bottom 2vw
      left 16vw !important
      // transform scale(1.3)
  
  .tips-container
    width 100%
    margin 0
    display flex
    align-items center
    justify-content center
    padding 0 64px
    padding-bottom 20px
    background var(--background-primary) !important
    position static  /* 确保不是固定定位 */
    z-index auto     /* 重置可能的高 z-index */

    @media (max-width: 900px) and (min-width: 701px)
      padding-right calc(32px * var(--scale)) !important
      padding-left calc(32px * var(--scale)) !important
      padding-bottom 2vw !important
    .tips-small
      margin 0
      padding-left 3vw !important
      position static  /* 确保组件本身也不是固定定位 */
      span
        font-size var(--font-size-medium) !important

  .scroll-area
    background none
    padding 0px !important
    /* 确保滚动区域包含 tips-container */
    /* 如果需要，可以调整这里的样式 */

    // 三分之一屏
    @media (max-width: 700px)
      background var(--background-primary)

    & > *:not(.nav-list-wrapper):not(.nav-list-wrapper-medium)
      background var(--background-primary) !important
      padding var(--padding-base)

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        padding-right calc(32px * var(--scale)) !important
        padding-left calc(32px * var(--scale)) !important
  .search-bar
    background var(--background-primary)
  // .home-guess
  //   // // 三分之二屏
  //   // @media (max-width: 900px) and (min-width: 701px)  
  //   //   padding-top calc(32px * var(--scale)) !important
  //   //   :deep(.song-item)
  //   //     .left
  //   //       &:active
  //   //         &:after
  //   //           width calc(360px * 1.48) !important
  //   //           height calc(75px * 1.48) !important
  //   //           margin 0 10px 0 10px !important

  //   // // 三分之一屏
  //   // @media (max-width: 700px)
  //   //   :deep(.section-container-header)
  //   //     margin-top calc(24px * 3)
  //   // //     color var(--text-primary-color)
  .sec-gusse-sing
    margin-bottom 0px
  .mic-operation
    margin 0px
    width 100%
    height calc(24px * var(--scale))
    border-radius: 0px;
    padding-top 60px
    padding-bottom 0px

    // 三分之一屏
    @media (max-width: 700px)
      padding-top calc(8px * 3)
      padding-bottom calc(16px * 3)
      height calc(16px * 3)
  .sheet-list
    width 100%
    height 100% !important
    height auto
    display flex
    flex-direction column
    position relative
    margin-bottom 0px
    overflow hidden
    &-tab
      margin-bottom 36px
      margin-top 0px
      // // 三分之二屏
      // @media (max-width: 900px) and (min-width: 701px)  
      //   margin-top calc(24px * 1.48) !important
      *
        font-size var(--font-size-extra-large)

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          font-size var(--font-size-large)
    &-content
      width 100%
      display flex
      align-items flex-start
      overflow hidden
      flex 1
      overflow hidden
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        margin-bottom calc(50px * 1.48) !important
      &-left
        width 360px
        margin-left 0
        margin-top 48px
        margin-right 108px
        margin-left 108px
        
        .cover
          width 360px
          aspect-ratio: 1 / 1; /* 1:1 的宽高比，高度等于宽度 */
          height auto
        
        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(240px * 1.48) !important
          margin-left 0 !important
          .cover
            width calc(240px * 1.48) !important

        img
          width 100%
          height auto
          margin-top 0px
          border-radius 16px
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            border-radius calc(16px * 1.48) !important
        p
          width 100%
          color var(--song-name-color)
          margin-top 20px
          text-align left
          font-size 27px
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)  
            margin-top calc(12px * 1.48) !important
            font-size calc(18px * 1.48) !important
      &-right
        flex 1
        height 100%
        margin 0 auto
        padding-bottom 150px
        overflow hidden
        .song-list
          width 100% !important
          padding-bottom 0px
          padding-right 0
          padding-left 0px
          padding-top 0px
        .empty
          margin-top 10vh
          font-size 28px
          color rgba(255, 255, 255, 0.5)
          text-align center
    
      // 三分之一屏
      @media (max-width: 700px)
        flex-direction column
        align-items center
        &-right
          width 100%
        &-left
          margin calc(20px * 3) auto
          width 100%
          align-items center
          img
            border-radius calc(16px * 3)
            width calc(180px * 3)
          p
            text-align center
            font-size var(--font-size-medium)
            width 100%

    .bot-hint
      font-size 24px
      color #FFF
      opacity 0.5
      text-align center
      margin-top 0px
</style>
