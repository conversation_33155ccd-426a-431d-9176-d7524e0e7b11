<template>
  <div class="page score-page">
    <HeadBar class="headbar" title="评分记录"></HeadBar>
    <LoadMore
      class="score-list"
      v-if="scoreHistoryList.length"
      safeAreaHeight="14.6991vw"
    >
      <ScoreItem
        v-for="(songItem, index) in scoreHistoryList"
        :key="index"
        :songItem="songItem"
        :log-from="{
          song_list_source: 3,
        }"
      />
    </LoadMore>
    <div v-else class="score-empty">
      您现在还没有记录哦～
    </div>
  </div>
</template>

<script>
import { ref, onBeforeMount } from 'vue'
import ScoreItem from '@/components/score-item/index.vue'
import store2 from 'store2'

export default {
  name: 'ScoreResults',
  components: {
    ScoreItem,
  },
  setup() {
    let scoreHistoryList = ref([])

    onBeforeMount(() => {
      let data = store2.get('score-results') || []
      scoreHistoryList.value = data.filter(item => (item.music_name || item.name) && item.level)
    })
    
    return {
      scoreHistoryList,
    }
  }
}
</script>

<style lang="stylus" scoped>
.score-page
  color #FFFFFF66
  ::-webkit-scrollbar
    display none !important
  .score-empty
    margin-top 236px
    background url(https://qncweb.ktvsky.com/20231208/other/8735391456ed4d23857d801ce6c5482b.svg) no-repeat top center
    background-size 90px 90px
    text-align center
    padding-top 130px
    font-size 28px
.page
  height 100vh
  overflow hidden
  background: radial-gradient(322.36% 100% at 50% 0%, #131C24 0%, rgba(19, 28, 36, 0.99) 8.07%, rgba(19, 28, 35, 0.98) 15.54%, rgba(18, 27, 34, 0.95) 22.5%, rgba(18, 26, 33, 0.92) 29.04%, rgba(17, 25, 31, 0.87) 35.26%, rgba(16, 23, 29, 0.82) 41.25%, rgba(14, 21, 27, 0.75) 47.1%, rgba(13, 19, 24, 0.68) 52.9%, rgba(12, 17, 22, 0.60) 58.75%, rgba(10, 15, 18, 0.52) 64.74%, rgba(8, 12, 15, 0.42) 70.96%, rgba(6, 9, 12, 0.33) 77.5%, rgba(4, 6, 8, 0.22) 84.46%, rgba(2, 3, 4, 0.11) 91.93%, #000 100%), #000;
  .headbar
    background: none
.theme-themeLight
  .score-page
    color #1D1D1F66!important
    .score-empty
      background-image url('https://qncweb.ktvsky.com/20240222/other/46fc15d93d419bed654d41b52f253151.png')
</style>

