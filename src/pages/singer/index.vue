<template>
  <div class="page singer-page">
    <SearchBar
      title="全部歌手"
      :isShowToSearch="true"
      :isShowBack="true"
      @on-click-search="onClickSearch"
    />
    <div class="scroll-area flex-column">
      <div class="singer-tab global-tab flex-between">
        <div
          class="item"
          v-for="(item, index) in tabList"
          :key="item"
          @click="handleChangeTab(item, index)"
          :id="'tab_' + index"
          v-log="{
            event_type: 'click',
            event_name: '6005',
            event_data: {
              str1: '歌手列表页',
              str2: '歌手分类',
              str3: item,
              str4: 'click',
            }
          }"
        >
          <div 
            class="item-txt"
            :class="{'active':curTab == item}"
          >
            {{ item }}
          </div>
        </div>
      </div>
      <!-- <div> -->
      <LoadMore
        v-if="singerUnionList.length"
        class="singer-list singer-list-pad-32"
        ref="loadMoreRef"
        @load-more="fetchSingerList"
      >
        <SingerItem
          v-for="(item, index) in singerUnionList"
          :singer="item"
          :key="index"
          @click="handleClickSinger(item)"
          v-log="{
            event_type: 'click',
            event_name: '6005',
            event_data: {
              str1: '歌手列表页',
              str2: '歌手列表',
              str3: '点击歌手',
              str4: 'click',
            }
          }"
        ></SingerItem>
      </LoadMore>
      <!-- <p class="no-data" v-if="isShowEmpty && net_status">抱歉，暂无歌手相关数据</p>
      <p class="hint" v-if="isEmpty && singerUnionList.length > 20">已加载全部</p> -->
      <!-- </div> -->
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, onActivated } from 'vue'
import { getSingerClassList, getSingerList } from '@/service/singer'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { useStore } from 'vuex'
import SingerItem from '@/components/singer-item/index.vue'
import { sendLog } from '@/directives/v-log/log'
import eventBus from '@/utils/event-bus'

const store = useStore()
const router = useRouter()
const route = useRoute()
const tabList = ref([])
let loadMoreRef = ref(null)
let singerList = ref([])
let curTab = ref('')
let p = 1
let version = {
  current: '',
  latest: ''
}
let isEmpty = ref(false)
let isRequest = false
let isShowEmpty = ref(false)

// const net_status = computed(() => store.state.base.net_status);

const tabLogMap = {
  '飙升周榜': 10037,
  '热门歌星': 10038,
  '大陆男歌星': 10039,
  '中国组合': 10040,
  '大陆女歌星': 10041,
  '港台女歌星': 10042,
  '港台男歌星': 10043,
  '外国歌星': 10044,
}

// 歌手列表去重
const singerUnionList = computed(() => {
  const idsMap = new Map();
  for(let i in singerList.value) {
    if(!idsMap.has(singerList.value[i].singerid)) {
      idsMap.set(singerList.value[i].singerid, singerList.value[i])
    }
  }
  return Array.from(idsMap.values())
})

const fetchSingerClassList = async () => {
  tabList.value = await getSingerClassList()
  handleInitTab(tabList.value[0])
}

const fetchSingerList = async () => {
  if (isRequest) {
    console.log('请求正在进行中，无法重复请求');
    return;
  }

  isRequest = true;
  console.log('开始获取歌手列表...', curTab.value);

  try {
    const response = await getSingerList({
      p,
      k: curTab.value.replace('歌星', '歌手'),
      version: version.latest
    });

    console.log('获取到的歌手数据:', response);

    if (response.data.length > 0) {
      if (p === 1 && response.version) {
        version = response.version;
        console.log('更新版本信息:', version);
      }
      singerList.value = [...singerList.value, ...response.data]; // 使用扩展运算符
      console.log('更新后的歌手列表:', singerList.value);
      p++;
    } else if (p === 1) {
      isShowEmpty.value = true;
      console.log('暂无数据，显示空状态');
    }
  } catch (error) {
    console.error('获取歌手列表时出错:', error);
  } finally {
    isRequest = false;
    console.log('请求结束');
  }
}

const handleInitTab = (val) => {
  const params = route.query
  handleChangeTab(params.tab ? params.tab : val)
}

const handleInitData = () => {
  singerList.value = []
  curTab.value = ''
  p = 1
  version = {
    current: '',
    latest: ''
  }
  isEmpty.value = false
  isRequest = false
  fetchSingerClassList()
}

const handleChangeTab = (tab, index) => {
  curTab.value = tab
  isShowEmpty.value = false
  isRequest = false

  const element = document.getElementById(`tab_${index}`);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', inline: 'center' }); // 使用smooth选项可以实现平滑滚动效果
  }

  if (tabLogMap[tab]) {
    sendLog({
      event_type: '10000~50000',
      event_name: tabLogMap[tab],
      event_data: {
        str1: '歌星',
        str2: '类型区',
        str3: tab,
        str4: 'click',
      },
    })
  }
}

const handleClickSinger = ({singername, singerheader, singerid}) => {
  sendLog({
    event_type: '10000~50000',
    event_name: 10045,
    event_data: {
      str1: '歌星',
      str2: '歌手区',
      str3: singername,
      str4: 'click',
    },
  })
  router.push({
    name: 'songList',
    query: {
      name: singername,
      image: singerheader,
      singerid,
    },
  })
}

// const handleUpdateCachePosition = (u, v) => {
//   store.commit(
//     'UPDATE_PAGE_CACHEDATA',
//     {
//       data: {
//         isUsed: u,
//         position: v
//       },
//       type: 'singer'
//     }
//   )
// }

const onClickSearch = () => {
  sendLog({
    event_type: 'click',
    event_name: 6005,
    event_data: {
      str1: '歌手列表页',
      str2: '搜索栏',
      str3: '搜索',
      str4: 'click',
    },
  });
}

watch(curTab, (tab) => {
  if (tab) {
    p = 1
    singerList.value = []
    fetchSingerList()
  }
})

onBeforeUnmount(() => {
  eventBus.off('singer-online', handleInitData)
})

onMounted(() => {
  handleInitData()
  eventBus.on('singer-online', handleInitData)
})

onActivated(() => {
  sendLog({
    event_type: 'show',
    event_name: 6005,
    event_data: {
      str1: '歌手列表页',
      str2: '歌手分类',
      str3: '进入歌手分类页',
      str4: 'show',
    },
  });
})
</script>

<style lang="stylus" scoped>
.page
  min-height 100vh
.singer-page
  height 100vh
  overflow hidden
  padding 0px
  .scroll-area
    padding 0 160px
    overflow hidden
    
.singer  
  &-tab
    width 100%  
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      .item
        margin-right calc(45px * 1.48)

  &-list
    flex 1
    display grid
    text-align center
    grid-template-columns repeat(6, 216px)
    justify-content space-between
    padding-top calc(36px * var(--scale))
    box-sizing border-box

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      grid-template-columns repeat(4, calc(144px * 1.48)) !important
      .common-singer-item
        width calc(144px * var(--scale))

    // 三分之一屏
    @media (max-width: 700px)
      grid-template-columns repeat(3, calc(100px * 3))
      padding-left calc(5px * 3)
      padding-right calc(5px * 3)
      .common-singer-item
        width calc(100px * var(--scale))

.no-data
  font-size 28px
  color rgba(255, 255, 255, 0.5)
  text-align center
  line-height 650px
.hint
  text-align center
  color #555555
</style>
