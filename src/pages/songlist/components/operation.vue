<template>
  <div v-if="!isVip && isSingerDetail" class="singer-detail-operation" @click.stop="handleShowVip">
    <span class="special-feel">小米车主专享</span>
    <span class="special-price">
        VIP低至
        <strong>0.27</strong>
        <span class="special-price-unit">元/天</span>
    </span>
  </div>
</template>

<script setup>
import { computed, defineProps, onMounted } from 'vue'
import { useStore } from 'vuex'
import useVip from '@/composables/useVip'
import { sendLog } from '@/directives/v-log/log'

// Define props
const props = defineProps({
  isSingerDetail: {
    type: Boolean,
    default: false
  }
})

const store = useStore()
const { showVipQrcode } = useVip()

const userInfo = computed(() => store.state.userInfo)
const isLogin = computed(() => !!userInfo.value.unionid)
const isVip = computed(() => store.state.vipInfo?.end_time)
// const isVip = computed(() => {
//   if (store.state.vipInfo?.end_time) {
//     const vipEndDate = new Date(store.state.vipInfo.end_time).getTime()
//     const now = Date.now()
//     return now <= vipEndDate
//   }
//   return false
// })
// const currentSize = computed(() => store.state.currentSize);

const handleShowVip = () => {
//   sendLog({
//     event_type: 'click',
//     event_name: 6001,
//     event_data: {
//       str1: '首页',
//       str2: '底部运营位',
//       str3: '点击',
//       str4: 'click',
//       str5: isLogin.value ? '2' : '1',
//     }
//   })
  showVipQrcode({
    log: '歌星详情-歌曲列表运营位',
    logData: {
      str1: '歌星详情页',
      str2: '运营位',
      str3: '点击',
      str4: 'click',
    },
    isLogin: isLogin.value,
    fr:  isLogin.value ? 1863 : 1862,
  })

}


onMounted(() => {
    // sendLog({
    //   event_type: 'click',
    //   event_name: 6001,
    //   event_data: {
    //     str1: '歌星详情页',
    //     str2: '歌星详情页-运营位',
    //     str3: '展示',
    //     str4: 'show',
    //     // str5: isLogin.value ? '2' : '1',
    //   }
    // })
})

</script>

<style lang="stylus" scoped>
.singer-detail-operation
  display: flex
  justify-content: space-between
  align-items: center
  background: url('https://qncweb.ktvsky.com/20250612/vadd/3895daaedda6d83ba4538ee32b434507.png') no-repeat
  background-size: 100% 100%
  width 1020px // 656px
  height auto // 65px
  margin-left 36px
  .special-feel
    color rgba(0, 0, 0, 1)
    font-size 33px
    font-weight 900
    margin-left 170px
    margin-top 20px

  .special-price
    color rgba(0, 0, 0, 1)
    font-size 33px
    font-weight 400
    margin-right 80px
    margin-top 20px
    &-unit
        font-size 24px
        color #1D1D1F
        font-weight 900
    strong
      color rgba(255, 52, 94, 1)
      font-size 45px
      font-weight 900

  // 三分之二屏
  @media (max-width: 900px) and (min-width: 701px)
    width 1020px

  // 三分之一屏
  @media (max-width: 700px)
    width 1070px
    margin-left 0px
</style>