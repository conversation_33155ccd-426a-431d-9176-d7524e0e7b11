<template>
  <div class="page songlist-page" :class="!listSheet.singerid && 'not-singer'">
    <!-- <SearchBar
      :title="listSheet.name"
      :isShowSearch="false"
      :isShowBack="true"
    /> -->
    <SearchBar
      :title="currentSize === 'small' ? '' : listSheet.name"
      :centerTitle="currentSize === 'small' && !listSheet.singerid ? listSheet.name : ''"
      :isShowSearch="listSheet.singerid"
      :isShowBack="true"
      :enableSearch="true"
      :placeholder="listSheet.singerid ? `搜索${listSheet.name}的歌` : ''"
      :enableBack="isSearchSinger"
      @search="handleSearch"
      @back="handleBack"
    />
    <div class="scroll-area">
      <div class="songlist flex-between">
        <div class="songlist-left">
          <div v-if="currentSize === 'small' && listSheet.singerid" class="songlist-small-cover flex">
            <img :src="listSheet.image" v-img-fallback="imgs[themeClass].imgFallback" :data-update="themeClass" />
            <p>{{ listSheet.name }}</p>
          </div>

          <Operation
            :isSingerDetail="listSheet.singerid"
          />

          <LoadMore
            class="song-list"
            ref="loadMoreRef"
            v-if="dataList.length"
            @load-more="fetchData"
          >
            <div v-if="currentSize === 'small' && !listSheet.singerid" class="songlist-small-cover-gedan">
              <img :src="listSheet.image" v-img-fallback="imgs[themeClass].imgFallback" :data-update="themeClass" />
              <p>{{ listSheet.name }}</p>
            </div>
            <SongItem
              v-for="(songItem, index) in dataList"
              :key="index"
              :songItem="songItem"
              :index="index"
              :order-log="orderLog"
              :logData="logData"
            />
          </LoadMore>
          <div v-else-if="isEmptyVisible" class="empty">
            <DynamicIcon name="empty" useTheme="true" />
            <p>{{ !keyword ? '暂无歌曲' : `抱歉，暂无"${keyword}"的结果` }}</p>
          </div>
        </div>
        <div v-if="currentSize !== 'small'" class="songlist-right">
          <img :src="listSheet.image" v-img-fallback="imgs[themeClass].imgFallback" :data-update="themeClass" />
          <p>{{ listSheet.name }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onBeforeMount, computed, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import SongItem from '@/components/song-item/index.vue'
import Operation from './components/operation.vue'
import { getPlaylistDetail } from '@/service/playlist'
import { searchSinger, searchSingerSong } from '@/service/search';
import { sendLog } from '@/directives/v-log/log'

export default {
  name: 'SongList',
  components: {
    SongItem,
    Operation,
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const store = useStore()

    const loadMoreRef = ref(null)
    const dataList = ref([])
    const singerbg = ref('')
    const singerhead = ref('')
    const isEmptyVisible = ref(false)
    const showOffline = ref(false)
    const isRequest = ref(false)
    const onFirstLoad = ref(true)
    const listSheet = ref({
      name: '',
      image: '',
      singerid: '',
    })
    const version = ref({
      current: '',
      latest: '',
    })
    const songType = ref('')
    let p = 1
    const keyword = ref('')
    const isSearchSinger = ref(false)
    

    const imgs = {
      themeDark: {
        imgFallback: {
          loading: require('@/assets/images/singer-dark.png'),
          error: require('@/assets/images/singer-dark.png')
        },
      },
      themeLight: {
        imgFallback: {
          loading: require('@/assets/images/singer-light.png'),
          error: require('@/assets/images/singer-light.png'),
        },
      },
      themeSystem: {
        imgFallback: {
          loading: require('@/assets/images/singer-dark.png'),
          error: require('@/assets/images/singer-dark.png')
        },
      },
    }

    const themeClass = computed(() => store.state.themeClass)
    const currentSize = computed(() => store.state.currentSize);

    const ponitActionLog = computed(() =>
      listSheet.value.singerid
        ? {
            event_type: '10000~50000',
            event_name: 10045,
            event_data: {
              str1: '歌星',
              str2: '歌手区',
              str3: '进入任意歌手',
              str4: 'click',
            },
          }
        : {}
    )

    const logData = computed(() => {
      const { from, name } = route.query;
      const isGedan = from === 'gedan';
      const isSingingList = name === '唱过的歌';
      const isDingzhigedan = isGedan && !isSingingList;
      const isSearchSingerDetail = from === '2';

      let str1, str2, fr;

      if (isDingzhigedan) {
        str1 = '首页';
        str2 = '车企定制歌单';
        fr = [1853, 1852];
      } else if (isSearchSingerDetail) {
        str1 = '搜索';
        str2 = '搜索结果-歌星-歌星详情页';
        fr = [1826, 1827];
      } else {
        str1 = '歌手';
        str2 = '歌星详情页';
        fr = [1849, 1850];
      }

      const vipLog = {
        str1,
        str2,
        str3: '点击 VIP 歌曲',
        str4: 'click',
      };

      return { vipLog, fr };
    })

    const orderLog = computed(() => {
      if (route.query.from === 'gedan') {
        if (route.query.name === '唱过的歌') {
          return {
            str1: '唱过的歌',
            str2: '歌曲列表',
          }
        }
        return {
          str1: '车企定制歌单',
          str2: '歌曲列表',
        }
      }
      return {
        str1: '歌手详情页',
        str2: '歌手详情',
      }
    })

    const fetchData = async () => {
      if (keyword.value) {
        handleSearch('loadmore')
        return
      }
      
      if (isRequest.value) return
      isRequest.value = true
      console.log('fetchData')

      try {
        let responseData
        if (listSheet.value.singerid) {
          const { data } = await searchSinger(listSheet.value.singerid, p)
          responseData = { data: data.song }
          singerhead.value = data.singerhead || ''
        } else {
          responseData = await getPlaylistDetail({
            p,
            type: listSheet.value.name,
            version: version.value.latest,
          })
        }

        if (responseData.data.length) {
          if (p === 1 && responseData.version) {
            version.value = responseData.version
          }
          dataList.value = dataList.value.concat(responseData.data)
          p++
        }

        showOffline.value = false
        isEmptyVisible.value = !dataList.value.length
        if (onFirstLoad.value) onFirstLoad.value = false
      } catch (error) {
        console.error('fetchData error:', error)
        if (!dataList.value.length) {
          showOffline.value = true
        }
      } finally {
        isRequest.value = false
      }
    }

    const handleInitData = () => {
      dataList.value = []
      listSheet.value = route.query
      p = 1
      version.value = { current: '', latest: '' }
      songType.value = ''
      fetchData()
    }

    const handleUpdateCachePosition = (u, v) => {
      store.commit('UPDATE_PAGE_CACHEDATA', {
        data: {
          isUsed: u,
          position: v,
        },
        type: 'playlist',
      })
    }

    const handleRetry = async () => {
      try {
        store.commit('base/SET_NET_LOADING', true)
        await fetchData()
      } catch (error) {
        console.error('handleRetry error:', error)
      } finally {
        store.commit('base/SET_NET_LOADING', false)
      }
    }

    const handleSearch = async (payload) => {
      if (!payload) {
        keyword.value = ''
        handleInitData()
        sendLog({
          event_type: 'click',
          event_name: 6006,
          event_data: {
            str1: '歌手详情页',
            str2: '搜索栏',
            str3: '搜索',
            str4: 'click',
          },
        });
        return
      }

      isRequest.value = true
      isEmptyVisible.value = false
      if (payload !== 'loadmore') {
        p = 1
        keyword.value = payload
        dataList.value = []
      } else {
        p++
      }
      try {
        const { song = [] } = await searchSingerSong(
          listSheet.value.singerid,
          keyword.value,
          p
        );
        console.log('歌手详情页面搜索', keyword, song);
        dataList.value = [...dataList.value, ...song]

        isEmptyVisible.value = !dataList.value.length
        isSearchSinger.value = true
      } catch (error) {
        console.error('搜索失败:', error);
      } finally {
        isRequest.value = false
      }
    }

    const handleBack = async () => {
      try {
        keyword.value = '';
        isEmptyVisible.value = false;
        isSearchSinger.value = false;
        p = 1;
        listSheet.value = route.query;
        dataList.value = [];
        onFirstLoad.value = true;

        await nextTick()
        fetchData();
      } catch (error) {
        console.error('An error occurred in handleBack:', error);
        // 你可以在这里添加更多的错误处理逻辑，比如显示错误提示等
      }
    };

    onBeforeMount(() => {
      listSheet.value = route.query
      // if (route.name === 'songList') {
        fetchData()
      // }
      if (route.query.from === 'gedan') {
        sendLog({
          event_type: 'show',
          event_name: 6003,
          event_data: {
            str1: '车企定制歌单',
            str2: '进入定制歌单页',
            str3: '展示',
            str4: 'show',
          },
        });
      } else {
        sendLog({
          event_type: 'show',
          event_name: 6006,
          event_data: {
            str1: '歌手详情页',
            str2: '歌手详情页',
            str3: '进入歌手详情页',
            str4: 'show',
            str5: route.query.singerid,
          },
        });
      }
    })

    onBeforeRouteLeave((to, from, next) => {
      if (to.name === 'search' && from.name === 'playList') {
        const position = loadMoreRef.value ? loadMoreRef.value.root.scrollTop : 0
        handleUpdateCachePosition(true, position)
      }
      next()
    })

    watch(route, (newVal) => {
      if (route.name !== 'songList') return
      p = 1
      listSheet.value = newVal.query
      dataList.value = []
      onFirstLoad.value = true
      fetchData()
    })

    return {
      onFirstLoad,
      imgs,
      themeClass,
      loadMoreRef,
      singerbg,
      singerhead,
      dataList,
      listSheet,
      ponitActionLog,
      songType,
      fetchData,
      handleInitData,
      handleUpdateCachePosition,
      showOffline,
      handleRetry,
      isEmptyVisible,
      handleSearch,
      keyword,
      currentSize,
      isSearchSinger,
      handleBack,
      logData,
      orderLog,
    }
  },
}
</script>

<style lang="stylus" scoped>
.songlist-page
  .scroll-area
    padding 0 calc(152px * 1.5) 0 calc(30px * 1.5)

    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      padding 0 calc(50px * 1.48) 0  calc(48px * 1.48) !important
  .singer-bg
    position absolute
    top 110px
    right 0
    width 807px
    height auto
    z-index 0
  .infinite-loading
    display flex
    justify-content center
  .songlist
    display flex
    justify-content space-between
    align-items flex-start
    height 100%
    overflow hidden
    width 100%
    .empty
      height 80%
      
      // 三分之一屏
      @media (max-width: 700px)
        height 60%
        
    &-right
      width 400px
      margin-top calc(50px * var(--scale))

      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(220px * 1.48) !important
      img
        width 400px
        height 400px
        border-radius 50%!important
        margin-top 0!important

        // 三分之二屏
        @media (max-width: 900px) and (min-width: 701px)  
          width calc(220px * 1.48) !important
          height calc(220px * 1.48) !important
      p
        width 100%
        font-size calc(32px * 1.5);
        margin-top calc(36px * var(--scale))
        text-align center
        text-overflow ellipsis
        white-space nowrap
        overflow hidden
    &-left
      width 1140px
      height 100%
      // 三分之一屏
      @media (max-width: 700px)
        width 100%
      .song-list
        padding-left 0px
        padding-bottom 200px
        @media (max-width: 700px)
          padding-bottom calc(18.33333vw * 3)
        @media (max-width: 900px) and (min-width: 701px)
          padding-bottom calc(18.33333vw * 1.2) !important
        :deep(.song-item)
          @media (max-width: 700px)
            height calc(85px * 3)!important
          // 三分之二屏
          @media (max-width: 900px) and (min-width: 701px)   
            height calc(80px * 1.48) !important
          // 三分之二屏
          @media (max-width: 838px)
            .left
              &:active
                &:after
                  width 100% !important
                  height calc(80px * 1.48) !important
              h3
                line-height calc(30px * 1.48) !important
              .name
                overflow hidden
                text-overflow ellipsis
                white-space nowrap
  .hint
    text-align center
    color #555555
  .offline-container
    width 100vw
    height 100vh
    position fixed
    top 50%
    transform translateY(-20%)

.not-singer
  .songlist-right
    img
      border-radius 40px !important

.songlist-small-cover
  margin-top calc(24px * 3)
  margin-bottom calc(16px * 3)
  img
    width calc(36px * 3)
    height calc(36px * 3)
    border-radius 50%
  p
    margin-left calc(8px * 3)

.songlist-small-cover-gedan
  margin-top 20px
  margin-bottom calc(20px * 3)
  img
    width calc(180px * 3)
    height calc(180px * 3)
    border-radius calc(16px * 3)
    margin 0 auto
  p
    text-align center
    margin-top calc(12px * 3) 
</style>

