<template>
  <div class="record">
    <div class="record-title">兑换记录</div>
    <div class="record-list">
      <LoadMore
        v-if="records.length"
        @load-more="fetchData"
        safeAreaHeight="12.6991vw"
      >
        <div class="record-item" v-for="(item, index) in records" :key="index">
          <div class="left">
            <h3>{{ item.pkg_desc }}</h3>
            <p>{{ item.create_time }}</p>
          </div>
          <div class="right">{{ item.days }} 天</div>
        </div>
        <div class="record-bottom">
          <p>已加载全部</p>
        </div>
      </LoadMore>
      <div v-else-if="!isRequest" class="record-empty">
        <DynamicIcon name="empty1" />
        <p>暂无兑换记录</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeMount, computed, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { getExchangeHistory } from '@/service/vip'
import eventBus from '@/utils/event-bus'

const store = useStore()
const { userInfo } = store.state

const records = ref([])
const p = ref(1)
const isRequest = ref(false)

const handleGetExchangeHistoryList = async (payload = '') => {
  if (payload === 'init') {
    isRequest.value = false
    p.value = 1
    records.value = []
  }
  if (isRequest.value) return
  isRequest.value = true

  const historyResponseData = await getExchangeHistory(userInfo.unionid, p.value)
  if (historyResponseData.length) {
    records.value = [...records.value, ...historyResponseData]
    p.value++
  }
  isRequest.value = false
}

onBeforeMount(() => {
  handleGetExchangeHistoryList()
  eventBus.on('exchange-success', () => handleGetExchangeHistoryList('init'))
})

onUnmounted(() => {
  eventBus.off('exchange-success', handleGetExchangeHistoryList)
})
</script>

<style lang="stylus" scoped>
.record
  &-title
    padding 36px 0 28px
    border-bottom 1px solid var(--border-color)
    color rgba(29, 29, 31, 0.5)
    margin-bottom 0
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      font-size: calc(18px * var(--scale)) !important

    // 三分之一屏
    @media (max-width: 700px)
      font-size var(--font-size-large)

  .record-item
    display flex
    justify-content space-between
    align-items center
    height 157px
    border-bottom 1px solid var(--border-color)

    // 三分之一屏
    @media (max-width: 700px)
      height calc(98px * 3)
      border-bottom 4px solid var(--border-color)
    p
      font-size var(--font-size-small)
      opacity 0.4
    .right
      font-size var(--font-size-extra-large)

  &-bottom
    margin-top 22px
    text-align center
    opacity 0.4
    p
      font-size var(--font-size-tiny)

      // 三分之一屏
      @media (max-width: 700px)
        font-size var(--font-size-small)
        margin-top calc(70px * 3)
    
  &-empty
    width auto
    padding calc(36px * var(--scale))  0 188px
    margin 0px auto 0
    text-align center
    min-height 30vh
    opacity 1
    .svg-icon
      width 90px
      height 90px
      margin-bottom 4px
      margin-left auto
      margin-right auto
      
      // 三分之一屏
      @media (max-width: 700px)
        width calc(50px * 3)
        height calc(50px * 3)
    p
      opacity 0.3
      font-size var(--font-size-extra-small)
        
</style>

