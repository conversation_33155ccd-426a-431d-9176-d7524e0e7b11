<template>
  <div class="page exchange-page">
    <SearchBar title="兑换VIP" :isShowOrder="false" />
    <div class="exchange scroll-area">
      <div class="exchange-input">
        <input
          v-model="cdkey"
          placeholder="请输入16位兑换码，注意区分大小写"
          maxlength="16"
          type="text"
          @input="handleFilterInput"
          @mousedown="disableDoubleClickSelection"
          @keydown="handleSearchKeydown($event)"
          autocapitalize="off"
          ref="exchangeInput"
        />
        <div
          v-show="cdkey"
          class="clear"
          @click.stop="cdkey = ''"
        >
          <DynamicIcon name="close" />
        </div>
      </div>
      <div
        @click="handleExchange"
        class="exchange-btn"
        :class="enable && 'enable'"
      >
        <p>确认兑换</p>
      </div>
      <div class="exchange-rule">
        <div class="title">VIP兑换规则说明:</div>
        <div class="content">
          <div>1. 兑换成功后即可获得兑换卡相应的VIP天数。</div>
          <div>2. 兑换码一次只能兑换一张，不可叠加兑换。</div>
        </div>
      </div>
      <Record from="exchange" />
    </div>
    <RenewVipModal v-if="isShowVipModal" @close="handleCloseVipModal" />
  </div>
</template>

<script setup>
import get from 'lodash/get'
import { useStore } from 'vuex'
import { ref, computed } from 'vue'
import { Dialog } from 'vant'
import Toast from '@/utils/toast'
import RenewVipModal from '@/components/teleport/renew-vip'
import Record from './components/record'
import eventBus from '@/utils/event-bus'
import { sendLog } from '@/directives/v-log/log'

const store = useStore()

let cdkey = ref('')
const userInfo = computed(() => store.state.userInfo)
const macAddress = computed(() => store.state.system.systemInfo.mac)
const enable = ref(false)

let isShowVipModal = ref(false)
const exchangeInput = ref(null)

const handleFilterInput = () => {
  if (!cdkey.value) {
    enable.value = false
    return
  }
  // eslint-disable-next-line
  if (!/^[A-Za-z0-9]+$/gi.test(cdkey.value)) {
    Toast('兑换码格式错误，请检查后重新输入')
    cdkey.value = ''
    enable.value = false
  } else if (cdkey.value.length === 16) {
    enable.value = true
  } else {
    enable.value = false
  }
}

const handleExchange = async () => {
  if (!enable.value) return
  if (cdkey.value.length != 16) {
    Toast('请输入16位兑换码')
    enable.value = false
    return
  }
  const res = await store.dispatch('exchangeVip', {
    cdkey: cdkey.value,
    mac_id: macAddress.value,
  })
  if (get(res, 'errmsg')) {
    enable.value = false
    Dialog.confirm({
      title: '兑换成功',
      confirmButtonText: '知道了',
      showCancelButton: false,
      message: `恭喜您，成功开通VIP\n会员有效期：${
        res.data.start_time.split(' ')[0]
      }-${res.data.end_time.split(' ')[0]}`,
    }).then(() => {
      handleShowVipModal()
    })
    cdkey.value = ''
    eventBus.emit('exchange-success')
  }
  exchangeInput.value.blur()
  sendLog({
    event_type: 'click',
    event_name: '6008',
    event_data: {
      str1: '我的页',
      str2: '兑换VIP',
      str3: '点击确认兑换按钮',
      str4: 'click',
      str5: get(res, 'errmsg') ? 1 : 2,
    }
  })
}

const handleCloseVipModal = () => {
  isShowVipModal.value = false
}

const handleShowVipModal = () => {
  isShowVipModal.value = true
}

const disableDoubleClickSelection = (event) => {
  if (event.detail > 1) {
    event.preventDefault();
  }
}

const handleSearchKeydown = (e) => {
  if (e.keyCode == 13) {
    exchangeInput.value.blur()
    handleExchange()
  }
}

// defineProps({
//   cdkey,
//   userInfo,
//   isShowVipModal,
//   handleExchange,
//   handleFilterInput,
//   handleCloseVipModal,
//   enable,
//   disableDoubleClickSelection,
//   handleSearchKeydown,
//   exchangeInput,
// })
</script>

<style lang="stylus" scoped>
.exchange
  margin-top 35px
  height calc(100vh - 164px)
  overflow-y scroll

  // 三分之一屏
  @media (max-width: 700px)
    padding 0 calc(32px * 3)

  &-input
    position relative
    display flex
    font-size 32px
    margin 0 auto
    width fit-content
    .clear
      width 30px
      height 30px
      position absolute
      right 30px
      top 50%
      transform: translateY(-50%);

      // 三分之一屏
      @media (max-width: 700px)
        width calc(18px * 3)
        height calc(18px * 3)

      .svg-icon
        margin 0px
        width 100%
        height 100%
        left unset
    input
      margin 0 auto
      width 1000px
      height 128px
      border-radius 4px
      border 2px solid var(--border-color2)
      padding 0 30px
      // font-size var(--font-size-medium)
      /* @media screen and (max-width 1200px) and (min-height 1000px)
        width 832px
        font-size 26px */
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        width calc(500px * var(--scale)) !important
        height calc(72px * var(--scale)) !important
        font-size calc(16px * var(--scale))
      // 三分之一屏
      @media (max-width: 700px)
        width 100%

    // 三分之一屏
    @media (max-width: 700px)
      font-size var(--font-size-medium)

      input
        width calc(320px * 3)
        height calc(64px * 3)
        border-radius calc(8px * 3)
  &-btn
    border-radius: 4px;
    background: var(--border-color)
    backdrop-filter: blur(133px);
    width: 300px;
    height: 80px;
    display flex
    justify-content center
    align-items center
    margin 60px auto 0

    // 三分之一屏
    @media (max-width: 700px)
      width calc(198px * 3)
      height calc(48px * 3)
      border-radius calc(8px * 3)
      font-size calc(16px * var(--scale))

    p
      opacity 0.3
    &.enable
      background: #DBAE6A
      p
        opacity 1
        color: rgba(29, 29, 31, 1)
    /* @media screen and (max-width 1200px)
      width: 240px;
      height: 64px;
      font-size: 22px;
      margin-top 48px; */
    // 三分之二屏
    @media (max-width: 900px) and (min-width: 701px)  
      width calc(180px * var(--scale)) !important
      height calc(50px * var(--scale)) !important
  &-rule
    font-size 28px
    margin-top 100px
    color rgba(255, 255, 255, 0.4)
    .title
      font-size: var(--font-size-large)
      opacity 1
      margin-bottom 16px
      // 三分之二屏
      @media (max-width: 900px) and (min-width: 701px)  
        font-size calc(18px * 1.48) !important
        margin-bottom calc(16px * 1.48) !important
    .content
      *
        font-weight 400
        opacity 0.8
        font-size: var(--font-size-extra-small)

    // 三分之一屏
    @media (max-width: 700px)
      .title
        margin-top calc(16px * 3)
</style>

