/* eslint-disable no-console */

import { register } from "register-service-worker";

if (process.env.NODE_ENV === "production") {
  register(`${process.env.BASE_URL}service-worker.js`, {
    ready() {
      console.log(
        "App is being served from cache by a service worker."
      );
    },
    registered() {
      console.log("Service worker has been registered.");
    },
    cached() {
      console.log("Content has been cached for offline use.");
    },
    updatefound() {
      console.log("New content is downloading.");
    },
    updated(registration) {
      if (registration.waiting) {
        registration.waiting.postMessage({
          type: 'SKIP_WAITING'
        })
      }
      console.log("New content is available; please refresh.");
    },
    offline() {
      console.log(
        "No internet connection found. App is running in offline mode."
      );
    },
    error(error) {
      console.error("Error during service worker registration:", error);
    },
  });
}
navigator.serviceWorker.addEventListener('controllerchange', (event) => {
  console.log('程序已更新到最新版,开始自动刷新', event)
  window.location.reload()
  // 当 SW controlling 变化时被触发，比如新的 SW skippedWaiting 成为一个新的被激活的 SW
});