export default {
  mounted(el, binding) {
    let isDragging = false;
    let startY = 0;
    let startX = 0;
    let startTop = 0;
    let startLeft = 0;
    
    // 获取配置参数
    const options = binding.value || {};
    const threshold = options.threshold || 30; // 默认阈值30px
    const smallScreenThreshold = options.smallScreenThreshold || 20; // 小屏阈值
    const mediumScreenThreshold = options.mediumScreenThreshold || 30; // 中屏阈值

    const handleStart = (e) => {
      isDragging = true;
      const touch = e.touches[0];
      startY = touch.clientY;
      startX = touch.clientX;
      
      // 改为获取元素实际位置（包含样式表设置的值）
      const computedStyle = window.getComputedStyle(el);
      startTop = parseInt(computedStyle.top) || 0;
      startLeft = parseInt(computedStyle.left) || 0;
    };

    const handleMove = (e) => {
      if (!isDragging) return;
      const touch = e.touches[0];
      
      const dy = touch.clientY - startY;
      const dx = touch.clientX - startX;
      let newTop = startTop + dy;
      let newLeft = startLeft + dx;

      // 限制垂直边界
      const screenHeight = window.innerHeight;
      newTop = Math.max(0, Math.min(newTop, screenHeight - el.offsetHeight));

      // 限制水平边界
      const screenWidth = window.innerWidth;
      newLeft = Math.max(0, Math.min(newLeft, screenWidth - el.offsetWidth));

      el.style.top = `${newTop}px`;
      el.style.left = `${newLeft}px`;
    };

    const handleEnd = () => {
      if (!isDragging) return;
      isDragging = false;

      const screenWidth = window.innerWidth;
      const currentLeft = parseInt(el.style.left);
      const num = screenWidth < 700 ? smallScreenThreshold : 
                screenWidth < 900 ? mediumScreenThreshold : threshold;

      if (currentLeft + el.offsetWidth / 2 > screenWidth / 2) {
        el.style.left = `${screenWidth - el.offsetWidth - num}px`;
      } else {
        el.style.left = `${num}px`;
      }
    };

    // 绑定事件
    el.addEventListener('touchstart', handleStart);
    el.addEventListener('touchmove', handleMove);
    el.addEventListener('touchend', handleEnd);
    
    // 保存事件处理器以便解绑
    el._draggableHandlers = { handleStart, handleMove, handleEnd };
  },

  unmounted(el) {
    if (el._draggableHandlers) {
      const { handleStart, handleMove, handleEnd } = el._draggableHandlers;
      el.removeEventListener('touchstart', handleStart);
      el.removeEventListener('touchmove', handleMove);
      el.removeEventListener('touchend', handleEnd);
    }
  }
};