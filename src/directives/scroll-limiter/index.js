import { debounce } from 'lodash';

const moveDistance = 400;

export default {
  beforeMount(el) {
    el.lastScrollTop = 0; // 初始化 lastScrollTop
    
    console.log('scroll-limiter beforeMount');

    const handleTouchStart = (event) => {
      el.startTouchY = event.touches[0].clientY; // 记录触摸开始位置
    };

    const handleTouchEnd = debounce((event) => {
      const currentScrollTop = el.scrollTop;

      // 计算触摸移动的方向
      const scrollDelta = el.startTouchY - event.changedTouches[0].clientY;

      // 根据移动的方向更新 scrollTop
      if (scrollDelta > 0) { // 向上滑动
        el.scrollTo({
          top: currentScrollTop + moveDistance,
          behavior: 'smooth' // 添加平滑滚动效果
        });
      } else if (scrollDelta < 0) { // 向下滑动
        el.scrollTo({
          top: currentScrollTop - moveDistance,
          behavior: 'smooth' // 添加平滑滚动效果
        });
      }

      // 更新 lastScrollTop
      el.lastScrollTop = el.scrollTop; 
    }, 100, { leading: true }); // 设置防抖时间为 100 毫秒，可以根据需要调整

    el.addEventListener('touchstart', handleTouchStart);
    el.addEventListener('touchend', handleTouchEnd);
    el._scrollLimiterStart = handleTouchStart; // 保存引用以便在 unbind 时移除
    el._scrollLimiterEnd = handleTouchEnd; // 保存引用以便在 unbind 时移除
  },
  update(el) {
    // 通常不需要在 update 中重复添加事件
  },
  unbind(el) {
    el.removeEventListener('touchstart', el._scrollLimiterStart);
    el.removeEventListener('touchend', el._scrollLimiterEnd);
  }
};
