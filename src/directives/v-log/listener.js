import { sendLog } from './log';

export default class Listener {
    el = null;
    value = null;
    isDone = false;
    io = null;

    constructor({ el, value }) {
      this.el = el;
      this.value = value;
      this.io = this.createIntersectionObserver(el);
    }

    createIntersectionObserver(el) {
      const io = new IntersectionObserver(
        (entries) => {
          if (!entries || !entries.length) return;
          
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.do();
            } else {
              this.isDone = false; // 离开视口时重置状态
            }
          });
        },
        { 
          threshold: 0,
          rootMargin: '200px 0px 200px 0px' // 扩大检测范围
        }
      );

      io.observe(el);
      return io;
    }

    do() {
      if (this.isDone) return;
      sendLog(this.value);
      this.isDone = true;
    }

    update(el, value) {
      this.el = el;
      this.value = value;
      this.isDone = false; // 仅重置状态不重建观察器
    }

    destroy() {
      if (this.io) {
        this.io.unobserve(this.el); // 仅停止观察当前元素
      }
      this.el = null;
      this.value = null;
    }
}