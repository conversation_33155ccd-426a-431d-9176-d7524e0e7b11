import axios from 'axios';
import getHttpConfig from './request/config';
import getRequestCommonParams from './request/request-common-params';
import injectToken from './request/response-token';
import { showErrorToast, reportError } from './request/error-handler';
import get from 'lodash/get';
import store from '@/store';
import { Dialog } from 'vant';

const http = axios.create(
  getHttpConfig({
    urls: {
      pro: '//x.ktvsky.com',
      pre: '//xpre.ktvsky.com',
      test: '//x.ktvsky.com',
    },
  })
);

http.interceptors.request.use((request) => {
  if (!request.params?.noLoading) {
    if ((!store.state.base.net_status || request.url.includes('stb/v2/vip/qr')) && !request.url.includes('/stb/v2/detail/song/lrc')) {
      store.commit('base/SET_NET_LOADING', true);
    }
  } else {
    store.commit('base/SET_NET_LOADING', false);
  }
  if (request.method === 'POST' || request.method === 'DELETE') {
    if (!request.url.includes('/stb')) {
      request.headers['Content-Type'] = 'application/x-www-form-urlencoded';
    }
  }
  if (request.url.includes('/stb') || request.url.includes('carplay')) {
    const commonParams = getRequestCommonParams(
      '84dcb88598ee46cebdd26b8b744625b1',
      (paramsQueryString) => {
        return paramsQueryString.replace(/[&]/g, '');
      }
    );
    request.params = {
      ...request.params,
      ...commonParams,
      noLoading: undefined
    };
  }
  return request;
});

http.interceptors.response.use(
  (response) => {
    store.commit('base/SET_NET_STATUS', true);
    store.commit('base/SET_NET_LOADING', false);
    const res = response.data;
    // console.log('Request URL:', response.config.url);
    // console.log('Request Params:', JSON.stringify(response.config.params));
    // console.log('Request Data:', JSON.stringify(res));
    if (res.errcode === 200 || res.errcode === 21001 || res.code === 200) {
      res.data = injectToken(response.headers, res.data);
      return res;
    }
    // 过滤签到活动的toast提示
    if (!get(res, 'errmsg', '').includes('签到')) showErrorToast(res.errmsg);
    return res;
  },
  (error) => {
    reportError(error);
    console.log('Request URL:', error.toJSON().config.url);
    console.log('Request Params:', JSON.stringify(error.config.params));
    console.log('Request Error', error.toJSON().message, error.toJSON());
    if (error.toJSON().message.includes('timeout of ') || error.toJSON().message.includes('Network Error')) {
      store.commit('base/SET_NET_STATUS', false);
      store.commit('base/SET_NET_LOADING', false);

      Dialog({
        title: '提示',
        className: 'net-error-dialog',
        confirmButtonText: '我知道了',
        showCancelButton: false,
        message: '当前网络状态不佳，请检查网络', // 歌曲下载出现了点问题，请重新加载
        zIndex: 10000,
        transition: false // 禁用动画效果
      })
      .then(() => {
        store.commit('base/SET_NET_LOADING', false);

        const isHomePage =  /^#\/(\?.*)?$/.test(window.location.hash)
        console.log('1204', document.querySelector('#home'))
        if (!document.querySelector('#home') && isHomePage) {
          window.TSNative.exit()
        }
      });

      return Promise.reject(error); // 确保所有的Promise错误都被正确处理
    }
    showErrorToast();
    return Promise.reject(error); // 确保所有的Promise错误都被正确处理
  }
);

export default http;
