import { openDatabase, setItem, getItem } from './IndexedDB';

// 将图片转换为 Base64 并缓存到 IndexedDB
export async function cacheImagesAsBase64(backgroundImages) {
  const cachedImages = {};

  // 打开数据库
  await openDatabase();

  for (const [key, url] of Object.entries(backgroundImages)) {
    try {
      // 检查是否已缓存
      const cached = await getItem(key);
      if (cached) {
        cachedImages[key] = cached;
        console.log(`Loaded ${key} from IndexedDB`);
        continue;
      }

      // 下载图片并转换为 Base64
      const response = await fetch(url);
      const blob = await response.blob();
      const base64 = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });

      // 存储到 IndexedDB
      await setItem(key, base64);
      cachedImages[key] = base64;
      console.log(`Cached ${key} as Base64`);
    } catch (error) {
      console.error(`Failed to cache image ${key}:`, error);
    }
  }

  return cachedImages;
}