//后续看情况确定是否需要加密存储信息
import store from '@/store'
import store2 from 'store2'
import _ from 'lodash'
import { parseISO, differenceInDays, format } from 'date-fns'
// import { TSConfigInstance } from '@/packages/TSJsbridge/TSConfig'
// import { openDatabase, setItem, getItem, deleteItem } from '@/utils/IndexedDB.js';

const setAlreadyList = _.debounce((data) => {
  let time = new Date();
  let alreadyList = data || [...store.state.alreadyList];
  if (alreadyList.length >= 99) {
    alreadyList = alreadyList.slice(0, 99);
  }

  store2('alreadyData', { alreadyList, time });
  console.log('Data saved using store2');
}, 500);

const setOrderedList = _.debounce((data) => {
  let time = new Date();
  let orderedList = data || [...store.state.orderedList];
  if (orderedList.length >= 99) {
    orderedList = orderedList.slice(0, 99);
  }

  store2('orderedData', { orderedList, time });
  console.log('Data saved using store2 orderedList', orderedList);
}, 500);

async function getAlreadyList() {
  try {
    let alreadyList = [];
    let time = null;

    const storedData = store2('alreadyData');
    if (storedData) {
      alreadyList = storedData.alreadyList || [];
      time = storedData.time;
    }

    return alreadyList.filter(item => item?.music_name !== '');
  } catch (error) {
    console.error('Error in getAlreadyList:', error);
    return [];
  }
}

async function getOrderedList() {
  try {
    let orderedList = [];
    let time = null;

    const storedData = store2('orderedData');
    console.log('orderedData', storedData)
    if (storedData) {
      orderedList = storedData.orderedList || [];
      time = storedData.time;
    }

    return orderedList || [];
  } catch (error) {
    console.error('Error in getOrderedList:', error);
    return [];
  }
}

/**
 * 登录送会员活动tag（三次关闭后进入应用不主动打开送会员提示弹窗）
 * 3次关闭领取弹窗进入不显示周期（周期15天，系统内的多次操作算一次）
 */
const setLoginSendVipActivityTag = (v) => {
  let time = new Date()
  store2('lsvaTag', {
    tag: v > 2 ? 3 : v,
    time
  })
}

const getLoginSendVipActivityTag = () => {
  if (!store2('lsvaTag')) return 0
  const { tag, time } = store2('lsvaTag')
  const currDate = new Date()
  if (differenceInDays(currDate, parseISO(time)) > 13) {
    setLoginSendVipActivityTag(0)
    return 0
  }
  return tag || 0
}

/**
 * 开屏弹窗销售麦克风
 * 一天只弹一次，每天第一次打开弹出
 */
const setMicActivityTag = (t) => {
  store2('micActivityTag', {
    time: t
  })
}

const getMicActivityShowTag = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  if (!store2('micActivityTag')) {
    setMicActivityTag(currDate)
    return true
  }
  const { time } = store2('micActivityTag')
  if (currDate !== time) {
    setMicActivityTag(currDate)
    return true
  }
  return false
}

/**
 * 开屏弹窗销售VIP
 * 一天只弹一次，每天第二次打开弹出
 */
const setVipActivityTag = (t) => {
  store2('vipModalActivityTag', {
    time: t
  })
}

// 通过micActivityTag字段判断当天是否已打开一次
const getVipActivityShowTag = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  if (!store2('vipModalActivityTag')) {
    setVipActivityTag(currDate)
    return true
  }
  const { time } = store2('vipModalActivityTag')
  if (currDate !== time) {
    setVipActivityTag(currDate)
    return true
  }
  return false
}

// A/B Test 本地存储规则：从2023/7/7往后推算每天的版本 - ABBAABBAA...
const setABTestTag = (str) => {
  store2('abTestTag', str)
}

const getABTestTag = () => {
  const abTest = store2('abTestTag')
  // 已标记过的用户不再标记
  if (abTest) {
    return abTest
  }
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  const ruleStartTime = '2023-07-08'
  if (currDate === ruleStartTime) {
    setABTestTag('A')
    return 'A'
  }
  // 推算当天版本
  const verObj = {
    1: 'B',
    2: 'B',
    3: 'A',
    0: 'A'
  }
  const length = Math.abs(differenceInDays(parseISO(currDate), parseISO(ruleStartTime)))
  const currver = verObj[length % 4]
  setABTestTag(currver)
  console.log('how many day?', length, currver)

  return currver
}

const setSearchCache = (data) => {
  let searchCache = data || [...store.state.search.searchCache]
  if (searchCache.length > 10) {
    searchCache = searchCache.slice(0, 10)
  }
  store2('searchCache', searchCache || [])
}
const getSearchCache = () => {
  if (!store2('searchCache')) return []
  return store2('searchCache')
}

// 搜索词条 - 一键演唱歌曲
const setSearchSong = (data) => {
  store2('searchSong', data)
}

const getSearchSong = () => {
  if (!store2('searchSong')) return []
  return store2('searchSong')
}

// 过期会员提醒 - 一天只弹一次
const setVipExpireTag = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  store2('vipExpireModalTag', {
    time: currDate
  })
}

const getVipExpireTag = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  if (!store2('vipExpireModalTag')) {
    return true
  }
  const { time } = store2('vipExpireModalTag')
  if (currDate !== time) {
    return true
  }
  return false
}

// 低价激活用户运营支付弹窗 - 一天只弹一次
const setOperationPopupPay = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  store2('OperationPopupPay', {
    time: currDate
  })
}

const getOperationPopupPay = () => {
  const currDate = format(Date.now(), 'yyyy-MM-dd')
  if (!store2('OperationPopupPay')) {
    return true
  }
  const { time = '' } = store2('OperationPopupPay')
  if (currDate !== time) {
    return true
  }
  return false
}

export {
  setAlreadyList,
  setOrderedList,
  getAlreadyList,
  getOrderedList,
  setLoginSendVipActivityTag,
  getLoginSendVipActivityTag,
  setMicActivityTag,
  getMicActivityShowTag,
  setVipActivityTag,
  getVipActivityShowTag,
  getABTestTag,
  setSearchCache,
  getSearchCache,
  setSearchSong,
  getSearchSong,
  setVipExpireTag,
  getVipExpireTag,
  setOperationPopupPay,
  getOperationPopupPay,
}