const postcss = require('postcss');

module.exports = postcss.plugin('remove-landscape-on-mobile', () => {
  return (root) => {
    root.walkAtRules('media', (atRule) => {
      // 检查媒体查询是否包含 (orientation: landscape) 并且宽度小于 818px
      if (
        atRule.params.includes('(orientation: landscape)') &&
        atRule.params.includes('(min-width: 800px)') // 修改为 818px
      ) {
        // 移除 (orientation: landscape)，保留其他条件
        atRule.params = atRule.params.replace('(orientation: landscape)', '').trim();
        // 清理多余的空格和 "and"
        atRule.params = atRule.params.replace(/\s+and\s+/, ' ');
      }
    });
  };
});