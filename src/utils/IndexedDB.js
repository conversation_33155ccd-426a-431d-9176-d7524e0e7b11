let db;

function openDatabase() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('myDatabase', 1);

        request.onupgradeneeded = (event) => {
            db = event.target.result;
            if (!db.objectStoreNames.contains('myStore')) {
                db.createObjectStore('myStore');
            }
        };

        request.onsuccess = (event) => {
            db = event.target.result;
            resolve(db);
        };

        request.onerror = (event) => {
            reject(`Error opening database: ${event.target.errorCode}`);
        };
    });
}

function setItem(key, value) {
    return new Promise((resolve, reject) => {
        const serializableValue = JSON.stringify(value);
        const transaction = db.transaction(['myStore'], 'readwrite');
        const store = transaction.objectStore('myStore');
        const request = store.put(serializableValue, key);

        request.onsuccess = () => {
            resolve(`Successfully set item with key: ${key}`);
        };

        request.onerror = (event) => {
            reject(`Error setting item: ${event.target.errorCode}`);
        };
    });
}

function getItem(key) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(['myStore'], 'readonly');
        const store = transaction.objectStore('myStore');
        const request = store.get(key);

        request.onsuccess = (event) => {
            const result = event.target.result;
            if (result) {
                resolve(JSON.parse(result));
            } else {
                resolve(null); // 如果没有找到数据，返回 null
            }
        };

        request.onerror = (event) => {
            reject(`Error getting item: ${event.target.errorCode}`);
        };
    });
}

function deleteItem(key) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(['myStore'], 'readwrite');
        const store = transaction.objectStore('myStore');
        const request = store.delete(key);

        request.onsuccess = (event) => {
            resolve(`Item with key ${key} deleted successfully`);
        };

        request.onerror = (event) => {
            reject(`Error deleting item: ${event.target.errorCode}`);
        };
    });
}

function clearAllData() {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(['myStore'], 'readwrite');
        const store = transaction.objectStore('myStore');
        const request = store.clear();

        request.onsuccess = () => {
            resolve('Successfully cleared all data');
        };

        request.onerror = (event) => {
            reject(`Error clearing all data: ${event.target.errorCode}`);
        };
    });
}

export {
    setItem,
    getItem,
    deleteItem,
    openDatabase,
    clearAllData,
}