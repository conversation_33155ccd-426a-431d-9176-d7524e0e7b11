import store from '@/store'

export function createTimeoutPromise(promise, timeout = 5000) {
  // 检查 promise 是否是一个 Promise 对象
  if (!(promise instanceof Promise)) {
    // 如果不是 Promise，将其包装成一个立即 resolved 的 Promise
    promise = Promise.resolve(promise);
  }

  // 创建一个超时 Promise
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Promise timed out')), timeout);
  });

  // 使用 Promise.race 来返回第一个完成的 Promise
  return Promise.race([promise, timeoutPromise]).catch((error) => {
    console.error('Operation timed out or failed:', error.message);
    // 在这里处理超时错误，并继续执行后续代码
    return Promise.reject(error);
  });
}

export async function withTimeoutHandling(promise, isShowLoading = false, timeout) {
  if (isShowLoading) {
    console.log('withTimeoutHandling')
    store.commit('base/SET_NET_LOADING', true)
  }
  try {
    const result = await createTimeoutPromise(promise, timeout);
    return result;
  } catch (error) {
    console.error('Operation timed out or failed:', error.message);
    // 在这里处理超时错误，并继续执行后续代码
    return null; // 或者返回一个默认值
  } finally {
    // 隐藏加载状态
    if (isShowLoading) {
      store.commit('base/SET_NET_LOADING', false)
    }
  }
}