// logger.js
import { TSNativeInstance } from '@/packages/TSJsbridge';

class Logger {
  constructor(options = {}) {
    this.logs = []
    this.endpoint = options.endpoint || '/api/logs/report'
    this.level = options.level || 'info'
    this.batchSize = options.batchSize || 10
    this.timer = null
    this.autoReport = options.autoReport !== false
    this.reportInterval = options.reportInterval || 5000
    if (this.autoReport) {
      this.startAutoReport()
    }
  }

  formatLog(level, message, info) {
    return {
      level,
      message: message instanceof Error ? message.stack || message.message : message,
      info,
      time: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: location.href
    }
  }

  log(level, message, info) {
    const log = this.formatLog(level, message, info)
    this.logs.push(log)
    if (this.logs.length >= this.batchSize) {
      this.report()
    }
    if (level === 'error') {
      // 立即上报严重错误
      this.report()
    }
  }

  info(message, info) {
    this.log('info', message, info)
  }

  warn(message, info) {
    this.log('warn', message, info)
  }

  error(message, info) {
    this.log('error', message, info)
  }

  startAutoReport() {
    if (this.timer) return
    this.timer = setInterval(() => {
      if (this.logs.length) {
        this.report()
      }
    }, this.reportInterval)
  }

  stopAutoReport() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  }

  async report() {
    if (!this.logs.length) return
    const logsToSend = this.logs.splice(0, this.batchSize)
    // 直接打印到控制台
    logsToSend.forEach(log => {
      if (log.level === 'error') {
        console.log('[Logger]', log)
      } else if (log.level === 'warn') {
        console.log('[Logger]', log)
      } else {
        console.log('[Logger]', log)
      }
    })
    // 如果以后有接口，只需恢复 fetch 逻辑即可
  }
}

export default Logger 