import axios from 'axios'

const http = axios.create({
  baseURL: 'https://hbeat.ktvsky.com',
  timeout: 15000
});

let heartReportNextTimer = 0
let heartReportNextInterval = 0

const sendHeartReport = (_boot_id) => {
  heartReportNextTimer = setTimeout(() => {
    http.get('/whoami', {
      params: {
        _boot_id,
        _src: 600232,
        _ts: Date.now(),
        t: Date.now() / 1000,
      }
    }).then((res) => {
      if (res.data && res.data.next_interval) {
        heartReportNextInterval = res.data.next_interval * 1000
        sendHeartReport(_boot_id)
      }
    })
  }, heartReportNextInterval)
}

addEventListener('message', ({data}) => {
  if (heartReportNextTimer) {
    clearTimeout(heartReportNextTimer)
  }
  sendHeartReport(data)
});