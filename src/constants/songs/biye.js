// cantoneseSongs - 临时数据 - 2025/6/16 下午1:30:19
const graduteSongs = [
  {
    "singer": "苏打绿,<PERSON>",
    "music_name": "你被写在我的歌里(HD)",
    "songid": 8001050,
    "clickcount": 269,
    "singerid": 109845,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 3,
    "islrc": 3,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/109845.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_vip": 1,
    "is_new": 0
  },
  {
    "songid": "8001007",
    "music_name": "干杯(HD)",
    "singer": "五月天",
    "is_vip": true,
    "singerid": 111739,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/111739.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2424
  },
  {
    "songid": "7594642",
    "music_name": "起风了(HD)",
    "singer": "周深",
    "is_vip": true,
    "singerid": 115336,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/115336.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2000
  },
  {
    "songid": "7036168",
    "music_name": "隐形的翅膀(HD)",
    "singer": "张韶涵",
    "is_vip": true,
    "singerid": 114458,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/114458.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 9525
  },
  {
    "songid": "8920185",
    "music_name": "晴天(B)(HD)",
    "singer": "周杰伦",
    "is_vip": false,
    "singerid": 115281,
    "flag": "流水影",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 2,
    "singer_head": "https://qnktv.ktvdaren.com/singer/115281.jpg",
    "dpi": "",
    "is_score": 0,
    "is_new": 0,
    "clickcount": 50
  },
  {
    "singer": "S.H.E",
    "music_name": "你曾是少年(HD)",
    "songid": 8011799,
    "clickcount": 1807,
    "singerid": 100542,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/100542.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_vip": 1,
    "is_new": 0
  },
  {
    "songid": "7411694",
    "music_name": "北京东路的日子(HD)",
    "singer": "南外学生",
    "is_vip": true,
    "singerid": 108582,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/108582.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 1693
  },
  {
    "songid": "7451161",
    "music_name": "夜空中最亮的星(HD)",
    "singer": "逃跑计划",
    "is_vip": true,
    "singerid": 110327,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/110327.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 4011
  },
  {
    "songid": "8013236",
    "music_name": "小幸运(HD)",
    "singer": "田馥甄",
    "is_vip": true,
    "singerid": 110426,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/110426.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 12374
  },
  {
    "songid": "8010680",
    "music_name": "我们的明天(HD)",
    "singer": "鹿晗",
    "is_vip": true,
    "singerid": 107811,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/107811.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 9806
  },
  {
    "songid": "7013165",
    "music_name": "一个像夏天一个像秋天(HD)",
    "singer": "范玮琪",
    "is_vip": true,
    "singerid": 103021,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/103021.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2252
  },
  {
    "singer": "周杰伦,杨瑞代",
    "music_name": "等你下课(B)(HD)",
    "songid": 8920323,
    "clickcount": 1014,
    "singerid": 115281,
    "flag": "流水影",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 2,
    "singer_head": "https://qnktv.ktvdaren.com/singer/115281.jpg",
    "dpi": "",
    "is_score": 0,
    "is_vip": 0,
    "is_new": 0
  },
  {
    "songid": "7550857",
    "music_name": "追光者(HD)",
    "singer": "岑宁儿",
    "is_vip": true,
    "singerid": 101468,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/101468.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 3707
  },
  {
    "songid": "7126343",
    "music_name": "知足(HD)",
    "singer": "五月天",
    "is_vip": true,
    "singerid": 111739,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/111739.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 3689
  },
  {
    "songid": "8625240",
    "music_name": "最好的我们(HD)",
    "singer": "王栎鑫",
    "is_vip": true,
    "singerid": 110875,
    "flag": "流水影",
    "is_1080": 0,
    "lrc": 5,
    "islrc": 5,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/110875.jpg",
    "dpi": "",
    "is_score": 0,
    "is_new": 0,
    "clickcount": 50
  },
  {
    "songid": "7047540",
    "music_name": "和你一样(HD)",
    "singer": "李宇春",
    "is_vip": true,
    "singerid": 106556,
    "flag": "流水影",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/106556.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 615
  },
  {
    "songid": "7200081",
    "music_name": "盛夏的果实(HD)",
    "singer": "莫文蔚",
    "is_vip": true,
    "singerid": 108484,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/108484.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 31925
  },
  {
    "songid": "8001043",
    "music_name": "那些年(HD)",
    "singer": "胡夏",
    "is_vip": true,
    "singerid": 104548,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/104548.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2379
  },
  {
    "songid": "7593264",
    "music_name": "同桌的你(B)",
    "singer": "老狼",
    "is_vip": false,
    "singerid": 105781,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/105781.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 6074
  },
  {
    "songid": "7651979",
    "music_name": "明天会更好(HD)",
    "singer": "卓依婷",
    "is_vip": true,
    "singerid": 115642,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/115642.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 4957
  },
  {
    "songid": "7016274",
    "music_name": "青春纪念册",
    "singer": "可米小子",
    "is_vip": true,
    "singerid": 105604,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/105604.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 143
  },
  {
    "songid": "8014180",
    "music_name": "勋章(HD)",
    "singer": "鹿晗",
    "is_vip": true,
    "singerid": 107811,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/107811.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 3574
  },
  {
    "singer": "汪苏泷,BY2",
    "music_name": "有点甜(HD)",
    "songid": 8002886,
    "clickcount": 26107,
    "singerid": 110639,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/110639.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_vip": 1,
    "is_new": 0
  },
  {
    "songid": "7468394",
    "music_name": "不再见(HD)",
    "singer": "陈学冬",
    "is_vip": true,
    "singerid": 102083,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/102083.jpg",
    "dpi": "480,720",
    "is_score": 0,
    "is_new": 0,
    "clickcount": 818
  },
  {
    "singer": "华语群星",
    "music_name": "告白气球+说好不哭+稻香+等你下课+Mojito(HD)",
    "songid": 7598240,
    "clickcount": 26,
    "singerid": 104627,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 5,
    "islrc": 5,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/104627.jpg",
    "dpi": "480",
    "is_score": 0,
    "is_vip": 1,
    "is_new": 0
  },
  {
    "singer": "TFBoys",
        "music_name": "宠爱(HD)",
        "songid": 8012157,
        "clickcount": 4933,
        "singerid": 100617,
        "flag": "MV",
        "is_1080": 1,
        "lrc": 2,
        "islrc": 2,
        "acc": 2,
        "org": 1,
        "singer_head": "https://qnktv.ktvdaren.com/singer/100617.jpg",
        "dpi": "480,720",
        "is_score": 1,
        "is_vip": 1,
        "is_new": 0
  },
  {
    "songid": "8008767",
    "music_name": "匆匆那年(HD)",
    "singer": "王菲",
    "is_vip": true,
    "singerid": 110722,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/110722.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 6257
  },
  {
    "songid": "7505255",
    "music_name": "不说再见(HD)",
    "singer": "好妹妹乐队",
    "is_vip": true,
    "singerid": 104079,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/104079.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 369
  },
  {
    "songid": "7593264",
    "music_name": "同桌的你(B)",
    "singer": "老狼",
    "is_vip": false,
    "singerid": 105781,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/105781.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 6074
  },
  {
    "singer": "何炅",
        "music_name": "栀子花开(HD)",
        "songid": 7009578,
        "clickcount": 336,
        "singerid": 104153,
        "flag": "MV",
        "is_1080": 0,
        "lrc": 2,
        "islrc": 2,
        "acc": 2,
        "org": 1,
        "singer_head": "https://qnktv.ktvdaren.com/singer/104153.jpg",
        "dpi": "480",
        "is_score": 1,
        "is_vip": 1,
        "is_new": 0
  },
  {
    "songid": "7531416",
    "music_name": "一笑倾城(HD)",
    "singer": "汪苏泷",
    "is_vip": true,
    "singerid": 110639,
    "flag": "流水影",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/110639.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 14019
  },
  {
    "songid": "7413846",
    "music_name": "追梦赤子心(HD)",
    "singer": "GALA乐队",
    "is_vip": true,
    "singerid": 100259,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/100259.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2037
  },
  {
    "songid": "7038773",
    "music_name": "最初的梦想(HD)",
    "singer": "范玮琪",
    "is_vip": true,
    "singerid": 103021,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/103021.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2797
  },
  {
    "songid": "8805962",
    "music_name": "遇见(HD)",
    "singer": "孙燕姿",
    "is_vip": true,
    "singerid": 110073,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/110073.jpg",
    "dpi": "",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 13187
  },
  {
    "songid": "7012488",
    "music_name": "当(HD)",
    "singer": "动力火车",
    "is_vip": true,
    "singerid": 102838,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/102838.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 22923
  },
  {
    "singer": "容祖儿",
        "music_name": "挥着翅膀的女孩(B)",
        "songid": 7593265,
        "clickcount": 1156,
        "singerid": 109311,
        "flag": "MV",
        "is_1080": 0,
        "lrc": 2,
        "islrc": 2,
        "acc": 2,
        "org": 1,
        "singer_head": "https://qnktv.ktvdaren.com/singer/109311.jpg",
        "dpi": "480",
        "is_score": 1,
        "is_vip": 0,
        "is_new": 0
  },
  {
    "songid": "6002918",
    "music_name": "我的未来式(HD)",
    "singer": "郭采洁",
    "is_vip": true,
    "singerid": 103752,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/103752.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2928
  },
  {
    "songid": "7529347",
    "music_name": "后来的我们(HD)",
    "singer": "五月天",
    "is_vip": true,
    "singerid": 111739,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/111739.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 4795
  },
  {
    "songid": "7001223",
    "music_name": "阳光总在风雨后(HD)",
    "singer": "许美静",
    "is_vip": true,
    "singerid": 112664,
    "flag": "流水影",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/112664.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 453
  },
  {
    "singer": "TFBoys",
        "music_name": "大梦想家(HD)",
        "songid": 7495065,
        "clickcount": 1509,
        "singerid": 100617,
        "flag": "演唱会",
        "is_1080": 0,
        "lrc": 2,
        "islrc": 2,
        "acc": 2,
        "org": 1,
        "singer_head": "https://qnktv.ktvdaren.com/singer/100617.jpg",
        "dpi": "480",
        "is_score": 1,
        "is_vip": 1,
        "is_new": 0
  },
  {
    "songid": "7131095",
    "music_name": "再见(HD)",
    "singer": "张震岳",
    "is_vip": true,
    "singerid": 114727,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/114727.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 16944
  },
  {
    "songid": "7300616",
    "music_name": "后来(HD)",
    "singer": "刘若英",
    "is_vip": true,
    "singerid": 107419,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/107419.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 118109
  },
  {
    "songid": "8620017",
    "music_name": "看得最远的地方(HD)",
    "singer": "毛不易",
    "is_vip": true,
    "singerid": 143730,
    "flag": "流水影",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/143730.jpg",
    "dpi": "480,720",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 673
  },
  {
    "songid": "7592713",
    "music_name": "想见你想见你想见你(HD)",
    "singer": "八三夭",
    "is_vip": true,
    "singerid": 101018,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/101018.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2616
  },
  {
    "singer": "秦博,秦小源",
        "music_name": "纸短情长(HD)",
        "songid": 4120173,
        "clickcount": 2191,
        "singerid": 119475,
        "flag": "流水影",
        "is_1080": 1,
        "lrc": 2,
        "islrc": 2,
        "acc": 2,
        "org": 1,
        "singer_head": "https://qnktv.ktvdaren.com/singer/119475.jpg",
        "dpi": "480",
        "is_score": 1,
        "is_vip": 0,
        "is_new": 0
  },
  {
    "songid": "7007449",
    "music_name": "倔强(HD)",
    "singer": "五月天",
    "is_vip": true,
    "singerid": 111739,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/111739.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2857
  },
  {
    "songid": "7200089",
    "music_name": "那些花儿(HD)",
    "singer": "朴树",
    "is_vip": true,
    "singerid": 108909,
    "flag": "MV",
    "is_1080": 0,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/108909.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 1279
  },
  {
    "songid": "7013148",
    "music_name": "像风一样自由(HD)",
    "singer": "许巍",
    "is_vip": true,
    "singerid": 112699,
    "flag": "MV",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/112699.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 2633
  },
  {
    "songid": "7404959",
    "music_name": "有何不可(HD)",
    "singer": "许嵩",
    "is_vip": true,
    "singerid": 112695,
    "flag": "流水影",
    "is_1080": 1,
    "lrc": 2,
    "islrc": 2,
    "acc": 2,
    "org": 1,
    "singer_head": "https://qnktv.ktvdaren.com/singer/112695.jpg",
    "dpi": "480",
    "is_score": 1,
    "is_new": 0,
    "clickcount": 12083
  }
];
module.exports = { graduteSongs };
