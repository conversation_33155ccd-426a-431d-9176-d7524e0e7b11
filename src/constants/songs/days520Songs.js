// days520Songs
const days520Songs = () => ([
    {
      "singer": "杨宗纬,王宇宙Leto,宝石Gem",
      "music_name": "若月亮没来-天赐的声音5(HD)",
      "songid": 8985449,
      "clickcount": 6819,
      "singerid": 113254,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/113254.jpg",
      "dpi": "",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "songid": "7402764",
      "music_name": "全是爱(HD)",
      "singer": "凤凰传奇",
      "is_vip": true,
      "singerid": 103264,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/103264.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 45795
    },
    {
      "songid": "8920181",
      "music_name": "告白气球(B)(HD)",
      "singer": "周杰伦",
      "is_vip": false,
      "singerid": 115281,
      "flag": "流水影",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 2,
      "singer_head": "https://qnktv.ktvdaren.com/singer/115281.jpg",
      "dpi": "",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 1977
    },
    {
      "singer": "陶喆,蔡依林",
      "music_name": "今天你要嫁给我(HD)",
      "songid": 7006455,
      "clickcount": 14336,
      "singerid": 110356,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/110356.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "songid": "7048526",
      "music_name": "很爱很爱你(HD)",
      "singer": "刘若英",
      "is_vip": true,
      "singerid": 107419,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/107419.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 3119
    },
    {
      "singer": "任静,付笛声",
      "music_name": "知心爱人(HD)",
      "songid": 7124709,
      "clickcount": 65580,
      "singerid": 109255,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/109255.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "林子祥,叶倩文",
      "music_name": "选择(HD)",
      "songid": 7421348,
      "clickcount": 6120,
      "singerid": 107128,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/107128.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "周杰伦,梁心颐",
      "music_name": "珊瑚海(B)(HD)",
      "songid": 8920378,
      "clickcount": 42,
      "singerid": 115281,
      "flag": "流水影",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 2,
      "singer_head": "https://qnktv.ktvdaren.com/singer/115281.jpg",
      "dpi": "",
      "is_score": 0,
      "is_vip": 0,
      "is_new": 0
    },
    {
      "singer": "林俊杰,蔡卓妍",
      "music_name": "小酒窝(HD)",
      "songid": 7350026,
      "clickcount": 37605,
      "singerid": 106927,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/106927.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "林俊杰,金莎",
      "music_name": "被风吹过的夏天(HD)",
      "songid": 7026698,
      "clickcount": 1764,
      "singerid": 106927,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/106927.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "陈奕迅,王菲",
      "music_name": "因为爱情(HD)",
      "songid": 7409832,
      "clickcount": 56250,
      "singerid": 102141,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/102141.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "张学友,高慧君",
      "music_name": "你最珍贵(HD)",
      "songid": 7031377,
      "clickcount": 12070,
      "singerid": 114593,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/114593.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "songid": "7300288",
      "music_name": "月亮代表我的心(HD)",
      "singer": "邓丽君",
      "is_vip": true,
      "singerid": 102609,
      "flag": "演唱会",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/102609.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 11996
    },
    {
      "songid": "8920225",
      "music_name": "简单爱(B)(HD)",
      "singer": "周杰伦",
      "is_vip": false,
      "singerid": 115281,
      "flag": "流水影",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 2,
      "singer_head": "https://qnktv.ktvdaren.com/singer/115281.jpg",
      "dpi": "",
      "is_score": 0,
      "is_new": 0,
      "clickcount": 309
    },
    {
      "singer": "孙楠,韩红",
      "music_name": "美丽的神话(HD)",
      "songid": 7300835,
      "clickcount": 45527,
      "singerid": 110023,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/110023.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "songid": "8013236",
      "music_name": "小幸运(HD)",
      "singer": "田馥甄",
      "is_vip": true,
      "singerid": 110426,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/110426.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 18115
    },
    {
      "songid": "7001245",
      "music_name": "爱你不是两三天(HD)",
      "singer": "梁静茹",
      "is_vip": true,
      "singerid": 106728,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/106728.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 641
    },
    {
      "singer": "韩红,林俊杰",
      "music_name": "飞云之下(HD)",
      "songid": 7571627,
      "clickcount": 2860,
      "singerid": 103993,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/103993.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "Charlie Puth,Selena Gomez",
      "music_name": "We Don't Talk Anymore(HD)",
      "songid": 6722639,
      "clickcount": 565,
      "singerid": 303409,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/303409.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "songid": "6733276",
      "music_name": "For Him",
      "singer": "Troye Sivan",
      "is_vip": false,
      "singerid": 303148,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 5,
      "islrc": 5,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/303148.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 147
    },
    {
      "songid": "6000907",
      "music_name": "Baby(HD)",
      "singer": "Justin Bieber",
      "is_vip": true,
      "singerid": 301546,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/301546.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 6180
    },
    {
      "songid": "7786804",
      "music_name": "Fantastic Baby(HD)",
      "singer": "BigBang",
      "is_vip": true,
      "singerid": 500083,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/500083.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 4154
    },
    {
      "songid": "7036939",
      "music_name": "好想好想(HD)",
      "singer": "古巨基",
      "is_vip": true,
      "singerid": 103624,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/103624.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 1393
    },
    {
      "singer": "温岚,吴宗宪",
      "music_name": "屋顶",
      "songid": 7131020,
      "clickcount": 96,
      "singerid": 111409,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/111409.jpg",
      "dpi": "",
      "is_score": 0,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "潘玮柏,弦子",
      "music_name": "不得不爱(HD)",
      "songid": 7131273,
      "clickcount": 9166,
      "singerid": 108772,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/108772.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "张学友,汤宝如",
      "music_name": "相思风雨中(HD)",
      "songid": 7122457,
      "clickcount": 19126,
      "singerid": 114593,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/114593.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "许秋怡,张智霖",
      "music_name": "现代爱情故事(B版)(HD)",
      "songid": 8895530,
      "clickcount": 1284,
      "singerid": 112682,
      "flag": "流水影",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/112682.jpg",
      "dpi": "",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "甄妮,罗文",
      "music_name": "铁血丹心(HD)",
      "songid": 7121051,
      "clickcount": 8704,
      "singerid": 114977,
      "flag": "演唱会",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/114977.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "梅艳芳,张国荣",
      "music_name": "芳华绝代",
      "songid": 7008684,
      "clickcount": 69,
      "singerid": 108287,
      "flag": "演唱会",
      "is_1080": 0,
      "lrc": 0,
      "islrc": 0,
      "acc": 1,
      "org": 2,
      "singer_head": "https://qnktv.ktvdaren.com/singer/108287.jpg",
      "dpi": "",
      "is_score": 0,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "singer": "梅艳芳,张学友",
      "music_name": "相爱很难",
      "songid": 7037675,
      "clickcount": 217,
      "singerid": 108287,
      "flag": "MV",
      "is_1080": 0,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/108287.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    },
    {
      "songid": "7014679",
      "music_name": "谁明浪子心(HD)",
      "singer": "王杰",
      "is_vip": true,
      "singerid": 110804,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/110804.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 13756
    },
    {
      "songid": "7007787",
      "music_name": "红豆(国＆粤)(HD)",
      "singer": "王菲",
      "is_vip": true,
      "singerid": 110722,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/110722.jpg",
      "dpi": "480",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 8786
    },
    {
      "songid": "8602189",
      "music_name": "如愿(HD)",
      "singer": "王菲",
      "is_vip": true,
      "singerid": 110722,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/110722.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_new": 0,
      "clickcount": 47404
    },
    {
      "singer": "莫文蔚,张洪量",
      "music_name": "广岛之恋(HD)",
      "songid": 7131853,
      "clickcount": 17835,
      "singerid": 108484,
      "flag": "MV",
      "is_1080": 1,
      "lrc": 2,
      "islrc": 2,
      "acc": 2,
      "org": 1,
      "singer_head": "https://qnktv.ktvdaren.com/singer/108484.jpg",
      "dpi": "480,720",
      "is_score": 1,
      "is_vip": 1,
      "is_new": 0
    }
]);
  
export { days520Songs };
  