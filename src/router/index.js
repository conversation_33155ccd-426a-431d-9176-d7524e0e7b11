import { createRouter, createWebHashHistory } from 'vue-router'
import { Toast } from 'vant'
import store from '@/store'
import useLoginConfirm from '@/components/modal/global/login-confirm/create'
import useLoginQrcode from '@/components/modal/global/login-qrcode/create'
import { sendLog } from '@/directives/v-log/log'
import { format } from 'date-fns'
import formatStr from '@/constants/index'
import { TSWebEventInstance } from '@/packages/TSJsbridge';
import { Dialog } from 'vant';
// import { getCarplayInfo } from '@/service/carplay-info'
import { isVcGreaterThanOrEqual } from '@/utils/device'
// import { withTimeoutHandling } from '@/utils/promiseUtils';
import Search from '../pages/search/index.vue'

const loginConfrimModal = useLoginConfirm()
const loginQrcodeModal = useLoginQrcode()

const routes = [
  {
    path: '/',
    name: 'home',
    component: () =>
      import(
        /* webpackChunkName: "home" */ '../pages/index/index.vue'
      ),
    meta: {
      showOrderedPopupBtn: false,
      keepAlive: true,
      key:'home'
    }
  },
  {
    path: '/agreement/privacy',
    name: 'agreementPrivacy',
    component: () =>
      import(
        /* webpackChunkName: "agreementPrivacy" */ '../pages/agreement/privacy_v1.vue'
      ),
    meta: {
      showOrderedPopupBtn: true
    }
  },
  {
    path: '/agreement/user',
    name: 'agreementUser',
    component: () =>
      import(
        /* webpackChunkName: "agreementUser" */ '../pages/agreement/user.vue'
      ),
    meta: {
      showOrderedPopupBtn: true
    }
  },
  {
    path: '/songlist',
    name: 'songList',
    component: () =>
      import(
        /* webpackChunkName: "songList" */ '../pages/songlist/index.vue'
      ),
    meta: {
      showOrderedPopupBtn: true,
      keepAlive: false,
      key:'songlist'
    }
  },
  {
    path: '/xiaomi-exclusive',
    name: 'xiaomiExclusive',
    component: () =>
      import(
        /* webpackChunkName: "songList" */ '../pages/songlist/index.vue'
      ),
    meta: {
      showOrderedPopupBtn: true,
      keepAlive: false,
      key:'songlist'
    }
  },
  {
    path: '/singer',
    name: 'singer',
    component: () =>
      import(
        /* webpackChunkName: "singer" */ '../pages/singer/index.vue'
      ),
    meta: {
      showOrderedPopupBtn: true,
      keepAlive: true,
      key:'singer'
    }
  },
  {
    path: '/singing',
    name: 'singing',
    component: () =>
      import(
        /* webpackChunkName: "singing" */ '../pages/singing/index.vue'
      ),
    meta: {
      showOrderedPopupBtn: true,
      key:'singing'
    }
  },
  {
    path: '/search',
    name: 'search',
    component: Search,
    meta: {
      showOrderedPopupBtn: true,
      isUseCache: false,
      keepAlive: true,
      key:'search'
    }
  },
  {
    path: '/mine',
    name: 'mine',
    component: () =>
      import(
        /* webpackChunkName: "mine" */ '../pages/mine/index.vue'
      ),
    meta: {
      showOrderedPopupBtn: true
    }
  },
  {
    path: '/orders',
    name: 'orders',
    component: () =>
      import(
        /* webpackChunkName: "orders" */ '../pages/mine/orders.vue'
      ),
    meta: {
      showOrderedPopupBtn: true,
      requireLoginValid: true
    }
  },
  {
    path: '/vip',
    name: 'vip',
    component: () =>
      import(
        /* webpackChunkName: "vip" */ '../pages/vip/index.vue'
      ),
    meta: {
      showOrderedPopupBtn: true,
      requireLoginValid: true
    }
  },
  {
    path: '/vip-exchange',
    name: 'vipExchange',
    component: () =>
      import(
        /* webpackChunkName: "vipExchange" */ '../pages/vip/exchange.vue'
      ),
    meta: {
      showOrderedPopupBtn: true,
      requireLoginValid: true
    }
  },
  {
    path: '/setting',
    name: 'setting',
    component: () =>
      import(
        /* webpackChunkName: "mine" */ '../pages/mine/setting.vue'
      ),
    meta: {
      showOrderedPopupBtn: true
    }
  },
  {
    path: '/small-more',
    name: 'more',
    component: () =>
      import(
        /* webpackChunkName: "mine" */ '../pages/mine/more.vue'
      ),
    meta: {
      showOrderedPopupBtn: true
    }
  },
  {
    path: '/small-ordered',
    name: 'smallOrdered',
    component: () =>
      import(
        /* webpackChunkName: "mine" */ '../pages/small/ordered.vue'
      ),
    meta: {
      showOrderedPopupBtn: true
    }
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }

    if (from.meta.keepAlive) {
      from.meta.savedPosition = document.body.scrollTop
    }
    return { x: 0, y: to.meta.savedPosition || 0 }
  }
})

function canUserAccess() {
  if (!store.state.userInfo.unionid) {
    router.push({
      name: 'home'
    })
    // 需求变更 - 改回只登录
    loginConfrimModal.show({
      onConfirm: () => {
        loginQrcodeModal.show()
      }
    })
    return false
  }
  return true
}

router.beforeEach(async (to, from) => {
  if (to.name !== 'home') {
    store.commit('SET_MINI_VISIBLE', false)
  } else {
    store.commit('SET_MINI_VISIBLE', true)
  }
  // store.commit('UPDATE_PROGRESS', true)
  console.log(`router beforeEach from ${from.name} to ${to.name}`)
  if (store.state.base.isRouterBack) {
    store.commit('base/SET_IS_ROUTER_BACK', false)
    return true
  }
  if (!store.state.base.net_status) {
    if (!store.state.base.net_status) {
      Dialog({
        title: '提示',
        className: 'net-error-dialog',
        confirmButtonText: '我知道了',
        showCancelButton: false,
        message: '当前网络状态不佳，请检查网络', // 歌曲下载出现了点问题，请重新加载
        zIndex: 10000,
        transition: false // 禁用动画效果
      }).then(() => {
        store.commit('UPDATE_MV_ISHIDE', true);
      });
      return isVcGreaterThanOrEqual(100030)
    }
  }
  if (to.meta.requireLoginValid) {
    return canUserAccess()
  }
  return true
})

// 页面停留时间上报
// https://m2miovoqda.feishu.cn/wiki/wikcnKrGotVoDCPjP6lki2IhLFf
let enterStartTime = 0

const RouteNameStatCodeMap = {
  songList: 181,
  singer: 182,
  classify: 183,
}

function stayTimeStat(routeName) {
  if (enterStartTime) {
    const endTime = Date.now()
    const stayTime = Math.round((endTime - enterStartTime) / 1000)
    sendLog({
      event_type: 'custom',
      event_name: RouteNameStatCodeMap[routeName],
      event_data: {
        start_time: format(enterStartTime, formatStr),
        end_time: format(endTime, formatStr),
        stay_time: stayTime,
      }
    })
  }
  if (RouteNameStatCodeMap[routeName]) {
    enterStartTime = Date.now()
  }
}

router.afterEach((to, from) => {
  // store.commit('UPDATE_PROGRESS', false)
  const toName = to.name || 'unknown'
  const fromName = from?.name || 'unknown'
  console.log(`router afterEach from ${fromName} to ${toName}`)
  // 路由上报
  sendLog({
    event_type: 'custom',
    event_name: 161,
    event_data: {
      route_page: toName || to.path || 'unknown', 
      route_name: toName || to.path || 'unknown'
    }
  })
  // 页面停留时间上报
  stayTimeStat(toName)
  Toast.clear()
  TSWebEventInstance.onWebPageChanged(toName, fromName)
})

/* 路由异常错误处理，尝试解析一个异步组件时发生错误，重新渲染目标页面 */
router.onError(async (error) => {
  console.log('router.onError', JSON.stringify(error))
  // store.commit('UPDATE_PROGRESS', false)

  const pattern = /Loading chunk.*failed/g;
  const isChunkLoadFailed = error.message.match(pattern);

  await router.isReady();
  const targetPath = router.history?.pending?.fullPath;
  console.log('router.onError', targetPath)
  
  if (isChunkLoadFailed && targetPath) {
    console.log('router.onError replace', targetPath)
    router.replace(targetPath);
  } else if (!isVcGreaterThanOrEqual(100030)) {
    store.commit('base/SET_NET_STATUS', false);
    store.commit('base/SET_NET_LOADING', false);
    Dialog({
      title: '提示',
      className: 'net-error-dialog',
      confirmButtonText: '我知道了',
      showCancelButton: false,
      message: '当前网络状态不佳，请检查网络', // 歌曲下载出现了点问题，请重新加载
      zIndex: 10000,
      transition: false // 禁用动画效果
    })
    .then(() => {
      store.commit('base/SET_NET_LOADING', false);
    });
  }
});

export default router
