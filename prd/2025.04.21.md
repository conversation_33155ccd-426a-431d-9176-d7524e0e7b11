### 2025.04.21 迭代需求

### 需求地址

- https://m2miovoqda.feishu.cn/docx/DPWYdoixcoEuKHx0WNHcdUTanpp

### UI 设计
- https://www.figma.com/design/x3mKUanGgWcKCtftixpMHt/%E8%BD%A6%E8%BD%BD%E8%BF%90%E8%90%A5?node-id=1256-23963&t=LEkEk3B53p2qkEk9-0

### 需求描述

1. 在首页顶栏位置，新增 tips 消息组件。

2. 组件内容

   - 1. 未登录：开小米汽车，唱雷石音乐，享巅峰人生！ 去登录
   - 2. 已登陆非 VIP：您的起步已经让 ta 望尘莫及如若伴随音乐，岂不快哉！点我又惊喜！
   - 3. 已登陆 VIP：以数为律，唱响心声！
   - 4. 已登陆过期 VIP：全新版本权益重磅升级，邀您回归！

3. 点击效果
   - 1. 未登录：点击弹出登录弹窗
   - 2. 已登陆非 VIP：  1. 点击弹出车机端付费弹窗
   - 3. 已登陆 VIP：点击弹出toast提示：展示您的歌喉，尽情欢唱吧！
   - 4. 已登陆过期 VIP：  1. 点击弹出车机端付费弹窗

4. 组件位置适配
    已知3种尺寸
        - 1280px * 664px：tips组件位于首页顶栏logo的右侧
        - 818px * 651px：tips组件位于首页顶栏下方位置
        - 399px * 651px：tips组件位于首页顶栏下方位置

5. figma导出vue组件代码参考：
```vue

<template>
  <div class="frame-2090051427">
    <img class="div" src="div0.png" />
    <div class="div2">
      <span>
        <span class="div-2-span">开小米汽车,唱雷石音乐,享巅峰人生！</span>
        <span class="div-2-span2">去登录</span>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  name: "Frame2090051427",
  components: {},
  props: {},
  data() {},
};
</script>
<style scoped>
.frame-2090051427,
.frame-2090051427 * {
  box-sizing: border-box;
}
.frame-2090051427 {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  padding: 13px 10px 13px 10px;
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}
.div {
  flex-shrink: 0;
  width: 27px;
  height: 27px;
  position: relative;
  object-fit: cover;
  aspect-ratio: 1;
}
.div2 {
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 16px;
  font-weight: 400;
  position: relative;
}
.div-2-span {
  color: #151515;
  font-family: "HarmonyOsSansSc-Regular", sans-serif;
  font-size: 16px;
  font-weight: 400;
}
.div-2-span2 {
  color: #dbae6a;
  font-family: "HarmonyOsSansSc-Bold", sans-serif;
  font-size: 16px;
  font-weight: 700;
}
</style>

```