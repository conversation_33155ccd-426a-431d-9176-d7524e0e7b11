# 公版应用

## 项目概述

公版应用是一个多功能应用，主分支不包含录音、打分和遥控走焦功能。如果项目需要用到，可以使用git cherry-pick commitID

1. **录音功能分支**：`public-version/record`
2. **打分功能分支**：`public-version/score`

## 录音功能

### 分支：`public-version/record`

录音功能允许用户在MV页面侧边栏开启或结束录音，并在“我的”页面展示“我的录音”列表。该功能提供了以下特性：

- **MV页面侧边栏**：用户可以在MV页面侧边栏开启或结束录音。
- **我的录音列表**：在“我的”页面展示用户的录音列表，支持在线播放、手机扫码播放落地页以及删除录音。

## 打分功能

### 分支：`public-version/score`

打分功能允许用户在设置页面开启或关闭打分功能，并在本地存储打分结果。该功能提供了以下特性：

- **设置页面打分开关**：用户可以在设置页面开启或关闭打分功能。

- **本地存储打分结果列表页**：用户可以在本地存储打分结果，并在列表页查看。

- **歌曲列表评分icon**：在歌曲列表中展示评分icon。

- **MV页面打分条**：在MV页面展示打分条，用户可以进行评分。

- **重唱/切歌展示打分结果页面**：在重唱或切歌时，展示打分结果页面。

## 安装依赖

在项目根目录下运行以下命令来安装项目依赖：

```bash
yarn install
```

本地运行
安装依赖后，运行以下命令以启动本地开发服务器：
```bash
yarn dev
```