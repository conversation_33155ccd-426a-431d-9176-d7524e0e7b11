cache:
  paths:
    - node_modules/
stages:
    - build
    - deploy
build:
    stage: build
    tags:
        - deploy-runner-4
    script:
        - yarn && yarn stage
    artifacts:
        name: '$CI_COMMIT_REF_NAME-dist'
        expire_in: 2 mins
        paths:
            - dist/
    only:
        - /^release\/.*$/
deploy:
    stage: deploy
    tags:
        - deploy-runner-4
    script:
        - ls -la .
        - rsync -a --delete dist/ work@*************:~/online/src/$CI_PROJECT_NAME/dist
    only:
        - /^release\/.*$/