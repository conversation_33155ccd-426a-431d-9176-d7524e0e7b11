# 图片缓存功能方案详解

## 一、功能背景与目标

在前端项目中，频繁加载远程图片会带来以下问题：
- 网络波动时图片加载慢，影响用户体验
- 重复访问同一图片会重复请求，浪费带宽
- 离线场景下图片无法显示

**目标**：通过将图片转为 Base64 并缓存到浏览器本地（IndexedDB），实现图片的高效复用、离线可用和加速加载。

---

## 二、核心原理

1. **图片转 Base64**：将远程图片下载为 Blob，再用 FileReader 转为 Base64 字符串。
2. **本地持久化**：利用浏览器的 IndexedDB 数据库，将 Base64 字符串以 key-value 形式存储。
3. **优先读取缓存**：每次需要图片时，先查本地缓存，有则直接用，无则下载、转码、缓存。
4. **离线可用**：即使断网，已缓存的图片依然可用。

---

## 三、依赖说明

- **IndexedDB**：浏览器原生的 NoSQL 数据库，适合存储大量结构化数据。
- **FileReader**：用于将 Blob 数据转为 Base64 字符串。
- **Promise/async-await**：异步流程控制。

---

## 四、核心实现结构

### 1. IndexedDB 封装（`src/utils/IndexedDB.js`）

- `openDatabase()`：初始化数据库和对象仓库
- `setItem(key, value)`：存储数据
- `getItem(key)`：读取数据
- `deleteItem(key)`：删除数据
- `clearAllData()`：清空所有数据

**代码片段：**
```js
function openDatabase() { ... }
function setItem(key, value) { ... }
function getItem(key) { ... }
function deleteItem(key) { ... }
function clearAllData() { ... }
```

### 2. 图片缓存主逻辑（`src/utils/cacheImages.js`）

- `cacheImagesAsBase64(backgroundImages)`：批量缓存图片
  - 遍历图片列表，优先查缓存
  - 未缓存则下载、转 Base64、存入 IndexedDB
  - 返回所有图片的 Base64 字典

**代码片段：**
```js
export async function cacheImagesAsBase64(backgroundImages) {
  await openDatabase();
  for (const [key, url] of Object.entries(backgroundImages)) {
    const cached = await getItem(key);
    if (cached) { ... }
    // 否则 fetch 下载、转 base64、setItem
  }
  return cachedImages;
}
```

### 3. 图片常量配置（`src/constants/index.js`）

- 统一管理所有需要缓存的图片 key-url 对应关系

**示例：**
```js
export const cacheImages = {
  smallWxBG: 'https://.../smallWxBG.png',
  loginWxBG1: 'https://.../loginWxBG1.png',
  ...
}
```

---

## 五、API 说明

### 1. openDatabase()
- 初始化数据库，返回 Promise
- 必须在首次操作前调用

### 2. setItem(key, value)
- 存储任意可序列化数据
- value 会被 JSON.stringify

### 3. getItem(key)
- 读取数据，自动 JSON.parse
- 若无数据返回 null

### 4. cacheImagesAsBase64(backgroundImages)
- 参数：图片 key-url 对象
- 返回：key-Base64 字典
- 用法：
```js
import { cacheImagesAsBase64 } from '@/utils/cacheImages'
import { cacheImages } from '@/constants'

cacheImagesAsBase64(cacheImages).then(base64Dict => {
  // base64Dict.smallWxBG 可直接用于 <img :src="base64Dict.smallWxBG" />
})
```

---

## 六、使用示例

### 1. 启动时批量缓存（全局预加载）
在 `App.vue` 或入口文件中，应用启动时批量缓存所有图片：
```js
import { cacheImagesAsBase64 } from '@/utils/cacheImages'
import { cacheImages } from '@/constants'

onMounted(() => {
  cacheImagesAsBase64(cacheImages)
})
```

### 2. 组件中异步获取并使用缓存图片
在组件中异步获取图片 Base64，如果没有缓存则回退到原始 URL：
```js
import { getItem } from '@/utils/IndexedDB'
import { cacheImages } from '@/constants'

const smallWxBG = ref('')
onMounted(async () => {
  smallWxBG.value = await getItem('smallWxBG') || cacheImages.smallWxBG
})
```
模板中：
```vue
<img :src="smallWxBG" />
```

### 3. 缓存失效/强制刷新
如需强制刷新某张图片缓存，可先删除再重新缓存：
```js
import { deleteItem } from '@/utils/IndexedDB'
import { cacheImagesAsBase64 } from '@/utils/cacheImages'
import { cacheImages } from '@/constants'

await deleteItem('smallWxBG')
await cacheImagesAsBase64({ smallWxBG: cacheImages.smallWxBG })
```

### 4. 优雅降级（缓存失败时回退到原始图片 URL）
在获取缓存图片时，始终用 `||` 回退到原始 URL，保证图片总能显示：
```js
const imgSrc = await getItem('loginWxBG1') || cacheImages.loginWxBG1
```

### 5. 清空所有图片缓存
如需清空所有缓存图片，可调用：
```js
import { clearAllData } from '@/utils/IndexedDB'

await clearAllData()
```

---

## 七、常见问题与排查

1. **IndexedDB 兼容性**：主流浏览器均支持，极老设备需降级方案
2. **Base64 体积膨胀**：Base64 比原图大约多 30%，建议只缓存小图/背景图
3. **缓存失效/更新**：如需强制刷新，先 deleteItem(key) 或 clearAllData()
4. **异步时序问题**：所有操作需 await openDatabase() 后再用
5. **图片跨域问题**：图片服务器需支持 CORS，否则 fetch 失败

---

## 八、性能分析

- **首次加载**：未缓存时需下载图片并转码，略慢
- **后续加载**：直接从本地读取，极快，离线可用
- **内存占用**：Base64 占用较多空间，IndexedDB 单域名可存 50MB~2GB
- **并发**：支持多图片并发缓存

---

## 九、扩展建议

1. **缓存过期策略**：可为每张图片加时间戳，定期清理过期图片
2. **分版本管理**：图片资源有版本号，升级时自动刷新缓存
3. **支持其它类型资源**：如音频、JSON 等
4. **UI 友好提示**：缓存失败时 fallback 到原图 URL
5. **图片压缩**：可先压缩再转 Base64，节省空间
6. **缓存进度反馈**：大批量缓存时可加进度条

---

## 十、完整流程图

```mermaid
graph TD
A[页面启动] --> B{已缓存？}
B -- 是 --> C[直接读取 Base64]
B -- 否 --> D[fetch 下载图片]
D --> E[转为 Base64]
E --> F[存入 IndexedDB]
F --> C
C --> G[渲染 <img>]
```

---

## 十一、参考资料

- [MDN IndexedDB](https://developer.mozilla.org/zh-CN/docs/Web/API/IndexedDB_API)
- [MDN FileReader](https://developer.mozilla.org/zh-CN/docs/Web/API/FileReader)
- [Base64 图片原理](https://developer.mozilla.org/zh-CN/docs/Glossary/Base64)

---

> 本文档适用于 Tesla KTV 项目及所有需要前端图片本地缓存的场景，欢迎团队成员补充完善。 