"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Play, Pause, Power, ChevronUp, ChevronDown, List, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface Drama {
  id: string
  title: string
  videoUrl: string
  episodes: Episode[]
}

interface Episode {
  id: string
  title: string
  videoUrl: string
  isPremium: boolean
}

const mockDramas: Drama[] = [
  {
    id: "1",
    title: "霸道总裁爱上我",
    videoUrl: "/placeholder.svg?height=640&width=360",
    episodes: Array.from({ length: 35 }, (_, i) => ({
      id: `1-${i + 1}`,
      title: `第${i + 1}集`,
      videoUrl: "/placeholder.svg?height=640&width=360",
      isPremium: i >= 10, // 第11集开始为付费内容
    })),
  },
  {
    id: "2",
    title: "重生之商业帝国",
    videoUrl: "/placeholder.svg?height=640&width=360",
    episodes: Array.from({ length: 28 }, (_, i) => ({
      id: `2-${i + 1}`,
      title: `第${i + 1}集`,
      videoUrl: "/placeholder.svg?height=640&width=360",
      isPremium: i >= 8,
    })),
  },
]

export default function ShortDramaPlayer() {
  const [currentDramaIndex, setCurrentDramaIndex] = useState(0)
  const [currentEpisodeIndex, setCurrentEpisodeIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [showControls, setShowControls] = useState(true)
  const [progress, setProgress] = useState(0)
  const [showEpisodeList, setShowEpisodeList] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const [showVipModal, setShowVipModal] = useState(false)
  const [inSeriesMode, setInSeriesMode] = useState(false)
  const [recentDramas, setRecentDramas] = useState([
    { id: "1", title: "第4集", thumbnail: "/placeholder.svg?height=80&width=60" },
    { id: "2", title: "第7集", thumbnail: "/placeholder.svg?height=80&width=60" },
    { id: "3", title: "第34集", thumbnail: "/placeholder.svg?height=80&width=60" },
    { id: "4", title: "第78集", thumbnail: "/placeholder.svg?height=80&width=60" },
  ])

  const videoRef = useRef<HTMLVideoElement>(null)
  const controlsTimeoutRef = useRef<NodeJS.Timeout>()

  const currentDrama = mockDramas[currentDramaIndex]
  const currentEpisode = currentDrama.episodes[currentEpisodeIndex]

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.volume = 0.3 // 自动调节至30%
    }
  }, [])

  useEffect(() => {
    if (isPlaying && showControls) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 5000)
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [isPlaying, showControls])

  const togglePlayPause = () => {
    if (currentEpisode.isPremium && !inSeriesMode) {
      setShowVipModal(true)
      return
    }

    setIsPlaying(!isPlaying)
    setShowControls(true)

    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
    }
  }

  const handleVideoClick = () => {
    togglePlayPause()
  }

  const handlePreviousDrama = () => {
    if (currentDramaIndex > 0) {
      setCurrentDramaIndex(currentDramaIndex - 1)
      setCurrentEpisodeIndex(0)
    }
  }

  const handleNextDrama = () => {
    if (currentDramaIndex < mockDramas.length - 1) {
      setCurrentDramaIndex(currentDramaIndex + 1)
      setCurrentEpisodeIndex(0)
    }
  }

  const handleSwipeUp = () => {
    handleNextDrama()
  }

  const handleSwipeDown = () => {
    handlePreviousDrama()
  }

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const progress = (videoRef.current.currentTime / videoRef.current.duration) * 100
      setProgress(progress)
    }
  }

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (videoRef.current) {
      const rect = e.currentTarget.getBoundingClientRect()
      const clickX = e.clientX - rect.left
      const width = rect.width
      const clickProgress = (clickX / width) * 100
      const newTime = (clickProgress / 100) * videoRef.current.duration
      videoRef.current.currentTime = newTime
      setProgress(clickProgress)
    }
  }

  return (
    <div className="relative h-screen w-full bg-black overflow-hidden">
      {/* Video Player */}
      <div className="absolute inset-0 flex items-center justify-center cursor-pointer" onClick={handleVideoClick}>
        <div className="relative w-full max-w-sm h-full max-h-[640px] bg-gray-900 rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            src={currentEpisode.videoUrl}
            onTimeUpdate={handleTimeUpdate}
            autoPlay
            loop
            muted
            playsInline
          />

          {/* Play/Pause Button Overlay */}
          {(!isPlaying || showControls) && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Button
                variant="ghost"
                size="lg"
                className="bg-black/50 hover:bg-black/70 text-white rounded-full p-4"
                onClick={(e) => {
                  e.stopPropagation()
                  togglePlayPause()
                }}
              >
                {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
              </Button>
            </div>
          )}

          {/* Progress Bar */}
          <div
            className="absolute bottom-0 left-0 right-0 h-1 bg-white/30 cursor-pointer"
            onClick={handleProgressClick}
          >
            <div className="h-full bg-white transition-all duration-300" style={{ width: `${progress}%` }} />
          </div>
        </div>
      </div>

      {/* Left Controls */}
      <div className="absolute left-4 top-1/2 -translate-y-1/2 flex flex-col gap-3">
        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={() => window.history.back()}
        >
          <Power className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={togglePlayPause}
        >
          {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={handlePreviousDrama}
          disabled={currentDramaIndex === 0}
        >
          <ChevronUp className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={handleNextDrama}
          disabled={currentDramaIndex === mockDramas.length - 1}
        >
          <ChevronDown className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={() => {
            setInSeriesMode(!inSeriesMode)
            setShowEpisodeList(true)
          }}
        >
          <List className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={() => setShowProfile(true)}
        >
          <User className="w-5 h-5" />
        </Button>
      </div>

      {/* Right Controls (Mirror) */}
      <div className="absolute right-4 top-1/2 -translate-y-1/2 flex flex-col gap-3">
        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={() => window.history.back()}
        >
          <Power className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={togglePlayPause}
        >
          {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={handlePreviousDrama}
          disabled={currentDramaIndex === 0}
        >
          <ChevronUp className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={handleNextDrama}
          disabled={currentDramaIndex === mockDramas.length - 1}
        >
          <ChevronDown className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={() => {
            setInSeriesMode(!inSeriesMode)
            setShowEpisodeList(true)
          }}
        >
          <List className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
          onClick={() => setShowProfile(true)}
        >
          <User className="w-5 h-5" />
        </Button>
      </div>

      {/* Drama Title Overlay */}
      <div className="absolute top-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-4 py-2 rounded-lg">
        <h1 className="text-lg font-bold">{currentDrama.title}</h1>
        <p className="text-sm opacity-80">{currentEpisode.title}</p>
      </div>

      {/* Episode List Modal */}
      <Dialog open={showEpisodeList} onOpenChange={setShowEpisodeList}>
        <DialogContent className="bg-gray-800 text-white border-gray-600 max-w-md">
          <DialogHeader className="pb-2">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-orange-400 text-lg">选集</DialogTitle>
              <button onClick={() => setShowEpisodeList(false)} className="text-gray-400 hover:text-white">
                ✕
              </button>
            </div>
          </DialogHeader>
          <div className="grid grid-cols-5 gap-2 max-h-80 overflow-y-auto p-2">
            {currentDrama.episodes.map((episode, index) => (
              <Button
                key={episode.id}
                variant={index === currentEpisodeIndex ? "default" : "outline"}
                className={`h-10 text-sm ${
                  index === currentEpisodeIndex
                    ? "bg-orange-500 hover:bg-orange-600 text-white"
                    : "bg-gray-700 hover:bg-gray-600 text-white border-gray-600"
                }`}
                onClick={() => {
                  if (episode.isPremium && !inSeriesMode) {
                    setShowVipModal(true)
                  } else {
                    setCurrentEpisodeIndex(index)
                    setShowEpisodeList(false)
                  }
                }}
              >
                {index + 1}
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* Profile Modal */}
      <Dialog open={showProfile} onOpenChange={setShowProfile}>
        <DialogContent className="bg-white text-black border-gray-300 max-w-sm">
          <DialogHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600">头像</span>
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">用户名称</span>
                    <button className="text-blue-500">✏️</button>
                  </div>
                  <p className="text-sm text-gray-500">开通短剧会员，全网短剧免费看！</p>
                </div>
              </div>
              <button onClick={() => setShowProfile(false)} className="text-gray-400 hover:text-gray-600">
                ✕
              </button>
            </div>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium mb-2">正在追剧ing</p>
              <div className="flex gap-2">
                {recentDramas.map((drama, index) => (
                  <div key={index} className="text-center">
                    <div className="w-12 h-16 bg-blue-400 rounded mb-1"></div>
                    <p className="text-xs">{drama.title}</p>
                    <p className="text-xs text-gray-500">[xxxxx]</p>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700">全部短剧</Button>
              <Button className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700">整剧</Button>
              <Button className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700">单集</Button>
            </div>

            <div className="flex items-center gap-4 p-3 bg-gray-50 rounded">
              <div className="w-12 h-12 bg-black flex items-center justify-center">
                <div className="grid grid-cols-2 gap-1">
                  <div className="w-1 h-1 bg-white"></div>
                  <div className="w-1 h-1 bg-white"></div>
                  <div className="w-1 h-1 bg-white"></div>
                  <div className="w-1 h-1 bg-white"></div>
                </div>
              </div>
              <div className="flex-1">
                <p className="text-sm">已有XXXXX年主</p>
                <p className="text-sm">开通畅看全部短剧活动！</p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* VIP Modal */}
      <Dialog open={showVipModal} onOpenChange={setShowVipModal}>
        <DialogContent className="bg-white text-black border-gray-300 max-w-sm">
          <DialogHeader className="pb-4">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-lg font-bold">VIP无限看，剧荒不迷茫！</DialogTitle>
              <button onClick={() => setShowVipModal(false)} className="text-gray-400 hover:text-gray-600">
                ✕
              </button>
            </div>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex gap-2">
              <Button className="flex-1 bg-orange-500 hover:bg-orange-600 text-white">全部短剧</Button>
              <Button variant="outline" className="flex-1 border-gray-300 text-gray-700 bg-transparent">
                整剧
              </Button>
              <Button variant="outline" className="flex-1 border-gray-300 text-gray-700 bg-transparent">
                单集
              </Button>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm text-gray-600">微信扫码支付</p>
              <div className="flex items-center justify-center gap-4">
                <div className="text-lg font-bold">XXX元</div>
                <div className="w-16 h-16 bg-black flex items-center justify-center">
                  <div className="grid grid-cols-2 gap-1">
                    <div className="w-2 h-2 bg-white"></div>
                    <div className="w-2 h-2 bg-white"></div>
                    <div className="w-2 h-2 bg-white"></div>
                    <div className="w-2 h-2 bg-white"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
