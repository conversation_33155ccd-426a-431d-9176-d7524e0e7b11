## 短剧合作预研

## 原始需求文档


具体需求
1. 入口
[短剧首页](./imgs/0-短剧入口.png)
  1. 用户点击“xx专属剧场“进入二级页面
2. 剧场
[短剧首页](./imgs/1-短剧首页.png)
  1. 推荐逻辑（服务端处理）
    1. 因当前为MVP版本，按照短剧热度高-底、每部短剧剧集高-底进行推荐
    2. 后续有一定数据后按照不同车主的观看习惯进行推荐（例如小米：武侠，逆袭、理想：剧情、科幻、爱情等）
  2. 交互逻辑
    1. 进入二级页面后即刻开播放，自动调节音量30%
    2. 左、右镜像模式方便主驾副驾用户操作
      1. 基本操作：上、下一篇、播/暂
        1. 用户点击下一篇按照推荐逻辑滑动至下一篇视频（若已无下一篇视频只此icon至灰）
        2. 用户点击下一篇按照推荐逻辑滑动至下一篇视频（若已无上一篇视频只此icon至灰）
        3. 播放or暂停
    3. 用户点击进入“进入剧集”
      1. 进入此短剧的剧集中，此时上、下一篇为此短剧的上、下一集，置灰逻辑保持一致
      2. 弹出集数显示，正在播放集数高亮处理
      3. 用户点击追剧则此剧加入至“我的”-“正在追ing”
      4. 若触发付费逻辑则弹出付费弹窗
      5. 点击“退出剧集”即可退出

[短剧首页](./imgs/2-短剧-显示剧集.png)
[短剧首页](./imgs/3-短剧-显示vip付费弹窗.png)

    4. 用户点击“我的
      1. 弹出“我的页面”如下图所示
        1. 若用户购买全部短剧则付费信息不展示，均展示“正在追剧ing”
        2. 若用户购买“整剧”“单集”则页面信息如图所示，引导用户购买“全部短剧”
[短剧首页](./imgs/4-短剧-显示个人中心.png)

- 短剧的基本信息：封面、剧集、简介、类型

- 付费逻辑
1. 默认每部剧的前10集都是免费，后面需要收费；
2. 三种付费方式：
  1. 按一部剧的单集购买收费；
  2. 按照整部剧购买收费；
  3. 购买所有短剧收费；