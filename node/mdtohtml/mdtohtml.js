#!/usr/bin/env node

/**
 * Markdown转HTML工具
 * 此脚本用于将Markdown文件转换为HTML，并可以将生成的HTML内容替换到Vue文件中
 */

const fs = require('fs');
const path = require('path');
const MarkdownIt = require('markdown-it');

// 创建markdown-it实例
const md = new MarkdownIt({
  html: true,        // 允许HTML标签
  breaks: true,      // 转换\n为<br>
  linkify: true,     // 自动将URL转为链接
  typographer: true  // 启用一些语言中立的替换和引号美化
});

/**
 * 将Markdown文件转换为HTML
 * @param {string} mdFilePath - Markdown文件路径
 * @returns {string} 转换后的HTML内容
 */
function convertMdToHtml(mdFilePath) {
  try {
    // 读取Markdown文件内容
    const mdContent = fs.readFileSync(mdFilePath, 'utf8');
    
    // 将Markdown转换为HTML
    const htmlContent = md.render(mdContent);
    
    return htmlContent;
  } catch (error) {
    console.error(`转换Markdown文件时出错: ${error.message}`);
    process.exit(1);
  }
}

/**
 * 将HTML内容格式化为Vue组件中的格式
 * @param {string} htmlContent - HTML内容
 * @returns {string} 格式化后的HTML内容，适合Vue组件使用
 */
function formatHtmlForVue(htmlContent) {
  // 将HTML分割为段落
  const paragraphs = htmlContent
    .replace(/<p>/g, '\n      <p>')
    .replace(/<\/p>/g, '</p>')
    .split('\n')
    .filter(line => line.trim() !== '');
  
  // 为第一个和第二个段落添加text-indent-2类
  if (paragraphs.length >= 1) {
    paragraphs[0] = paragraphs[0].replace('<p>', '<p class="text-indent-2">');
  }
  if (paragraphs.length >= 2) {
    paragraphs[1] = paragraphs[1].replace('<p>', '<p class="text-indent-2">');
  }
  
  return paragraphs.join('\n');
}

/**
 * 更新Vue文件中的HTML内容
 * @param {string} vueFilePath - Vue文件路径
 * @param {string} htmlContent - 要插入的HTML内容
 */
function updateVueFile(vueFilePath, htmlContent) {
  try {
    // 读取Vue文件内容
    const vueContent = fs.readFileSync(vueFilePath, 'utf8');
    
    // 查找<div class="agreement-content scroll-area">和</div>之间的内容
    const startTag = '<div class="agreement-content scroll-area">';
    const endTag = '</div>';
    
    const startIndex = vueContent.indexOf(startTag) + startTag.length;
    let endIndex = vueContent.indexOf(endTag, startIndex);
    
    // 如果找不到标签，则退出
    if (startIndex === -1 || endIndex === -1) {
      console.error('无法在Vue文件中找到目标区域');
      process.exit(1);
    }
    
    // 构建新的Vue文件内容
    const newVueContent = 
      vueContent.substring(0, startIndex) + 
      '\n' + htmlContent + '\n    ' + 
      vueContent.substring(endIndex);
    
    // 写入新的Vue文件内容
    fs.writeFileSync(vueFilePath, newVueContent, 'utf8');
    
    console.log(`成功更新Vue文件: ${vueFilePath}`);
  } catch (error) {
    console.error(`更新Vue文件时出错: ${error.message}`);
    process.exit(1);
  }
}

/**
 * 主函数
 */
function main() {
  // 检查命令行参数
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log('用法: node mdtohtml.js <markdown文件路径> <vue文件路径>');
    process.exit(1);
  }
  
  const mdFilePath = args[0];
  const vueFilePath = args[1];
  
  // 检查文件是否存在
  if (!fs.existsSync(mdFilePath)) {
    console.error(`Markdown文件不存在: ${mdFilePath}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(vueFilePath)) {
    console.error(`Vue文件不存在: ${vueFilePath}`);
    process.exit(1);
  }
  
  // 转换Markdown为HTML
  const htmlContent = convertMdToHtml(mdFilePath);
  
  // 格式化HTML以适应Vue组件
  const formattedHtml = formatHtmlForVue(htmlContent);
  
  // 更新Vue文件
  updateVueFile(vueFilePath, formattedHtml);
}

// 执行主函数
main();