# Markdown 转 HTML 工具

## 简介

这是一个用于将 Markdown 文件转换为 HTML 并更新 Vue 组件内容的 Node.js 工具。该工具特别适用于需要将文档内容（如隐私政策、用户协议等）从 Markdown 格式转换并集成到 Vue 应用中的场景。

## 功能特点

- 将 Markdown 文件转换为 HTML 格式
- 自动格式化 HTML 以适应 Vue 组件结构
- 智能添加 CSS 类（如为前两个段落添加`text-indent-2`类）
- 自动定位并更新 Vue 文件中的指定区域内容
- 保持原有的 HTML 结构和样式

## 安装

### 前提条件

- Node.js (v12.0.0 或更高版本)
- npm (v6.0.0 或更高版本)

### 安装步骤

1. 克隆或下载本工具到您的项目中
2. 进入工具目录并安装依赖：

```bash
cd /path/to/mdtohtml
npm install
```

## 使用方法

### 基本用法

```bash
node mdtohtml.js <Markdown文件路径> <Vue文件路径>
```

### 参数说明

- `<Markdown文件路径>`: 要转换的 Markdown 文件的路径
- `<Vue文件路径>`: 需要更新内容的 Vue 组件文件路径

### 示例

```bash
# 将privacy.md转换并更新到privacy_v1.vue文件中
node mdtohtml.js privacy.md ../../src/pages/agreement/privacy_v1.vue

# 将terms.md转换并更新到terms_v1.vue文件中
node mdtohtml.js terms.md ../../src/pages/agreement/terms_v1.vue
```

## Markdown 文件格式要求

工具支持标准的 Markdown 语法，包括但不限于：

- 标题（#、##、###等）
- 段落（空行分隔）
- 强调（_斜体_、**粗体**）
- 列表（有序和无序）
- 链接和图片
- 引用（> 开头）

### 示例 Markdown 内容

```markdown
# 隐私政策标题

【特别提示】请您在使用雷石 KTV 前，仔细阅读（未成年人请在监护人陪同下阅读）并了解本政策（特别是字体加粗的内容），您应重点阅读，在确认充分理解并同意后再开始使用。

## 一、我们如何收集和使用您的个人信息

（一）我们将通过以下途径收集和获得您的个人信息：

1、您提供的信息。包括：

（1）您在注册雷石 KTV 帐号或使用雷石 KTV 服务时，向我们提供的信息；

（2）您使用雷石 KTV 服务时所存储的信息。
```

## Vue 文件要求

目标 Vue 文件必须包含以下 HTML 结构，工具将在这个区域内替换内容：

```html
<div class="agreement-content scroll-area">
  <!-- 这里的内容将被替换 -->
</div>
```

## 工作原理

1. 读取指定的 Markdown 文件内容
2. 使用 markdown-it 库将 Markdown 转换为 HTML
3. 格式化 HTML 以适应 Vue 组件结构（添加适当的缩进和 CSS 类）
4. 在 Vue 文件中定位`<div class="agreement-content scroll-area">`和`</div>`之间的内容
5. 用格式化后的 HTML 替换该区域内容
6. 保存更新后的 Vue 文件

## 转换规则

工具在转换过程中会应用以下规则：

1. 所有 Markdown 内容将被转换为对应的 HTML 标签
2. 段落会被转换为`<p>`标签
3. 前两个段落会自动添加`class="text-indent-2"`样式
4. 所有段落会进行适当的缩进处理，以保持 Vue 文件的格式一致性

## 注意事项

- 确保 Vue 文件中存在`<div class="agreement-content scroll-area">`标签，否则工具将无法定位要替换的内容区域
- 工具会自动为前两个段落添加`text-indent-2`类，以保持一致的样式
- 如果转换或更新过程中出现错误，工具会输出详细的错误信息并退出
- 建议在运行工具前备份目标 Vue 文件，以防意外情况发生

## 自定义

如需自定义 HTML 格式化逻辑，可以修改`formatHtmlForVue`函数：

```javascript
function formatHtmlForVue(htmlContent) {
  // 自定义格式化逻辑
  // ...
  return formattedHtml;
}
```

如需修改目标区域的定位标签，可以修改`updateVueFile`函数中的以下部分：

```javascript
// 查找<div class="agreement-content scroll-area">和</div>之间的内容
const startTag = '<div class="agreement-content scroll-area">';
const endTag = "</div>";
```

## 故障排除

### 常见问题

1. **找不到目标区域**：确保 Vue 文件中包含`<div class="agreement-content scroll-area">`标签
2. **依赖安装失败**：尝试使用`npm install --force`强制安装依赖
3. **转换结果不符合预期**：检查 Markdown 文件格式是否正确，可能存在不兼容的 Markdown 语法
4. **路径错误**：确保提供的文件路径正确，可以使用相对路径或绝对路径

### 错误信息解读

- `无法在Vue文件中找到目标区域`：检查 Vue 文件中是否包含正确的目标区域标签
- `Markdown文件不存在`：检查 Markdown 文件路径是否正确
- `Vue文件不存在`：检查 Vue 文件路径是否正确
- `转换Markdown文件时出错`：检查 Markdown 文件内容格式是否正确

## 许可证

本工具使用 MIT 许可证。
