<!DOCTYPE html>
<html lang="" aria-hidden="true">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <link rel="preload" as="image" href="https://qncweb.ktvsky.com/20240222/other/832cf5435e0716312f21e1e2a0206dc5.png" />
    <link rel="preload" as="image" href="https://qncweb.ktvsky.com/20231219/other/8ca99b075df0166fdac8a96c0c41fb43.png" />
    
    <script>
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?93a959fbd42b63538a86b19a9c2141cf";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();
    </script>
    <style type="text/css">
      html,
      body {
        padding: 0;
        margin: 0;
      }
    </style>
  </head>
  <body>
    <script>
      window.__bl = {
        config: {
          pid: "cgbd80rba0@5a2df2979b45fe0",
          appType: "web",
          imgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
          sendResource: true,
          enableLinkTrace: true,
          behavior: true,
          useFmp: true,
          enableSPA: true,
          release: "<%= VUE_APP_BL_RELEASE %>",
          environment: "<%= VUE_APP_ENVIRONMENT %>",
        },
      };

      // Function to cache images as base64
      function cacheImagesAsBase64(imageUrls) {
        imageUrls.forEach(url => {
          fetch(url)
            .then(response => response.blob())
            .then(blob => {
              const reader = new FileReader();
              reader.onloadend = () => {
                localStorage.setItem(url, reader.result);
              };
              reader.readAsDataURL(blob);
            });
        });
      }

      // Preload and cache images as base64
      const imagesToCache = [
        "https://qncweb.ktvsky.com/20240222/other/832cf5435e0716312f21e1e2a0206dc5.png",
        "https://qncweb.ktvsky.com/20231219/other/8ca99b075df0166fdac8a96c0c41fb43.png"
      ];
      cacheImagesAsBase64(imagesToCache);
    </script>
    <script
      type="text/javascript"
      src="https://retcode.alicdn.com/retcode/bl.js"
      crossorigin
    ></script>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <img src="https://qncweb.ktvsky.com/20231212/vadd/81cfbbbf4c7c514648c15f07f34a75b1.webp" style="position:fixed;bottom: -10000px;left: -10000px;" />
    <!-- built files will be auto injected -->
    <!-- 启动loading -->
    <!-- <img id="app-load" src="https://qncweb.ktvsky.com/20211217/vadd/0c639d301d8cb12ef9058fef77643c61.png" alt=""> -->
    <script>
      window.addEventListener("unhandledrejection", function (event) {
        console.log("ThunderStone App unhandledrejection", event, event.reason);
        event.preventDefault();
      });
      window.onerror = function (message, source, lineno, colno, error) {
        console.log("ThunderStone App onerror:", message, source, lineno, colno, error);
        return true;
      };
    </script>
  </body>
</html>