{"name": "tesla-ktv", "version": "0.1.0", "private": true, "scripts": {"sub": "cd src/subMoudle && git pull origin master", "build": "vue-cli-service build", "stage": "vue-cli-service build --mode stage", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve", "postinstall": "patch-package"}, "dependencies": {"axios": "^0.24.0", "axios-retry": "^3.2.4", "babel-plugin-import": "^1.13.3", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "css-init": "^1.1.9", "cssnano": "5", "date-fns": "^2.26.0", "events": "^3.3.0", "lodash": "^4.17.21", "lottie-web": "^5.13.0", "nanoid": "^3.1.30", "nats.ws": "^1.29.2", "qrcode": "^1.5.0", "query-string": "^7.0.1", "register-service-worker": "^1.7.1", "store2": "^2.12.0", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.11", "ua-parser-js": "^1.0.2", "vant": "3", "vue": "^3.0.0", "vue-loading-overlay": "^4.0", "vue-router": "^4.0.0-0", "vue-scrollto": "^2.20.0", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-marquee": "^4.2.2", "vuex": "^4.0.0-0", "watermark-package": "^2.1.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-pwa": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "eruda": "^3.4.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.0.0", "git-rev-sync": "^3.0.2", "patch-package": "^8.0.0", "postcss-px-to-viewport": "^1.1.0", "prettier": "^2.2.1", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "vue-loader-v16": "^16.0.0-beta.5.4", "vue3-carousel": "^0.15.1", "vue3-touch-events": "^5.0.13", "worker-plugin": "^5.0.1"}, "resolutions": {"minimatch": "^5.1.6"}}