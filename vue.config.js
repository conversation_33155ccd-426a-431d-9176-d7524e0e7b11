const WorkerPlugin = require('worker-plugin');
const pxtoviewport = require('postcss-px-to-viewport');
const git = require('git-rev-sync');

const ReportEnvironmentMap = {
  production: 'prod',
  stage: 'daily',
  development: 'local'
};

function getReportEnvironment() {
  return ReportEnvironmentMap[process.env.NODE_ENV] || 'local';
}

module.exports = {
  css: {
    loaderOptions: {
      postcss: {
        plugins: [
          pxtoviewport({
            viewportWidth: 1200,
            mediaQuery: true,
            landscape: true,
            landscapeWidth: 1920,
          }),
        ],
      },
    },
  },
  pwa: {
    workboxOptions: {
      importScripts: ['https://g.alicdn.com/kg/workbox/4.3.1/workbox-sw.js'],
    },
  },
  configureWebpack: {
    plugins: [new WorkerPlugin()],
  },
  chainWebpack: (config) => {
    // 配置 svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(/src\/assets\/icons/) // 排除 icons 目录
      .end();

    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(/src\/assets\/icons/) // 只处理 icons 目录下的 SVG 文件
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]', // 配置 symbolId 格式
      })
      .end();

    config.plugin('define').tap((options) => {
      options[0].VUE_APP_BL_RELEASE = JSON.stringify(git.short());
      options[0].VUE_APP_ENVIRONMENT = JSON.stringify(getReportEnvironment());
      return options;
    });
  },
};