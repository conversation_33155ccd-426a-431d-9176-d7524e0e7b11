module.exports = {
  root: true,
  env: {
    node: true,
  },
  globals: {
    __bl: true
  },
  extends: ["plugin:vue/vue3-essential", "eslint:recommended"],
  parserOptions: {
    parser: "babel-eslint",
  },
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "semi": "off", // 语句末尾不加分号
    "quotes": ["warn", "single"], // 默认使用单引号
    'no-else-return': "warn", // if 内有 return 不允许结束后再跟 else
    'no-unused-vars': 'warn'
  },
};
